<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SealType 多选测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>SealType 多选功能测试</h1>
    
    <div class="test-section">
        <h2>1. 数组转逗号分隔字符串测试</h2>
        <div id="array-to-string-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 逗号分隔字符串转数组测试</h2>
        <div id="string-to-array-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 字典格式化测试</h2>
        <div id="formatter-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. StatusTag 组件测试</h2>
        <div id="status-tag-result" class="result"></div>
    </div>

    <script>
        // 模拟我们的函数
        function getSealTypeArray(sealType) {
            return sealType ? sealType.split(',').filter(Boolean) : [];
        }
        
        function setSealTypeArray(value) {
            return value.length > 0 ? value.join(',') : undefined;
        }
        
        // 模拟字典格式化函数
        function formatter(dictValue, code) {
            // 模拟字典数据
            const mockDict = {
                'SEAL_TYPE': {
                    '1': '公章',
                    '2': '财务章',
                    '3': '合同章',
                    '4': '法人章'
                }
            };
            
            if (!dictValue) return dictValue;
            
            // 处理逗号分隔的字符串（多选值）
            if (typeof dictValue === 'string' && dictValue.includes(',')) {
                const values = dictValue.split(',').filter(Boolean);
                const labels = values.map(val => 
                    mockDict[code]?.[val] || val
                );
                return labels.join(', ');
            }
            
            return mockDict[code]?.[dictValue] || dictValue;
        }
        
        // 测试用例
        function runTests() {
            // 测试1: 数组转字符串
            const testArrays = [
                ['1', '2', '3'],
                ['1'],
                [],
                ['2', '4']
            ];
            
            let arrayToStringResults = [];
            testArrays.forEach(arr => {
                const result = setSealTypeArray(arr);
                arrayToStringResults.push(`${JSON.stringify(arr)} → "${result}"`);
            });
            
            document.getElementById('array-to-string-result').innerHTML = 
                arrayToStringResults.join('<br>');
            
            // 测试2: 字符串转数组
            const testStrings = [
                '1,2,3',
                '1',
                '',
                undefined,
                '2,4',
                '1,2,3,4'
            ];
            
            let stringToArrayResults = [];
            testStrings.forEach(str => {
                const result = getSealTypeArray(str);
                stringToArrayResults.push(`"${str}" → ${JSON.stringify(result)}`);
            });
            
            document.getElementById('string-to-array-result').innerHTML = 
                stringToArrayResults.join('<br>');
            
            // 测试3: 字典格式化
            const testFormatterValues = [
                '1',
                '1,2',
                '1,2,3',
                '2,4',
                '',
                undefined
            ];
            
            let formatterResults = [];
            testFormatterValues.forEach(val => {
                const result = formatter(val, 'SEAL_TYPE');
                formatterResults.push(`"${val}" → "${result}"`);
            });
            
            document.getElementById('formatter-result').innerHTML = 
                formatterResults.join('<br>');
            
            // 测试4: StatusTag 模拟
            document.getElementById('status-tag-result').innerHTML = `
                <div>模拟 StatusTag 组件处理逗号分隔值:</div>
                <div>• "1,2,3" 应该显示为: ${formatter('1,2,3', 'SEAL_TYPE')}</div>
                <div>• "2,4" 应该显示为: ${formatter('2,4', 'SEAL_TYPE')}</div>
                <div>• "1" 应该显示为: ${formatter('1', 'SEAL_TYPE')}</div>
            `;
        }
        
        // 运行测试
        runTests();
    </script>
</body>
</html>
