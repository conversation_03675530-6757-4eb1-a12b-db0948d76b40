<script lang="ts" setup>
import type { MessageInfo } from '@vben/types';

// 1. 引入 h 函数和新的图标
import { computed, h, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { useSse } from '@vben/base-ui';
import { AuthenticationLoginExpiredModal, NoticeModal, useVbenModal } from '@vben/common-ui';
import { useWatermark } from '@vben/hooks';
import { BasicLayout, LockScreen, Notification, UserDropdown } from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useDictStore, useUserStore } from '@vben/stores';

// 2. 引入你需要的图标
import {
  BellOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  WarningOutlined,
} from '@ant-design/icons-vue';
import { notification as AntdNotification, TypographyLink } from 'ant-design-vue';

import {
  allReadMessageApi,
  batchReadMessageApi,
  getMyMessagePageListApi,
  getMyNoticeDetailApi,
  getPopupNoticeApi,
} from '#/api';
import { useAuthStore } from '#/store';
import LoginForm from '#/views/_core/authentication/login.vue';

const dictStore = useDictStore();

const [api, contextHolder] = AntdNotification.useNotification();
const router = useRouter();
const notifications = ref<MessageInfo[]>([]);

const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const sseUrl = `${import.meta.env.VITE_GLOB_API_URL}/upms/sse?token=${accessStore.accessToken}`;
const { messages, connect } = useSse(sseUrl);
connect();
watch(
  () => messages.value.length,
  (newLength, oldLength = 0) => {
    if (newLength > oldLength) {
      const latestMessage = messages.value[0];
      if (!latestMessage) return;
      switch (latestMessage.type) {
        case 'login_msg_event': {
          const isWarning = latestMessage.data.title.includes('其他设备');

          // 使用 h() 函数创建更丰富的 description 内容
          const descriptionNode = h('div', { class: 'login-info-details' }, [
            h('div', { class: 'info-item' }, [
              h('span', { class: 'info-label' }, '登录时间：'),
              h('span', { class: 'info-value' }, latestMessage.data.date ?? 'N/A'),
            ]),
            h('div', { class: 'info-item' }, [
              h('span', { class: 'info-label' }, '登录地点：'),
              h('span', { class: 'info-value' }, latestMessage.data.address ?? 'N/A'),
            ]),
            h('div', { class: 'info-item' }, [
              h('span', { class: 'info-label' }, '登录 IP：'),
              h('span', { class: 'info-value' }, latestMessage.data.ip ?? 'N/A'),
            ]),
            // 如果是警告信息，可以添加额外的提示
            isWarning && h('div', { class: 'info-warning-tip' }, '如果此活动非您本人操作，请及时修改密码。'),
          ]);

          api.open({
            message: latestMessage.data.title,
            description: descriptionNode, // 传入 VNode
            duration: 10,
            placement: 'bottomRight',
            class: 'custom-login-notification', // 添加自定义类名，方便CSS控制
            // 根据消息类型动态设置图标和颜色
            icon: () =>
              isWarning
                ? h(WarningOutlined, { style: 'color: #faad14' })
                : h(InfoCircleOutlined, { style: 'color: #1677ff' }),
          });
          break;
        }
        case 'msg_event': {
          api.open({
            message: '消息通知',
            class: 'whitespace-pre-line',
            description: latestMessage.data.title,
            placement: 'topRight',
            icon: () => h(BellOutlined, { style: 'color: #108ee9' }),
          });
          getMyUnreadMessage();
          break;
        }
        case 'password_update_event': {
          const descriptionNode = h('div', { class: 'notification-details' }, [
            h('div', { class: 'info-item' }, [
              h('span', { class: 'info-label' }, '剩余天数：'),
              // 突出显示剩余天数
              h(
                'span',
                { class: 'info-value', style: 'color: #faad14; font-weight: 600;' },
                `${latestMessage.data.daysLeft} 天`,
              ),
            ]),
            h('div', { class: 'info-item' }, [
              h('span', { class: 'info-label' }, '过期日期：'),
              h('span', { class: 'info-value' }, latestMessage.data.expiryDate ?? 'N/A'),
            ]),
            h('div', { class: 'info-warning-tip' }, '为了您的账户安全，请及时更新您的密码。'),
          ]);

          api.open({
            message: latestMessage.data.title,
            description: descriptionNode,
            duration: 0, // 永不自动关闭，确保用户看到
            placement: 'bottomRight',
            class: 'custom-notification', // 使用通用类名
            icon: () => h(WarningOutlined, { style: 'color: #faad14' }),
          });
          break;
        }
        default: {
          break;
        }
      }
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
const { destroyWatermark, updateWatermark } = useWatermark();
const showDot = computed(() => notifications.value.length > 0);

const menus = computed(() => [
  {
    icon: 'iconamoon:profile',
    text: '个人中心',
    handler: () => {
      router.push({ name: 'Profile' });
    },
  },
]);

const avatar = computed(() => {
  return userStore.userInfo?.headIcon ?? preferences.app.defaultAvatar;
});

async function handleLogout() {
  await authStore.logout(false);
}

async function getMyUnreadMessage() {
  const res = await getMyMessagePageListApi({ readFlag: 'UNREAD', current: 1, size: 10 });
  notifications.value = res.records ?? [];
}
getMyUnreadMessage();
async function handleNoticeClear() {
  await allReadMessageApi();
  await getMyUnreadMessage();
}

async function handleMakeAll() {
  await allReadMessageApi();
  await getMyUnreadMessage();
}
function handleViewAll() {
  router.push({ name: 'MyNotice' });
}
const messageDetail = ref<MessageInfo>({});
const [Modal, modalApi] = useVbenModal({
  footer: false,
  centered: true,
});
const readMessage = async (message: MessageInfo) => {
  messageDetail.value = message;
  modalApi.setState({ title: messageDetail.value.title }).open();
  await batchReadMessageApi([message.id!]);
  await getMyUnreadMessage();
};
watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userStore.userInfo?.realName}@${userStore.userInfo?.userName}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <BasicLayout @clear-preferences-and-logout="handleLogout">
    <template #user-dropdown>
      <UserDropdown
        :avatar
        :menus
        :text="userStore.userInfo?.realName"
        :description="userStore.userInfo?.phone || '未绑定手机'"
        @logout="handleLogout"
      />
    </template>
    <template #notification>
      <Notification
        :dot="showDot"
        :notifications="notifications"
        @clear="handleNoticeClear"
        @make-all="handleMakeAll"
        @view-all="handleViewAll"
        @read="readMessage"
      />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal v-model:open="accessStore.loginExpired" :avatar>
        <LoginForm />
      </AuthenticationLoginExpiredModal>
      <NoticeModal
        :get-notice-list="getPopupNoticeApi"
        :get-notice-detail="getMyNoticeDetailApi"
        :notice-type-list="dictStore.getDictList('NOTICE_TYPE')"
      />
      <contextHolder />
      <Modal class="w-[800px]">
        <div class="flex h-full flex-col justify-between p-2">
          <div class="mt-4 min-h-[80px] flex-1">
            {{ messageDetail.content }}
          </div>
          <div class="mt-4 flex items-center justify-between">
            <div class="flex items-center text-sm text-gray-400">
              <ClockCircleOutlined class="mr-1.5" />
              <span>发送时间：{{ messageDetail.createTime }}</span>
            </div>
            <TypographyLink v-if="messageDetail.urlPath" :href="messageDetail.urlPath" target="_blank" title="打开链接">
              <span class="flex items-center">
                <LinkOutlined class="mr-1" />
                <span>查看详情</span>
              </span>
            </TypographyLink>
          </div>
        </div>
      </Modal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>
</template>

<style>
/* 对自定义的登录通知框进行样式美化 */
.custom-login-notification .ant-notification-notice-description {
  /* 移除默认的 margin-top，让我们的自定义内容更紧凑 */
  margin-top: 8px;
}

.notification-details {
  font-size: 14px;
  line-height: 1.6;
}

.login-info-details {
  font-size: 14px;
  line-height: 1.6;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px; /* 增加行间距 */
}

.info-label {
  flex-shrink: 0; /* 防止标签被压缩 */
  margin-right: 8px;
  color: rgb(0 0 0 / 65%); /* 标签颜色稍浅 */
}

.info-value {
  font-weight: 500;
  color: rgb(0 0 0 / 88%); /* 内容颜色 */
}

.info-warning-tip {
  padding-top: 8px;
  margin-top: 12px;
  font-size: 13px;
  color: #faad14; /* 警告文字颜色 */
  border-top: 1px solid #f0f0f0;
}
</style>
