import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * CompanyRoleConfigRequest，企业角色配置表
 */
export interface BusinessRoleInfo {
  // 创建人
  createBy?: number;
  // 创建时间
  createTime?: Date;
  // 逻辑删除
  deleteFlag?: number;
  // 主键
  id?: number;
  // 是否准入
  isAccess?: number;
  // 是否审批
  isReview?: number;
  // 审批编码
  reviewCode?: string;
  // 角色编码
  roleCode?: string;
  // 角色名称
  roleName?: string;
  // 更新人
  updateBy?: number;
  // 更新时间
  updateTime?: Date;
  [property: string]: any;
}

export async function getBusinessRolePageListApi(params: PageListParams) {
  return requestClient.get('/base/company/role/config/page', { params });
}
export async function addBusinessRoleApi(data: BusinessRoleInfo) {
  return requestClient.post('/base/company/role/config/add', data);
}
export async function editBusinessRoleApi(data: BusinessRoleInfo) {
  return requestClient.post('/base/company/role/config/edit', data);
}
export async function deleteBusinessRoleApi(id: number) {
  return requestClient.post('/base/company/role/config/delete', {}, { params: { id } });
}
export async function getBusinessRoleListApi() {
  return requestClient.get('/base/company/role/config/list');
}
