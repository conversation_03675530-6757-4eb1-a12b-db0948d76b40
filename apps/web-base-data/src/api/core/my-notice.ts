import type { MessageInfo, NoticeInfo, PageListParams, Pagination } from '@vben/types';

import { requestClient } from '#/api/request';

export function getPopupNoticeApi() {
  return requestClient.get<number>('/upms/notice/unRead/popup');
}
export function getMyNoticeDetailApi(params: { id: number }) {
  return requestClient.get<NoticeInfo>('/upms/notice/info', { params });
}
export function getMyNoticePageListApi(params: PageListParams) {
  return requestClient.get<Pagination<NoticeInfo>>('/upms/notice/page', { params });
}
export function getUnReadNoticeCountApi() {
  return requestClient.get<number>('/upms/notice/unRead/count');
}
export function getMyMessagePageListApi(params: PageListParams & { readFlag: 'READ' | 'UNREAD' }) {
  return requestClient.get<Pagination<MessageInfo>>('/upms/message/page', { params });
}
export function getMyMessageDetailApi(params: { id: number }) {
  return requestClient.get<MessageInfo>('/upms/message/info', { params });
}
export function getUnreadMessageCountApi() {
  return requestClient.get<number>('/upms/message/unRead/count');
}
export function allReadMessageApi() {
  return requestClient.post('/upms/message/fullRead');
}
export function batchReadMessageApi(data: number[]) {
  return requestClient.post('/upms/message/read', data);
}
export function batchDeleteMessageApi(data: number[]) {
  return requestClient.post('/upms/message/delete', data);
}
