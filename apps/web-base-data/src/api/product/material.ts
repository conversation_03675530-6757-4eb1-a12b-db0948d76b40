import type { RequestClientConfig } from '@vben/request';
import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 物料
 */
export interface MaterialInfo {
  // 主键
  id?: number;
  // 创建人ID
  createBy?: number;
  // 创建时间 (时间戳)
  createTime?: number;
  // 更新人ID
  updateBy?: number;
  // 更新时间 (时间戳)
  updateTime?: number;
  // 逻辑删除标志 (0-未删除, 1-已删除)
  deleteFlag?: number;
  // 版本号（乐观锁）
  version?: number;
  // 产品编码
  productCode?: string;
  // 产品名称
  productName?: string;
  // 产品别名
  productAlias?: string;
  // 规格型号 (如: 1.1*1250*C)
  specifications?: string;
  // 分类ID
  categoryId?: number;
  // 主要单位（重量单位）
  measureUnit?: string;
  // 牌号/等级 (如: Q235B, DC01)
  brandName?: string;
  // 产地 (如: 河北安丰, 本钢)
  originName?: string;
  // 状态: 0-禁用, 1-启用
  status?: number;
  // 备注
  remarks?: string;
}
export async function getMaterialPageListApi(params: PageListParams) {
  return requestClient.get('/base/product/material/page', { params });
}
export async function addMaterialApi(data: MaterialInfo) {
  return requestClient.post('/base/product/material/add', data);
}
export async function editMaterialApi(data: MaterialInfo) {
  return requestClient.post('/base/product/material/update', data);
}
export async function deleteMaterialApi(data: string[]) {
  return requestClient.post('/base/product/material/delete', data);
}
export async function enableMaterialApi(data: number[]) {
  return requestClient.post('/base/product/material/enable', data);
}
export async function disableMaterialApi(data: number[]) {
  return requestClient.post('/base/product/material/disable', data);
}
export async function importMaterialApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/base/product/material/import', data, config);
}
export async function downloadMaterialTemplateApi() {
  return requestClient.downloadAndSave('/base/product/material/import/template');
}
export async function exportMaterialApi() {
  return requestClient.downloadAndSave('/base/product/material/export');
}
