import type { PageListParams } from '@vben/types';

import type { SkuRequest } from '#/api/product/sku';

import { requestClient } from '#/api/request';

/**
 * SpuSaveRequest，SPU
 */
export interface SpuInfo {
  // 品牌名称
  brandName?: string;
  // 商品分类id
  categoryId: null | number;
  // 主键
  id?: number;
  // 商品图片（逗号分隔,）
  imgUrls?: string;
  // 商品主图
  mainImgUrl?: string;
  // 生产厂商
  manufacturerName?: string;
  // 计量单位
  measureUnit?: string;
  // 商品SKU列表
  newSkuList?: SkuRequest[];
  // 商品描述
  productDescription?: string;
  // 备注
  remarks?: string;
  // 商品SKU列表
  skuList?: SkuRequest[];
  // 规格型号
  specification?: string;
  // 商品编码
  spuCode: string;
  // 商品名称
  spuName: string;
  // 商品规格列表
  spuSpecList?: SpuSpecRequest[];
  // 供应商Id
  supplierId?: string;
  // 版本号
  version?: number;
  [property: string]: any;
}

/**
 * SpuSpecRequest，SPU规格属性
 */
export interface SpuSpecRequest {
  // 规格名
  specName?: string;
  // 规格值列表
  specValueList?: string[];
  [property: string]: any;
}

export async function getSpuPageListApi(params: PageListParams) {
  return requestClient.get('/base/product/spu/page', { params });
}
export async function addSpuApi(data: SpuInfo) {
  return requestClient.post('/base/product/spu/add', data);
}
export async function editSpuApi(data: SpuInfo) {
  return requestClient.post('/base/product/spu/update', data);
}
export async function deleteSpuApi(data: string[]) {
  return requestClient.post('/base/product/spu/delete', data);
}
export async function getSpuInfoApi(params: { id: number }) {
  return requestClient.get('/base/product/spu/info', { params });
}
