import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * CompanyRequest
 */
export interface MonitorCompanyInfo {
  // 企业编码
  companyCode?: string;
  // 企业名称
  companyName?: string;
  // 消耗额度
  consumeVol?: number;
  // 统一社会信用代码
  creditNo?: string;
  // 公司 id
  eid?: string;
  // 公司名称
  ename?: string;
  // 主键
  id?: number;
  // 是否开启企业监控0未开,1开启
  isMonitor?: number;
  // 是否开启企业舆情监控0未开,1开启
  isNewsMonitor?: number;
  // 最后开启时间
  lastStartTime?: Date;
  // 开启时间
  startTime?: Date;
  // 租户主键
  tenantId?: string;
  [property: string]: any;
}

export function getMonitorCompanyPageListApi(params: PageListParams) {
  return requestClient.get('/infra/monitor/companyPage', { params });
}
export function addMonitorCompanyApi(data: MonitorCompanyInfo) {
  return requestClient.post('/infra/monitor/addCompany', data);
}
export function editMonitorCompanyApi(data: MonitorCompanyInfo) {
  return requestClient.post('/infra/monitor/updateCompany', data);
}
export function deleteMonitorCompanyApi(data: MonitorCompanyInfo) {
  return requestClient.post('/infra/monitor/deleteCompany', data);
}
