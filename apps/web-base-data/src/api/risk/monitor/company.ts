import type { PageListParams, Pagination } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * CompanyMonitorVO
 */
export interface CompanyMonitorVO {
  // 推送字段，字段内容-更新后
  changeAfter?: { [key: string]: any };
  // 推送字段，字段内容-更新前
  changeBefore?: { [key: string]: any };
  // 主表更新日期
  changeDate?: Date;
  // 变更字段
  changeFields?: string;
  // 推送字段，主键
  changePrimaryContent?: string;
  // 监控表
  changeTableName?: string;
  // 变更类型（0 新增，1 更新，2 删除）
  changeType?: string;
  // 统一社会信用代码
  creditNo?: string;
  // 公司 id
  eid?: string;
  // 情感倾向（中立 负面 正面）
  emotion?: string;
  // 主键
  id?: number;
  // 大事标签（0 正常 1 大事）
  majorEvent?: number;
  // 公司名称
  name?: string;
  // 风险等级（0 利好 1 提示 2 关注 3 警惕 4 风险）
  riskClass?: number;
  // 唯一标识
  rowKey?: string;
  // 租户主键
  tenantId?: string;
  [property: string]: any;
}

/**
 * CompanyNewsMonitorVO
 */
export interface CompanyNewsMonitorVO {
  // 发布时间
  date?: Date;
  // 新闻舆情
  disType?: string;
  // 公司唯一 ID
  eid?: string;
  // 主键
  id?: number;
  // 内容摘要
  keywordDesc?: string;
  // 公司名称
  name?: string;
  // 文章 id
  oid?: string;
  // 舆情情感分类
  sentiment?: string;
  // 负面指数,0-1 的 3 位小数，数值越大表示负
  sentimentScore?: string;
  // 本条记录的序列号
  sign?: string;
  // 新闻来源
  source?: string;
  // 舆情标签分类
  tags?: string;
  // 租户主键
  tenantId?: string;
  // 新闻标题
  title?: string;
  // 地址
  url?: string;
  [property: string]: any;
}

export function getMonitorInfoPageListApi(params: PageListParams) {
  return requestClient.get<Pagination<CompanyMonitorVO>>('/infra/monitor/monitorPage', { params });
}
export function getMonitorNewsPageListApi(params: PageListParams) {
  return requestClient.get<Pagination<CompanyNewsMonitorVO>>('/infra/monitor/newsMonitorPage', { params });
}
