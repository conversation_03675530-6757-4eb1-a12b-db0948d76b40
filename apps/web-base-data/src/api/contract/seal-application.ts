import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface SealPageParams extends BaseDataParams {
  /** 当前页 */
  current?: string;
  /** 每页的数量 */
  size?: string;
  /** 正排序规则 */
  ascs?: string;
  /** 倒排序规则 */
  descs?: string;
  /** 查询开始日期 */
  beginDate?: string;
  /** 查询结束日期 */
  endDate?: string;
  /** 附件列表 */
  attachmentList?: string;
  /** 流程key */
  processDefinitionKey?: string;
  /** 流程摘要 */
  summary?: string;
  /** 申请单编号 */
  applyCode?: string;
  /** 经办人ID[人员选择器] */
  userIds?: string[];
  /** 经办人姓名[冗余字段] */
  userName?: string;
  /** 申请部门ID[部门选择器] */
  organId?: string;
  /** 申请部门名称[冗余字段] */
  organName?: string;
  /** 用印日期 */
  usageDate?: string;
  /** 状态 */
  status?: string;
  /** 审批状态 */
  approvalStatus?: string;
  /** 用印事别 */
  description?: string;
  /** 所属项目ID[项目下拉列表] */
  projectId?: string;
  /** 所属项目名称 */
  projectName?: string;
  /** 所属项目编号 */
  projectCode?: string;
  /** 印章种类 [字典表] */
  sealType?: string;
  /** 文件名称 */
  documentName?: string;
  /** 文件类型[字典表] */
  documentType?: string;
  /** 用印份数 */
  copies?: string;
  /** 版本号 (用于乐观锁) */
  version?: string;
  /** 主键 */
  id?: number;
}

export interface SealBaseInfo extends BaseDataParams {
  /** 主键 */
  id?: number;
  /** 申请单编号 */
  applyCode?: string;
  /** 经办人ID[人员选择器] */
  userId?: number;
  userIds: number[] | string[];
  /** 经办人姓名[冗余字段] */
  userName?: string;
  /** 申请部门ID[部门选择器] */
  organId?: number;
  /** 申请部门名称[冗余字段] */
  organName?: string;
  /** 用印日期 */
  usageDate?: string;
  /** 状态 */
  status?: string;
  /** 审批状态 */
  approvalStatus?: string;
  /** 用印事别 */
  description?: string;
  /** 所属项目ID[项目下拉列表] */
  projectId?: number;
  /** 所属项目名称 */
  projectName?: string;
  /** 所属项目编号 */
  projectCode?: string;
  /** 印章种类 [字典表] */
  sealType?: string;
  sealTypeList?: string[];
  /** 文件名称 */
  documentName?: string;
  /** 文件类型[字典表] */
  documentType?: string;
  /** 用印份数 */
  copies?: number;
  /** 版本号 (用于乐观锁) */
  version?: number;
  /** 业务附件 */
  attachmentList?: number[];
  /** 流程摘要 */
  summary: { key: string; value: string }[];
}

// 分页查询
export async function sealApplyPageApi(params: PageListParams) {
  return requestClient.get<SealPageParams>('/base/seal/apply/page', { params });
}

// 新增
export async function saveSealApplyApi(data: SealBaseInfo) {
  return requestClient.post<SealBaseInfo>('/base/seal/apply/save', data);
}

// 编辑
export async function updateSealApplyApi(data: SealBaseInfo) {
  return requestClient.post<SealBaseInfo>('/base/seal/apply/update', data);
}

// 详情
export async function detailSealApplyApi(id: number) {
  return requestClient.get(`/base/seal/apply/detail?id=${id}`);
}

// 删除
export async function delSealApplyApi(id: number) {
  return requestClient.post(`/base/seal/apply/delete?id=${id}`);
}
// 获取人员列表
export async function getUserListApi(params?: PageListParams) {
  return requestClient.get('/upms/user/list', { params });
}
