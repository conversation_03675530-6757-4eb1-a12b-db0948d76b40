<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { watch } from 'vue';

import { DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { BaseAttachmentList } from '#/adapter/base-ui';
import CompanyBaseDetail from '#/views/company/components/company-base-detail.vue';
import CompanyLegalPersonDetail from '#/views/company/components/company-legal-person-detail.vue';

const props = defineProps({ companyInfo: { type: Object, default: () => ({}) } });
const baseGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  border: 'inner',
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const bankGridOptions = {
  columns: [
    { field: 'accountName', title: '账户名称', minWidth: '160px' },
    { field: 'account', title: '银行账号', minWidth: '160px' },
    { field: 'bank', title: '开户银行', minWidth: '160px' },
    { field: 'branchNumber', title: '开户行号', minWidth: '160px' },
    { field: 'isDefault', title: '是否默认', minWidth: '160px', formatter: 'formatBoolean' },
  ],
  ...baseGridOptions,
} as VxeTableGridOptions;
const [BankGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
const invoiceGridOptions = {
  columns: [
    { field: 'title', title: '抬头名称', minWidth: '160px' },
    { field: 'taxNumber', title: '纳税人识别号', minWidth: '160px' },
    { field: 'bank', title: '开户银行', minWidth: '160px' },
    { field: 'account', title: '开户银行账号', minWidth: '160px' },
    { field: 'phone', title: '电话', minWidth: '160px' },
    { field: 'address', title: '地址', minWidth: '160px' },
    { field: 'isDefault', title: '是否默认', minWidth: '160px', fixed: 'right', formatter: 'formatBoolean' },
  ],
  ...baseGridOptions,
} as VxeTableGridOptions;
const [InvoiceGrid, invoiceGridApi] = useVbenVxeGrid({
  gridOptions: invoiceGridOptions,
});
const shareholderGridOptions = {
  columns: [
    { field: 'name', title: '股东名称', minWidth: '160px' },
    { field: 'shouldCapital', title: '认缴出资额（万元）', minWidth: '160px' },
    { field: 'stockPercent', title: '股比（%）', minWidth: '160px' },
    { field: 'realCapital', title: '实缴金额（万元）', minWidth: '160px' },
    { field: 'startDate', title: '首次持股日期', minWidth: '160px', formatter: 'formatDate' },
  ],
  ...baseGridOptions,
} as VxeTableGridOptions;
const [ShareholderGrid, shareholderGridApi] = useVbenVxeGrid({
  gridOptions: shareholderGridOptions,
});
const mortgageGridOptions = {
  columns: [
    { field: 'number', title: '登记编号', minWidth: '160px' },
    { field: 'mortgagee', title: '抵押权人', minWidth: '160px' },
    { field: 'debitType', title: '债务类型', minWidth: '160px' },
    { field: 'amount', title: '被担保债权数额', minWidth: '160px' },
    { field: 'department', title: '登记机关', minWidth: '160px' },
    { field: 'startDate', title: '开始日期', minWidth: '160px', formatter: 'formatDate' },
    { field: 'endDate', title: '结束日期', minWidth: '160px', formatter: 'formatDate' },
  ],
  ...baseGridOptions,
} as VxeTableGridOptions;
const [MortgageGrid, mortgageGridApi] = useVbenVxeGrid({
  gridOptions: mortgageGridOptions,
});
watch(
  () => props.companyInfo,
  (val = {}) => {
    bankGridApi.grid.reloadData(val.companyBankList ?? []);
    invoiceGridApi.grid.reloadData(val.companyInvoiceList ?? []);
    shareholderGridApi.grid.reloadData(val.shareholderList ?? []);
    mortgageGridApi.grid.reloadData(val.mortgageList ?? []);
  },
  { deep: true },
);
</script>

<template>
  <div>
    <!-- 基本信息 -->
    <CompanyBaseDetail :company-info="companyInfo" />
    <!-- 法人基本信息 -->
    <CompanyLegalPersonDetail :company-info="companyInfo" />
    <BasicCaption content="企业联系人信息" />
    <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
      <a-descriptions-item label="联系人姓名">
        {{ companyInfo.contactPersonName }}
      </a-descriptions-item>
      <a-descriptions-item label="联系人电话">
        {{ companyInfo.contactPersonPhone }}
      </a-descriptions-item>
    </a-descriptions>
    <BasicCaption content="客户负责人" />
    <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
      <a-descriptions-item label="业务经理">
        {{ companyInfo.manager }}
      </a-descriptions-item>
    </a-descriptions>
    <BasicCaption content="银行账户" />
    <BankGrid />
    <BasicCaption content="开票信息" />
    <InvoiceGrid />
    <BasicCaption content="股东信息" />
    <ShareholderGrid />
    <BasicCaption content="动产抵押" />
    <MortgageGrid />
    <BasicCaption content="业务角色" />
    <a-space class="my-4">
      <a-tag
        v-for="item in companyInfo.roleList"
        :key="item.id"
        :color="item.status === 'EFFECTIVE' ? 'blue' : ''"
        class="text-sm"
      >
        {{ item.name }}
      </a-tag>
    </a-space>
    <BaseAttachmentList :business-id="companyInfo.id" business-type="COMPANY" />
  </div>
</template>

<style></style>
