<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { BrandInfo } from '#/api';

import { ref, unref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addAttributeBrandApi,
  deleteAttributeBrandApi,
  disableAttributeBrandApi,
  enableAttributeBrandApi,
  getAttributeBrandPageList,
  updateAttributeBrandApi,
} from '#/api';

import BrandForm from './components/brand-form.vue';

const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'brandCode',
      label: '品牌编码',
    },
    {
      component: 'Input',
      fieldName: 'brandName',
      label: '品牌名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '品牌状态',
      componentProps: {
        options: dictStore.getDictList('baseEnableType'),
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'brandCode', title: '品牌编码' },
    { field: 'brandName', title: '品牌名称' },
    { field: 'remarks', title: '备注' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { brandCode: string; brandName: string; status: string },
      ) => {
        return await getAttributeBrandPageList({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const brandFormRef = ref();
const modalTitle = ref('新增品牌');
const currentBrand = ref({});

const [Modal, modalApi] = useVbenModal({
  onOpened: async () => {
    brandFormRef.value.init(currentBrand.value);
  },
  onConfirm: async () => {
    currentBrand.value = await brandFormRef.value.submit();
    let api = addAttributeBrandApi;
    if (currentBrand.value.pid) {
      api = updateAttributeBrandApi;
    }
    await api(unref(currentBrand) as BrandInfo);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
});

const addBrand = () => {
  modalTitle.value = '新增品牌';
  currentBrand.value = {};
  modalApi.open();
};

const editBrand = (row: BrandInfo) => {
  modalTitle.value = '编辑品牌';
  currentBrand.value = { ...row };
  modalApi.open();
};
const enableRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.pid);
  AModal.confirm({
    title: '确认启用',
    content: '确认启用此品牌？',
    onOk: async () => {
      await enableAttributeBrandApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const disenableRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.pid);
  AModal.confirm({
    title: '确认禁用',
    content: '确认禁用此品牌？',
    onOk: async () => {
      await disableAttributeBrandApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const delRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.pid);
  AModal.confirm({
    title: '确认删除',
    content: '确认删除此品牌？',
    onOk: async () => {
      await deleteAttributeBrandApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="addBrand">新增</a-button>
        <a-button class="mr-2" type="primary" @click="enableRows">启用</a-button>
        <a-button class="mr-2" danger type="primary" @click="disenableRows">禁用</a-button>
        <a-button class="mr-2" danger type="primary" @click="delRows">删除</a-button>
      </template>
      <template #action="{ row }">
        <a-typography-link @click="editBrand(row)">编辑</a-typography-link>
      </template>
    </Grid>

    <Modal :title="modalTitle">
      <BrandForm ref="brandFormRef" />
    </Modal>
  </Page>
</template>

<style></style>
