<script setup lang="ts">
import type { BrandInfo } from '#/api';

import { ref } from 'vue';

import { defaultsDeep } from 'lodash-es';

const formModel = ref<Partial<BrandInfo>>({
  brandName: '',
  remarks: '',
});

const rules = {
  brandName: [{ required: true, message: '请输入品牌名称' }],
};

const formModelRef = ref();

const init = (data: Partial<BrandInfo>) => {
  formModelRef.value.clearValidate();
  formModel.value = defaultsDeep(data, {});
};

const submit = () => {
  formModelRef.value.validate();
  return formModel.value;
};

defineExpose({ init, submit });
</script>

<template>
  <a-form ref="formModelRef" :model="formModel" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
    <a-form-item label="品牌名称" name="brandName">
      <a-input v-model:value="formModel.brandName" />
    </a-form-item>
    <a-form-item label="备注" name="remarks">
      <a-textarea v-model:value="formModel.remarks" :rows="4" />
    </a-form-item>
  </a-form>
</template>

<style></style>
