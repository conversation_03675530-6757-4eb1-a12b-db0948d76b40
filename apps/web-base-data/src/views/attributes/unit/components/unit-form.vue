<script setup lang="ts">
import type { UnitInfo } from '#/api';

import { ref } from 'vue';

import { defaultsDeep } from 'lodash-es';

const formModel = ref<Partial<UnitInfo>>({
  unitName: '',
  unitCode: '',
  remarks: '',
});

const rules = {
  unitName: [{ required: true, message: '请输入单位名称' }],
  unitCode: [{ required: true, message: '请输入单位编码' }],
};

const formModelRef = ref();

const init = (data: Partial<UnitInfo>) => {
  formModelRef.value.clearValidate();
  formModel.value = defaultsDeep(data, {});
};

const submit = () => {
  formModelRef.value.validate();
  return formModel.value;
};

defineExpose({ init, submit });
</script>

<template>
  <a-form ref="formModelRef" :model="formModel" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
    <a-form-item label="单位编码" name="unitCode">
      <a-input v-model:value="formModel.unitCode" />
    </a-form-item>
    <a-form-item label="单位名称" name="unitName">
      <a-input v-model:value="formModel.unitName" />
    </a-form-item>
    <!--<a-form-item label="备注" name="remarks">-->
    <!--  <a-textarea v-model:value="formModel.remarks" :rows="4" />-->
    <!--</a-form-item>-->
  </a-form>
</template>

<style></style>
