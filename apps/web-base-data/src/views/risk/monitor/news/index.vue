<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CompanyNewsMonitorVO } from '#/api/risk/monitor/company';

import { computed, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { DESCRIPTIONS_PROP } from '@vben/constants';
import { defineFormOptions, formatDate } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getMonitorNewsPageListApi } from '#/api/risk/monitor/company';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '企业名称',
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'name', title: '企业名称' },
    { field: 'title', title: '新闻标题' },
    { field: 'source', title: '新闻来源' },
    { field: 'date', title: '发布时间', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getMonitorNewsPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  showConfirmButton: false,
});
const infoDetail = ref<CompanyNewsMonitorVO>({});

const parsedKeywordDesc = computed(() => {
  if (!infoDetail.value.keywordDesc) return [];

  const text = infoDetail.value.keywordDesc;
  const parts: Array<{ isHighlight: boolean; text: string }> = [];

  const regex = /<em>(.*?)<\/em>/g;
  let lastIndex = 0;
  let match;

  // 使用无限循环和 break 来替代条件赋值
  while (true) {
    match = regex.exec(text);
    if (match === null) {
      break;
    }

    // 添加em标签前的普通文本
    if (match.index > lastIndex) {
      const normalText = text.slice(lastIndex, match.index).replaceAll(/<[^>]*>/g, '');
      if (normalText) {
        parts.push({ text: normalText, isHighlight: false });
      }
    }

    // 添加em标签内的高亮文本
    parts.push({ text: match[1], isHighlight: true });
    lastIndex = regex.lastIndex;
  }

  // 添加最后剩余的普通文本
  if (lastIndex < text.length) {
    const remainingText = text.slice(lastIndex).replaceAll(/<[^>]*>/g, '');
    if (remainingText) {
      parts.push({ text: remainingText, isHighlight: false });
    }
  }

  return parts;
});

const view = (row: CompanyNewsMonitorVO) => {
  infoDetail.value = row;
  modalApi.open();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="view(row)">详情</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <Modal title="舆情详情" class="w-[1000px]">
      <Descriptions v-bind="DESCRIPTIONS_PROP" class="mb-4">
        <DescriptionsItem label="标题" :span="2">{{ infoDetail.title }}</DescriptionsItem>
        <DescriptionsItem label="公司名称">{{ infoDetail.name }}</DescriptionsItem>
        <DescriptionsItem label="发布时间">{{ formatDate(infoDetail.date) }}</DescriptionsItem>
        <DescriptionsItem label="来源">{{ infoDetail.source }}</DescriptionsItem>
        <DescriptionsItem label="类型">{{ infoDetail.disType }}</DescriptionsItem>
        <DescriptionsItem label="舆情链接" :span="2">
          <a :href="infoDetail.url" target="_blank">{{ infoDetail.url }}</a>
        </DescriptionsItem>
        <DescriptionsItem label="舆情内容" :span="2">
          <div class="keyword-content">
            <template v-for="(part, index) in parsedKeywordDesc" :key="index">
              <span v-if="part.isHighlight" class="highlight-text">{{ part.text }}</span>
              <span v-else>{{ part.text }}</span>
            </template>
          </div>
        </DescriptionsItem>
      </Descriptions>
    </Modal>
  </Page>
</template>

<style>
.highlight-text {
  font-weight: bold;
  color: hsl(var(--destructive));
}
</style>
