import type { SchemaItem } from '../index';

import { typeDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name = '劳动仲裁开庭公告';
export const schema: SchemaItem[] = [
  { fieldName: 'role', label: '角色' },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'doc_id', label: '仲裁编号' },
  { fieldName: 'hearing_date', label: '开庭时间' },
  { fieldName: 'hearing_venue', label: '开庭地点' },
  { fieldName: 'title', label: '标题' },
  { fieldName: 'release_time', label: '发布日期' },
  { fieldName: 'case_reason', label: '案由' },
  { fieldName: 'arbitrators', label: '仲裁员' },
  { fieldName: 'clerk', label: '书记员' },
  { fieldName: 'department', label: '发布机构' },
  {
    fieldName: 'related_companies',
    label: '关联企业/人员',
    schema: [
      { fieldName: 'role', label: '角色' },
      { fieldName: 'name', label: '名称' },
      { fieldName: 'type', label: '类型', formatter: formatDictValue(typeDictList) },
    ],
  },
];

export const handleKeys = {
  related_companies: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
