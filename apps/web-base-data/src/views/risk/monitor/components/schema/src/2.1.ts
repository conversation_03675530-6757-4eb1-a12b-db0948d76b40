import type { SchemaItem } from '../index';

export const name: string = '知识产权出质';
export const schema: SchemaItem[] = [
  { fieldName: 'number', label: '注册号' },
  { fieldName: 'pledgor', label: '出质人名称' },
  { fieldName: 'pawnee', label: '质权人名称' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'period', label: '质权登记期限' },
  { fieldName: 'name', label: '名称' },
  { fieldName: 'public_date', label: '公示日期' },
  { fieldName: 'type', label: '出质种类' },
];
