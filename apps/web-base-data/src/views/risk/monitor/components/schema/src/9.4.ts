import type { SchemaItem } from '../index';

export const name: string = '债券基本信息';
export const schema: SchemaItem[] = [
  { fieldName: 'ename', label: '公司名称' },
  { fieldName: 'companyname', label: '公司全称' },
  { fieldName: 'securityname', label: '债券全称' },
  { fieldName: 'securitysname', label: '债券简称' },
  { fieldName: 'securitytype', label: '债券类型' },
  { fieldName: 'securitytypecode', label: '债券类型编码' },
  { fieldName: 'portfoliocode', label: '债券组合代码' },
  { fieldName: 'tcodeqxb', label: '展示债券代码' },
  { fieldName: 'securitycode', label: '债券交易代码' },
  { fieldName: 'issuetype', label: '募集方式' },
  { fieldName: 'parvalue', label: '面值' },
  { fieldName: 'currency', label: '币种' },
  { fieldName: 'couponrate', label: '票面利率' },
  { fieldName: 'bondperiod', label: '债券期限(年)' },
  { fieldName: 'paymentdate', label: '兑付日期' },
  { fieldName: 'mrtydate', label: '到期日期' },
  { fieldName: 'coupontype', label: '计息方式' },
  { fieldName: 'paytype', label: '付息方式' },
  { fieldName: 'ratedes', label: '利率说明', span: 2 },
  { fieldName: 'trademarketcode', label: '交易场所' },
  { fieldName: 'listvolsh', label: '沪市上市规模' },
  { fieldName: 'listvolsz', label: '深市上市规模' },
  { fieldName: 'issuevol', label: '发行规模' },
  { fieldName: 'firstvaluedate', label: '起息日期' },
  { fieldName: 'lastvaluedate', label: '止息日期' },
  { fieldName: 'issuedate', label: '发行日期' },
  { fieldName: 'listdate', label: '上市日期' },
  { fieldName: 'payperyear', label: '年付息次数' },
  { fieldName: 'payday', label: '每年付息日' },
  { fieldName: 'creditrating', label: '信用等级' },
  { fieldName: 'credittypeout', label: '外部信用增级方式' },
  { fieldName: 'credittypein', label: '内部信用增级方式' },
  { fieldName: 'issueprice', label: '发行价格' },
];
