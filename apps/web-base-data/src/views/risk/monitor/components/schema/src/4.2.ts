import type { SchemaItem } from '../index';

export const name: string = '被执行人';
export const schema: SchemaItem[] = [
  { fieldName: 'status', label: '执行状态', isChange: true },
  { fieldName: 'amount', label: '执行标的（元）' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'case_date', label: '立案时间' },
  { fieldName: 'case_number', label: '案号' },
  { fieldName: 'court', label: '执行法院' },
  { fieldName: 'p_eid', label: '个人所在公司eid' },
  { fieldName: 'name', label: '企业名称' },
];
