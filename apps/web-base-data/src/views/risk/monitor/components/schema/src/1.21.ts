import type { SchemaItem } from '../index';

import { companyStatusDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name: string = '经营状态变更';
export const schema: SchemaItem[] = [
  {
    fieldName: 'new_status_code',
    label: '企业状态',
    isChange: true,
    formatter: formatDictValue(companyStatusDictList),
  },
  { fieldName: 'ename', label: '公司名称' },
  { fieldName: 'regist_capi_new', label: '注册资本（万元）' },
  { fieldName: 'currency_unit', label: '货币单位' },
  { fieldName: 'econ_kind', label: '企业类型' },
  { fieldName: 'oper_name', label: '法定代表人' },
  { fieldName: 'scope', label: '经营范围', span: 2 },
  { fieldName: 'term_start', label: '经营期限起始日期' },
  { fieldName: 'term_end', label: '经营期限结束日期' },
  { fieldName: 'belong_org', label: '所属工商局' },
  { fieldName: 'check_date', label: '核准日期' },
  { fieldName: 'name', label: '企业名称' },
];
