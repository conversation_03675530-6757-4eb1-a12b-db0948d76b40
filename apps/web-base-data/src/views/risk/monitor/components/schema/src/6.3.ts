import type { SchemaItem } from '../index';

import { formatDictValue } from '../../utils';

const typeDictList = [
  { label: '作品著作权', value: 'P' },
  { label: '软件著作权', value: 'S' },
];

export const name: string = '著作权/软件著作权';
export const schema: SchemaItem[] = [
  { fieldName: 'name', label: '软件全称', isChange: true },
  { fieldName: 'approval_date', label: '登记批准日期' },
  { fieldName: 'company', label: '著作权人/所属公司' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'first_date', label: '首次发表日期' },
  { fieldName: 'number', label: '登记号' },
  { fieldName: 'short_name', label: '作品简称' },
  { fieldName: 'success_date', label: '创作完成日期' },
  { fieldName: 'type', label: '类型', formatter: formatDictValue(typeDictList) },
  { fieldName: 'type_name', label: '作品类别' },
  { fieldName: 'type_num', label: '分类号' },
  { fieldName: 'version', label: '版本号' },
];
