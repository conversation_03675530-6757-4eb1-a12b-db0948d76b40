<script setup lang="ts">
import type { Key } from 'ant-design-vue/es/_util/type';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ContractClassifyInfo, ContractInfo, ContractTemplateInfo } from '#/api';
import type { CreateContractData } from '#/views/contract/components/create-contract-start.vue';

import { onMounted, ref } from 'vue';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message, TabPane, Tabs } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteContractSignApi,
  getContractAuditPageListApi,
  getContractClassifyListApi,
  getContractSignPageListApi,
  getContractTemplateListApi,
  mergeSubmitContractApi,
} from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import CreateContractStart from '#/views/contract/components/create-contract-start.vue';
import SignAudit from '#/views/contract/sign/sign-audit.vue';
import SignConfirm from '#/views/contract/sign/sign-confirm.vue';
import SignDetail from '#/views/contract/sign/sign-detail.vue';
import SignEdit from '#/views/contract/sign/sign-edit.vue';

const { startWorkflow, WorkflowPreviewModal, initWorkflow } = useWorkflowBase();
const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'contractName',
      label: '合同名称',
    },
    {
      component: 'Input',
      fieldName: 'contractNo',
      label: '合同编码',
    },
    // {
    //   component: 'RangePicker',
    //   fieldName: 'createTime',
    //   label: '发起时间',
    // },
    {
      component: 'Select',
      fieldName: 'sealType',
      label: '用章类型',
      componentProps: {
        options: dictStore.getDictList('SEAL_TYPE'),
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'classifyId',
      label: '合同分类',
      componentProps: {
        api: getContractClassifyListApi,
        labelField: 'categoryName',
        valueField: 'id',
      },
    },
    // {
    //   component: 'Input',
    //   fieldName: 'signatoryOrg',
    //   label: '签约方',
    // },
    {
      component: 'Select',
      fieldName: 'status',
      label: '合同状态',
      componentProps: {
        options: dictStore.getDictList('CONTRACT_STATUS'),
      },
    },
    // {
    //   component: 'Select',
    //   fieldName: 'signMethod',
    //   label: '签约方式',
    // },
  ],
  fieldMappingTime: [['createTime', ['createTimeStart', 'createTimeEnd'], 'x']],
});
const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    checkStrictly: true,
  },
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'contractCode', title: '合同编号' },
    { field: 'contractName', title: '合同名称' },
    { field: 'status', title: '合同状态', formatter: ['formatStatus', 'CONTRACT_STATUS'] },
    // { field: 'signMethod', title: '签约方式' },
    { field: 'sealType', title: '用章类型', cellRender: { name: 'CellStatus', props: { code: 'SEAL_TYPE' } } },
    { field: 'categoryName', title: '合同类型' },
    // { field: 'signatoryNames', title: '签约方' },
    { field: 'createTime', title: '创建时间' },
    // { field: 'initiatedDate', title: '发起签约时间' },
    // { field: 'completedDate', title: '签约完成时间' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { categoryName: string },
      ) => {
        return await getContractSignPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const auditGridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    checkStrictly: true,
  },
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'contractApproveCode', title: '合同审批编号' },
    { field: 'contractApproveName', title: '合同审批名称' },
    { field: 'approvalStatus', title: '审批状态', formatter: ['formatStatus', 'REVIEW_STATUS'] },
    { field: 'createByName', title: '创建人' },
    { field: 'createTime', title: '创建时间' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
      showOverflow: false,
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { categoryName: string },
      ) => {
        return await getContractAuditPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const CreateContractStartRef = ref();
const addContract = () => {
  CreateContractStartRef.value.init();
};
const editContract = (row: ContractClassifyInfo) => {
  openFormPopup(true, row);
};
const delContract = async () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '确认删除此合同？',
    async onOk() {
      await deleteContractSignApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const templateList = ref<ContractTemplateInfo[]>([]);
const getTemplateList = async () => {
  templateList.value = await getContractTemplateListApi();
};
getTemplateList();
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const [registerConfirm, { openPopup: openConfirmPopup }] = usePopup();
const [registerAudit, { openPopup: openAuditPopup }] = usePopup();
const confirmPick = (data: CreateContractData) => {
  openFormPopup(true, data);
};
const editSuccess = () => {
  gridApi.reload();
};
// const revokeContract = () => {
//   const res = gridApi.grid.getCheckboxRecords(true);
//   if (res.length === 0) {
//     message.error('请选择数据');
//     return false;
//   }
//   const id = res[0].id;
//   AntdModal.confirm({
//     title: '确认撤回',
//     content: '确认撤回此合同？',
//     async onOk() {
//       await revokeContractSignApi(id);
//       message.success($t('base.resSuccess'));
//       await gridApi.reload();
//     },
//   });
// };
// const cancelContract = () => {
//   const res = gridApi.grid.getCheckboxRecords(true);
//   if (res.length === 0) {
//     message.error('请选择数据');
//     return false;
//   }
//   const id = res[0].id;
//   AntdModal.confirm({
//     title: '确认作废',
//     content: '确认作废此合同？',
//     async onOk() {
//       await cancelContractSignApi(id);
//       message.success($t('base.resSuccess'));
//       await gridApi.reload();
//     },
//   });
// };
const signContract = (row: ContractInfo) => {
  openConfirmPopup(true, row);
};
const viewContract = (row: ContractInfo) => {
  openDetailPopup(true, row);
};
const viewAuditContract = (row: ContractInfo) => {
  openAuditPopup(true, row);
};
const auditContract = (row: ContractInfo) => {
  openAuditPopup(true, row);
};
const activeKey = ref('info');
const changeTab = async (key: Key) => {
  await (key === 'info' ? gridApi.setGridOptions(gridOptions) : gridApi.setGridOptions(auditGridOptions));
  await gridApi.reload();
};
const mergeSubmit = async () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
  const formData = {
    contractIds: res.map((o: ContractInfo) => o.id as number),
    processDefinitionKey,
    startUserSelectAssignees,
  };
  await mergeSubmitContractApi(formData);
  message.success($t('base.resSuccess'));
  await gridApi.reload();
};
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data = {
        id: params.id,
      };
      editContract(data);
    },

    // 详情弹层
    detail: (params) => {
      const data = {
        id: params.id,
      };
      viewContract(data);
    },

    // 审核弹层
    audit: (params) => {
      const data = {
        id: params.id,
        pageType: 'audit',
      };
      auditContract(data);
    },
  },
});
onMounted(async () => {
  await initWorkflow({ formKey: 'contract_apply', required: true });
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #form-append>
        <Tabs v-model:active-key="activeKey" @change="changeTab">
          <TabPane key="info" tab="合同信息" />
          <TabPane key="audit" tab="审批记录" />
        </Tabs>
      </template>
      <template #toolbar-actions>
        <a-space v-if="activeKey === 'info'">
          <a-button type="primary" @click="addContract">新增</a-button>
          <a-button type="primary" @click="mergeSubmit">合并发起审批</a-button>
          <!--<a-button type="primary" danger @click="revokeContract">撤回</a-button>-->
          <!--<a-button type="primary" danger @click="cancelContract">作废</a-button>-->
          <a-button type="primary" danger @click="delContract">删除</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <template v-if="activeKey === 'info'">
            <a-typography-link v-if="['DRAFTING'].includes(row.status)" @click="editContract(row)">
              编辑
            </a-typography-link>
            <a-typography-link @click="viewContract(row)">详情</a-typography-link>
            <a-typography-link v-if="['5', '6'].includes(row.status)" @click="signContract(row)">
              签署
            </a-typography-link>
          </template>
          <template v-if="activeKey === 'audit'">
            <a-typography-link @click="viewAuditContract(row)">详情</a-typography-link>
          </template>
        </a-space>
      </template>
    </Grid>
    <CreateContractStart
      ref="CreateContractStartRef"
      :get-template-list-api="getContractTemplateListApi"
      :get-config-api="getContractTemplateListApi"
      @confirm-pick="confirmPick"
    />
    <SignEdit @register="registerForm" @ok="editSuccess" />
    <SignDetail @register="registerDetail" />
    <SignConfirm @register="registerConfirm" @ok="editSuccess" />
    <SignAudit @register="registerAudit" @ok="editSuccess" />
    <WorkflowPreviewModal />
  </Page>
</template>

<style></style>
