<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { TaxonomyInfo } from '#/api';

import { computed, ref } from 'vue';

import { DictSelect } from '@vben/base-ui';
import { ApiComponent, useVbenModal } from '@vben/common-ui';
import { BasicTitle } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { Select } from 'ant-design-vue';

import {
  getAttributeBrandList,
  getAttributeManufacturerList,
  getAttributeUnitList,
  getTaxonomyAttrNameListApi,
} from '#/api';
import EditAttrModal from '#/views/taxonomy/manage/components/edit-attr-modal.vue';

interface AttrInfo {
  attrName: string;
  formType: string;
  attrValueJson: string;
}

const [Modal, modalApi] = useVbenModal({
  onOpened: async () => {
    categoryInfoList.value = modalApi.getData();
  },
});
const categoryInfoList = ref<TaxonomyInfo[]>([]);
const dataForm = ref({
  categoryIds: [],
  allCategory: false,
  categoryOperationType: '',
  selectFields: {
    attributeFlag: false,
    baseAttributeFlag: false,
    defaultBrandId: false,
    defaultManufacturerId: false,
    defaultMeasureUnit: false,
  },
  defaultBrandId: '',
  defaultManufacturerId: '',
  defaultMeasureUnit: '',
  attributeList: [],
  chooseList: [],
});
const categoryNameList = computed(() => {
  return categoryInfoList.value.map((item) => item.categoryName);
});
const step = ref(0);
const rules = {
  categoryOperationType: [{ required: true, message: '批量操作类型' }],
};
const dataFormRef = ref();
const next = async () => {
  await dataFormRef.value.validate();
  step.value = step.value + 1;
  if (dataForm.value.categoryOperationType === '2') {
    await getAttrNameList();
  }
};

const [AttrModal, attrModalApi] = useVbenModal({
  connectedComponent: EditAttrModal,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', dragSort: true },
    { field: 'attrName', title: '属性名称' },
    {
      field: 'formType',
      title: '表单方式',
      minWidth: '120px',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'formType',
        },
      },
    },
    { field: 'attrValueJson', title: '属性值' },
    { field: '', title: '操作', slots: { default: 'action' } },
  ],
  data: dataForm.value.attributeList,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const addAttr = () => {
  attrModalApi.setData({ insert: gridApi.grid.insert }).open();
};
const editAttr = (row: AttrInfo) => {
  attrModalApi.setData({ data: row, insert: gridApi.grid.insert }).open();
};
const delAttr = (row: AttrInfo) => {
  gridApi.grid.remove(row);
};
const attrOptions = ref([]);
const getAttrNameList = async () => {
  const ids = categoryInfoList.value.map((item) => item.pid);
  attrOptions.value = await getTaxonomyAttrNameListApi({ categoryIds: ids.join(',') });
};
</script>

<template>
  <Modal title="批量属性设置" class="w-[1200px]" :show-confirm-button="false">
    <a-form ref="dataFormRef" :model="dataForm" :rules="rules">
      <a-form-item label="已选商品分类">
        {{ categoryNameList.join('，') }}
      </a-form-item>
      <template v-if="step === 0">
        <a-form-item label="选择所有分类">
          <a-checkbox v-model:checked="dataForm.allCategory" />
        </a-form-item>
        <a-form-item label="批量操作类型" name="categoryOperationType">
          <DictSelect v-model:value="dataForm.categoryOperationType" code="categoryOperationType" />
        </a-form-item>
      </template>
      <template v-else-if="step === 1">
        <template v-if="dataForm.categoryOperationType === '1'">
          <BasicTitle class="mb-2">
            <a-checkbox v-model:checked="dataForm.selectFields.attributeFlag" class="mr-2" />
            通用属性
          </BasicTitle>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <a-checkbox
                    v-model:checked="dataForm.selectFields.defaultBrandId"
                    :disabled="!dataForm.selectFields.attributeFlag"
                    class="mr-2"
                  />
                  商品品牌
                </template>
                <ApiComponent
                  v-model="dataForm.defaultBrandId"
                  :component="Select"
                  :api="getAttributeBrandList"
                  :disabled="!dataForm.selectFields.attributeFlag"
                  label-field="brandName"
                  value-field="pid"
                  model-prop-name="value"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <a-checkbox
                    v-model:checked="dataForm.selectFields.defaultManufacturerId"
                    :disabled="!dataForm.selectFields.attributeFlag"
                    class="mr-2"
                  />
                  生产厂商
                </template>
                <ApiComponent
                  v-model="dataForm.defaultManufacturerId"
                  :component="Select"
                  :api="getAttributeManufacturerList"
                  :disabled="!dataForm.selectFields.attributeFlag"
                  label-field="manufacturerName"
                  value-field="pid"
                  model-prop-name="value"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <a-checkbox
                    v-model:checked="dataForm.selectFields.defaultMeasureUnit"
                    :disabled="!dataForm.selectFields.attributeFlag"
                    class="mr-2"
                  />
                  计量单位
                </template>
                <ApiComponent
                  v-model="dataForm.defaultMeasureUnit"
                  :component="Select"
                  :api="getAttributeUnitList"
                  :disabled="!dataForm.selectFields.attributeFlag"
                  label-field="unitName"
                  value-field="pid"
                  model-prop-name="value"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <BasicTitle>
            <a-checkbox v-model:checked="dataForm.selectFields.baseAttributeFlag" class="mr-2" />
            基础属性
          </BasicTitle>
          <Grid>
            <template #toolbar-actions>
              <a-button
                :disabled="!dataForm.selectFields.baseAttributeFlag"
                class="mr-2"
                type="primary"
                @click="addAttr"
              >
                新增属性
              </a-button>
            </template>
            <template #action="{ row }">
              <a-space>
                <a-typography-link @click="editAttr(row)">编辑</a-typography-link>
                <a-typography-link type="danger" @click="delAttr(row)">删除</a-typography-link>
              </a-space>
            </template>
          </Grid>
        </template>
        <template v-else-if="dataForm.categoryOperationType === '2'">
          <a-form-item label="要删除的基础属性名称">
            <a-select v-model:value="dataForm.chooseList" mode="multiple" :options="attrOptions" />
          </a-form-item>
        </template>
      </template>
    </a-form>
    <template #append-footer>
      <a-button type="primary" @click="next">下一步</a-button>
    </template>
    <AttrModal />
  </Modal>
</template>

<style></style>
