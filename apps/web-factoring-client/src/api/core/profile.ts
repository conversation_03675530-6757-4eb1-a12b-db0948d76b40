import { requestClient } from '#/api/request';

/**
 * 用户信息
 */
export interface UserInfo {
  // 最后修改密码时间
  changePasswordDate?: Date;
  // 编码
  code?: string;
  // 创建人
  createBy?: number;
  // 创建时间
  createTime?: Date;
  // 描述
  description?: string;
  // 邮箱
  email?: string;
  // 过期日期；NULL=永不过期
  expiryDate?: Date;
  // 首次登录IP
  firstLogIp?: string;
  // 首次登录时间
  firstLogTime?: Date;
  // 性别
  gender?: string;
  // 分组主键
  groupIds?: number[];
  // 分组名称
  groupNames?: string[];
  // 头像
  headIcon?: string;
  // 主键
  id?: number;
  // 是否管理员; 0=非管理员 1=管理员
  isAdmin?: number;
  // 是否主管
  isSupervisor?: number;
  // 最后登录IP
  lastLogIp?: string;
  // 最后登录时间
  lastLogTime?: Date;
  // 登录成功次数
  logSuccessCount?: number;
  // 直属上级主键
  managerId?: number;
  // 组织完整名称
  organFullNames?: string[];
  // 组织主键
  organIds?: number[];
  // 组织名称
  organNames?: string[];
  // 密码
  password?: string;
  // 电话
  phone?: string;
  // 岗位主键
  postIds?: number[];
  // 岗位名称
  postNames?: string[];
  // 前次登录IP
  prevLogIp?: string;
  // 前次登录时间
  prevLogTime?: Date;
  // 扩展属性
  propertyJson?: string;
  // 姓名
  realName?: string;
  // 角色主键
  roleIds?: number[];
  // 角色名称
  roleNames?: string[];
  // 排序
  sortCode?: number;
  // 数据来源
  source?: string;
  // 是否启用; -1=禁用 0=锁定 1=启用
  status?: number;
  // 租户主键
  tenantId?: string;
  // 类型
  type?: string;
  // 更新人
  updateBy?: number;
  // 更新时间
  updateTime?: Date;
  // 账户
  userName?: string;
  [property: string]: any;
}

/**
 * UserSocialInfo，数据
 */
export interface UserSocialInfo {
  // 主键
  id?: number;
  // 最后登录时间
  lastLoginTime?: Date;
  // 扩展信息（JSON 格式）
  metaJson?: string;
  // 第三方平台的用户唯一标识
  openId?: string;
  // 数据来源（如：wechat, dingtalk, alipay 等）
  source: 'wechat_enterprise';
  // 租户主键
  tenantId?: string;
  // 用户主键（关联用户表）
  userId?: number;
  [property: string]: any;
}

export function getUserProfileApi() {
  return requestClient.get<UserInfo>('/upms/user/profile/info');
}
export function editUserProfileApi(data: UserInfo) {
  return requestClient.post('/upms/user/profile/edit', data);
}
export function editUserProfilePasswordApi(params: { newPassword: string; oldPassword: string }) {
  return requestClient.post('/upms/user/profile/update_pwd', {}, { params });
}
export function getUserSocialListApi() {
  return requestClient.get<UserSocialInfo[]>('/upms/user/social/list');
}
export function bindUserSocialApi(data: { code: string; source: string; state: string }) {
  return requestClient.post('/upms/user/social/bind', data);
}
export function unbindUserSocialApi(params: { source: string }) {
  return requestClient.post('/upms/user/social/unbind', {}, { params });
}
