import type { WorkflowType } from '@vben/base-ui';
import type { PageListParams, Pagination } from '@vben/types';

import { requestClient } from '#/api/request';

/** 查询流程定义 */
export async function getProcessDefinitionApi(params: { id?: string; key?: string }) {
  return requestClient.get<WorkflowType.BpmProcessDefinitionApi.ProcessDefinition>('/bpm/bpm/process-definition/get', {
    params,
  });
}
/** 申请人主动取消流程实例 */
export async function cancelProcessInstanceByStartUser(id: number, reason: string) {
  return requestClient.delete<boolean>('/bpm/bpm/process-instance/cancel-by-start-user', {
    data: { id, reason },
  });
}
/** 获取下一个执行的流程节点 */
export async function getNextApprovalNodes(params: any) {
  return requestClient.get<WorkflowType.BpmProcessInstanceApi.ApprovalNodeInfo[]>(
    `/bpm/bpm/process-instance/get-next-approval-nodes`,
    {
      params,
    },
  );
}
/** 获取审批详情 */
export async function getApprovalDetailApi(params: any) {
  return requestClient.get<WorkflowType.BpmProcessInstanceApi.ApprovalDetail>(
    `/bpm/bpm/process-instance/get-approval-detail`,
    {
      params,
    },
  );
}

/** 查询待办任务分页 */
export async function getTaskTodoPage(params: PageListParams) {
  return requestClient.get<Pagination<WorkflowType.BpmTaskApi.Task>>('/bpm/bpm/task/todo-page', {
    params,
  });
}

/** 查询已办任务分页 */
export async function getTaskDonePage(params: PageListParams) {
  return requestClient.get<Pagination<WorkflowType.BpmTaskApi.Task>>('/bpm/bpm/task/done-page', {
    params,
  });
}

/** 查询任务管理分页 */
export async function getTaskManagerPage(params: PageListParams) {
  return requestClient.get<Pagination<WorkflowType.BpmTaskApi.Task>>('/bpm/bpm/task/manager-page', { params });
}

/** 审批任务 */
export const approveTask = async (data: any) => {
  return await requestClient.put('/bpm/bpm/task/approve', data);
};

/** 驳回任务 */
export const rejectTask = async (data: any) => {
  return await requestClient.put('/bpm/bpm/task/reject', data);
};

/** 根据流程实例 ID 查询任务列表 */
export const getTaskListByProcessInstanceId = async (id: string) => {
  return await requestClient.get(`/bpm/bpm/task/list-by-process-instance-id?processInstanceId=${id}`);
};

/** 获取所有可退回的节点 */
export const getTaskListByReturn = async (id: string) => {
  return await requestClient.get(`/bpm/bpm/task/list-by-return?id=${id}`);
};

/** 退回 */
export const returnTask = async (data: any) => {
  return await requestClient.put('/bpm/bpm/task/return', data);
};

// 委派
export const delegateTask = async (data: any) => {
  return await requestClient.put('/bpm/bpm/task/delegate', data);
};

// 转派
export const transferTask = async (data: any) => {
  return await requestClient.put('/bpm/bpm/task/transfer', data);
};

// 加签
export const signCreateTask = async (data: any) => {
  return await requestClient.put('/bpm/bpm/task/create-sign', data);
};

// 减签
export const signDeleteTask = async (data: any) => {
  return await requestClient.delete('/bpm/bpm/task/delete-sign', data);
};

// 抄送
export const copyTask = async (data: any) => {
  return await requestClient.put('/bpm/bpm/task/copy', data);
};

// 获取我的待办任务
export const myTodoTask = async (processInstanceId: string) => {
  return await requestClient.get(`/bpm/bpm/task/my-todo?processInstanceId=${processInstanceId}`);
};

// 获取加签任务列表
export const getChildrenTaskList = async (id: string) => {
  return await requestClient.get(`/bpm/bpm/task/list-by-parent-task-id?parentTaskId=${id}`);
};
export const getProcessDefinitionListByFormKeyApi = async (params: { formKey: string }) => {
  return requestClient.get<WorkflowType.BpmProcessDefinitionApi.ProcessDefinition[]>(
    '/bpm/bpm/process-definition/list',
    { params },
  );
};
export const getWorkflowConfigApi = async (params: { businessKey?: number; formKey: string }) => {
  return requestClient.get('/bpm/bpm/process-instance/get-last-business-process', {
    params,
  });
};
/** 获取流程实例 BPMN 模型视图 */
export async function getProcessInstanceBpmnModelView(id: string) {
  return requestClient.get<WorkflowType.BpmProcessInstanceApi.ProcessInstance>(
    `/bpm/bpm/process-instance/get-bpmn-model-view?id=${id}`,
  );
}
