import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface LimitInfo {
  /**
   * 决策时间
   */
  beginDate?: string;
  /**
   * 地级市
   */
  cityName?: string;
  /**
   * 累计用信额度
   */
  companyAccumulatedUsedAmount?: string;
  /**
   * 可用额度
   */
  companyAvailableAmount?: string;
  /**
   * 企业社会统一信用代码
   */
  companyCode?: string;
  /**
   * 授信额度
   */
  companyCreditAmount?: string;
  /**
   * 企业名称
   */
  companyName?: string;
  /**
   * 已结清额度
   */
  companySettlementAmount?: string;
  /**
   * 存量业务余额
   */
  companyStockBalanceAmount?: string;
  /**
   * 授信期限
   */
  creditTerm?: string;
  /**
   * 授信方式
   */
  creditType?: string;
  /**
   * 区县
   */
  districtName?: string;
  /**
   * 授信到期日
   */
  endDate?: string;
  [property: string]: any;
}

// 获取分页列表
export async function getLimitDetailApi(params: PageListParams) {
  return requestClient.get<LimitInfo[]>('/factoring/client/limit/detail', { params });
}
