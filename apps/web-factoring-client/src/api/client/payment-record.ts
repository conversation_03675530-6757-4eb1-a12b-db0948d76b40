import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface PaymentRecordInfo {
  /**
   * 付款记录编号
   */
  confirmCode?: string;
  /**
   * 确认投放金额
   */
  confirmInvestAmount?: number;
  /**
   * 确认投放日期
   */
  confirmInvestDate?: Date;
  /**
   * 实际付款方式
   */
  confirmPaymentMethod?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 付款单位银行账号
   */
  payerBankAccount?: string;
  /**
   * 付款单位开户行名称
   */
  payerBankBranch?: string;
  /**
   * 付款单位名称
   */
  payerCompanyName?: string;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 备注
   */
  remarks?: string;
  [property: string]: any;
}

// 获取分页列表
export async function getPaymentRecordPageListApi(params: PageListParams) {
  return requestClient.get<PaymentRecordInfo[]>('/factoring/client/payment/page', { params });
}
