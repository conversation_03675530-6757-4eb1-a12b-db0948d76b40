import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface ContractInfo {
  /**
   * 分类名称
   */
  categoryName?: string;
  /**
   * 合同编号
   */
  contractCode?: string;
  /**
   * 合同名称
   */
  contractName?: string;
  /**
   * 上传合同路径（原）
   */
  fileId?: number;
  /**
   * 用章类型
   */
  sealType?: string;
  [property: string]: any;
}

// 获取分页列表
export async function getContractPageListApi(params: PageListParams) {
  return requestClient.get<ContractInfo[]>('/factoring/client/contract/page', { params });
}
