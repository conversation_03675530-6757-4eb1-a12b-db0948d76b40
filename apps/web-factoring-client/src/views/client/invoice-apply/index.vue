<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

import { FilePreviewDialog } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getBusinessFileListApi,
  getDownloadFileLinkApi,
  getInvoiceApplyPageListApi,
  getPreviewFileExternalLink,
} from '#/api';

const dictStore = useDictStore();
// 搜索表单配置
const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'projectName', label: '项目名称' },
    {
      component: 'Select',
      fieldName: 'invoiceItem',
      label: '开票内容',
      componentProps: {
        options: dictStore.getDictList('FCT_INVOICE_ITME'),
      },
    },
    {
      component: 'Select',
      fieldName: 'invoiceType',
      label: '发票类型',
      componentProps: {
        options: dictStore.getDictList('FCT_INVOICE_TYPE'),
      },
    },
  ],
  commonConfig: { labelCol: { span: 6 }, wrapperCol: { span: 18 } },
});

// 表格列配置
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'invoiceType',
      title: '发票类型',
      minWidth: 150,
      cellRender: { name: 'CellStatus', props: { code: 'FCT_INVOICE_TYPE' } },
    },
    { field: 'buyerName', title: '购买方名称', minWidth: 200 },
    { field: 'taxNumber', title: '税号', minWidth: 180 },
    { field: 'applicationDate', title: '开票申请日期', minWidth: 160, formatter: 'formatDate' },
    { field: 'invoiceDate', title: '开票日期', minWidth: 160, formatter: 'formatDate' },
    { field: 'invoiceNumber', title: '发票号码', minWidth: 180 },
    { field: 'invoiceCode', title: '发票代码', minWidth: 180 },
    {
      field: 'invoiceItem',
      title: '开票内容',
      minWidth: 150,
      cellRender: { name: 'CellStatus', props: { code: 'FCT_INVOICE_ITME' } },
    },
    { field: 'amount', title: '不含税金额（元）', minWidth: 150, formatter: 'formatMoney' },
    { field: 'tax', title: '税额（元）', minWidth: 150, formatter: 'formatMoney' },
    { field: 'amountTax', title: '价税合计（元）', minWidth: 150, formatter: 'formatMoney' },
    {
      field: 'invoiceStatus',
      title: '开票状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_INVOICE_OPEN_STATUS' } },
      minWidth: 160,
    },
    { field: 'action', title: '操作', fixed: 'right', width: 120, slots: { default: 'action' } },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) =>
        await getInvoiceApplyPageListApi({ current: page.currentPage, size: page.pageSize, ...formValues }),
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};

const [Grid] = useVbenVxeGrid({ formOptions, gridOptions });
const FilePreviewDialogRef = ref();
const checkFile = async (file: any, type: string) => {
  const fileList = await getBusinessFileListApi({ businessId: file.id, businessType: 'FCT_INVOICE_UPLOAD' });
  if (type === 'Preview') {
    if (fileList[0].extension && previewTypeList.has(fileList[0].extension)) {
      FilePreviewDialogRef.value.init(fileList[0].id);
    } else {
      console.warn('文件不支持预览');
    }
  } else {
    const url = await getDownloadFileLinkApi({ id: fileList[0].id });

    window.open(url);
  }
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="checkFile(row, 'Preview')"> 预览 </a-typography-link>
          <a-typography-link @click="checkFile(row, 'Download')">下载</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <FilePreviewDialog ref="FilePreviewDialogRef" :preview-api="getPreviewFileExternalLink" />
  </Page>
</template>
