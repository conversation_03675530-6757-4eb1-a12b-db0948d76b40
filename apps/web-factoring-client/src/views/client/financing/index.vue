<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getOverviewPageListApi } from '#/api';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'projectType',
      label: '项目类型',
      componentProps: {
        options: dictStore.getDictList('FCT_PROJECT_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'factoringType',
      label: '业务类型',
      componentProps: {
        options: dictStore.getDictList('FCT_FACTORING_TYPE'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
      minWidth: 150,
    },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    { field: 'creditApplyName', title: '用信名称', minWidth: 180 },
    { field: 'creditBeginDate', title: '决策日期', minWidth: 160, formatter: 'formatDate' },
    { field: 'creditTerm', title: '授信期限（月）', minWidth: 140 },
    { field: 'creditApplyApprovalDate', title: '用信审批日期', minWidth: 160, formatter: 'formatDate' },
    { field: 'investAmount', title: '累计投放金额（元）', minWidth: 160, formatter: 'formatMoney' },
    {
      field: 'factoringType',
      title: '业务类型',
      minWidth: 140,
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_FACTORING_TYPE',
        },
      },
    },
    { field: 'creditorName', title: '债权人', minWidth: 160 },
    { field: 'debtorName', title: '债务人', minWidth: 160 },
    { field: 'guarantorName', title: '担保人', minWidth: 160 },
    { field: 'contractTerm', title: '合同期限（月）', minWidth: 140 },
    { field: 'investDate', title: '投放日期', minWidth: 160, formatter: 'formatDate' },
    { field: 'dueDate', title: '结束日期', minWidth: 160, formatter: 'formatDate' },
    { field: 'settlementDate', title: '结清日期', minWidth: 160, formatter: 'formatDate' },
    {
      field: 'survivalStatus',
      title: '存续状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_SURVIVAL_STATUS',
        },
      },
      minWidth: 140,
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getOverviewPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <Grid />
  </Page>
</template>

<style></style>
