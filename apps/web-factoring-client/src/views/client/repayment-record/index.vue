<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRepaymentRecordPageListApi } from '#/api';

import RepaymentRecordDetail from './repayment-record-detail.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'repaymentConfirmCode',
      label: '还款记录编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'repaymentPlanCode',
      label: '还款计划编号',
    },
    {
      component: 'RangePicker',
      fieldName: 'repaymentDate',
      label: '还款日期',
    },
  ],
  fieldMappingTime: [
    ['repaymentDate', ['beginRepaymentDate', 'endRepaymentDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'repaymentConfirmCode', title: '还款记录编号', minWidth: 180 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    { field: 'creditApplyName', title: '关联用信申请', minWidth: 200 },
    { field: 'paymentConfirmCode', title: '关联到款记录', minWidth: 180 },
    { field: 'repaymentPlanCode', title: '还款计划编号', minWidth: 180 },
    { field: 'companyName', title: '还款企业', minWidth: 180 },
    { field: 'repaymentAmount', title: '还款金额(元)', minWidth: 120, formatter: 'formatMoney' },
    { field: 'repaymentDate', title: '还款日期', minWidth: 120, formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getRepaymentRecordPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: RepaymentRecordInfo) => {
  openDetailPopup(true, row);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <RepaymentRecordDetail @register="registerDetail" />
  </Page>
</template>

<style></style>
