<script setup lang="ts">
import type { Key } from 'ant-design-vue/es/_util/type';

import { reactive, ref } from 'vue';

import { formatMoney } from '@vben/utils';

import { InputSearch, Modal, Table } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import {
  getPurchaseOrderItemPageApi,
  getPurchaseReturnOrderItemPageApi,
  getSalesOrderItemPageApi,
  getSalesReturnOrderItemPageApi,
} from '#/api';

const props = withDefaults(
  defineProps<{
    rowKey?: string;
  }>(),
  {
    rowKey: 'id',
  },
);
const typeInfoList = {
  PURCHASE_ORDER: {
    api: getPurchaseOrderItemPageApi,
    columns: [
      { title: '订单名称', dataIndex: 'purchaseOrderName', key: 'purchaseOrderName', width: 200, ellipsis: true },
      { title: '订单编号', dataIndex: 'purchaseOrderCode', key: 'purchaseOrderCode', width: 150, ellipsis: true },
      {
        title: '上游企业名称',
        dataIndex: 'supplierCompanyName',
        key: 'supplierCompanyName',
        width: 200,
        ellipsis: true,
      },
      {
        title: '上游企业社会信用代码',
        dataIndex: 'supplierCompanyCode',
        key: 'supplierCompanyCode',
        width: 200,
        ellipsis: true,
      },
    ],
  },
  SALES_ORDER: {
    api: getSalesOrderItemPageApi,
    columns: [
      { title: '订单名称', dataIndex: 'salesOrderName', key: 'salesOrderName', width: 200, ellipsis: true },
      { title: '订单编号', dataIndex: 'salesOrderCode', key: 'salesOrderCode', width: 200, ellipsis: true },
      {
        title: '下游企业名称',
        dataIndex: 'purchaserCompanyName',
        key: 'purchaserCompanyName',
        width: 200,
        ellipsis: true,
      },
      {
        title: '下游企业社会信用代码',
        dataIndex: 'purchaserCompanyCode',
        key: 'purchaserCompanyCode',
        width: 200,
        ellipsis: true,
      },
    ],
  },
  PURCHASE_RETURN_ORDER: {
    api: getPurchaseReturnOrderItemPageApi,
    columns: [
      {
        title: '订单名称',
        dataIndex: 'purchaseReturnOrderName',
        key: 'purchaseReturnOrderName',
        width: 200,
        ellipsis: true,
      },
      {
        title: '订单编号',
        dataIndex: 'purchaseReturnOrderCode',
        key: 'purchaseReturnOrderCode',
        width: 200,
        ellipsis: true,
      },
      {
        title: '上游企业名称',
        dataIndex: 'supplierCompanyName',
        key: 'supplierCompanyName',
        width: 200,
        ellipsis: true,
      },
      {
        title: '上游企业社会信用代码',
        dataIndex: 'supplierCompanyCode',
        key: 'supplierCompanyCode',
        width: 200,
        ellipsis: true,
      },
    ],
  },
  SALES_RETURN_ORDER: {
    api: getSalesReturnOrderItemPageApi,
    columns: [
      { title: '订单名称', dataIndex: 'salesReturnOrderName', key: 'salesReturnOrderName', width: 200, ellipsis: true },
      { title: '订单编号', dataIndex: 'salesReturnOrderCode', key: 'salesReturnOrderCode', width: 200, ellipsis: true },
      {
        title: '下游企业名称',
        dataIndex: 'purchaserCompanyName',
        key: 'purchaserCompanyName',
        width: 200,
        ellipsis: true,
      },
      {
        title: '下游企业社会信用代码',
        dataIndex: 'purchaserCompanyCode',
        key: 'purchaserCompanyCode',
        width: 200,
        ellipsis: true,
      },
    ],
  },
};
const columns = [
  { title: '商品行号', dataIndex: 'itemNumber', key: 'itemNumber', width: 100 },
  { title: '商品名称', dataIndex: 'productName', key: 'productName', width: 100, ellipsis: true },
  { title: '商品编码', dataIndex: 'productCode', key: 'productCode', width: 100 },
  { title: '商品别名', dataIndex: 'productAlias', key: 'productAlias', width: 100 },
  { title: '规格型号', dataIndex: 'specifications', key: 'specifications', width: 100 },
  { title: '单位', dataIndex: 'measureUnit', key: 'measureUnit', width: 100 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100 },
  {
    title: '含税单价',
    dataIndex: 'priceWithTax',
    key: 'priceWithTax',
    width: 100,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '税率(%)',
    dataIndex: 'taxRate',
    key: 'taxRate',
    width: 100,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '含税金额',
    dataIndex: 'amountWithTax',
    key: 'amountWithTax',
    width: 100,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '不含税金额',
    dataIndex: 'amountWithoutTax',
    key: 'amountWithoutTax',
    width: 150,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '税额',
    dataIndex: 'taxAmount',
    key: 'taxAmount',
    width: 100,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '销售含税单价',
    dataIndex: 'sourcePrice',
    key: 'sourcePrice',
    width: 150,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  { title: '备注', dataIndex: 'remarks', width: 200, key: 'remarks' },
];
const source = ref<any[]>([]);
let globalResolve: any;
let globalReject: any;
const state = reactive<{
  keyword?: string;
  loading: boolean;
  projectId?: number;
  sourceDocumentType: keyof typeof typeInfoList;
  visible: boolean;
}>({
  visible: false,
  sourceDocumentType: 'PURCHASE_ORDER',
  keyword: '',
  loading: false,
});
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});
const selectedRowKeys = ref<Key[]>([]);
const selectedRowList = ref<any[]>([]);
const pick = async (settings: { projectId: number; sourceDocumentType: keyof typeof typeInfoList }) => {
  return new Promise((resolve, reject) => {
    state.sourceDocumentType = settings.sourceDocumentType;
    state.projectId = settings.projectId;
    getList();
    globalResolve = resolve;
    globalReject = reject;
    state.visible = true;
  });
};
const getList = async (current = 1) => {
  const api = typeInfoList[state.sourceDocumentType].api;
  const res = await api({ current, size: pagination.pageSize, keyword: state.keyword, projectId: state.projectId });
  source.value = res.records;
  pagination.total = res.total;
  pagination.current = res.current;
  pagination.pageSize = res.size;
};
const onSelectChange = (keys: Key[], selectedRows: any[]) => {
  selectedRowKeys.value = keys;
  selectedRowList.value = selectedRows;
};
const handleOk = () => {
  state.visible = false;
  if (globalResolve) {
    const list = cloneDeep(selectedRowList.value);
    list.forEach((item: any) => {
      delete item.id;
    });
    globalResolve(list);
  }
  globalResolve = null;
  globalReject = null;
  clear();
};
const clear = () => {
  selectedRowKeys.value = [];
  selectedRowList.value = [];
  source.value = [];
  state.projectId = 0;
  state.keyword = '';
  if (globalReject) {
    globalReject();
  }
  globalResolve = null;
  globalReject = null;
};
const onRowClick = (record: any) => {
  const key = record[props.rowKey];
  const index = selectedRowKeys.value.indexOf(key);
  if (index === -1) {
    selectedRowKeys.value.push(key);
    selectedRowList.value.push(record);
  } else {
    selectedRowKeys.value.splice(index, 1);
    selectedRowList.value.splice(index, 1);
  }
};
const tableChange = (pagination: any) => {
  getList(pagination.current);
};
defineExpose({
  pick,
});
</script>

<template>
  <Modal v-model:open="state.visible" title="商品选择" width="80%" @ok="handleOk" @cancel="clear">
    <InputSearch
      v-model:value="state.keyword"
      placeholder="请输入订单关键字"
      class="mb-4 w-[300px]"
      allow-clear
      @search="getList(1)"
    />
    <Table
      :columns="[...(typeInfoList[state.sourceDocumentType].columns ?? []), ...columns]"
      :data-source="source"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange, fixed: true, preserveSelectedRowKeys: true }"
      :row-key="rowKey"
      table-layout="fixed"
      :scroll="{ x: '100%' }"
      :pagination="pagination"
      :custom-row="(record) => ({ onClick: () => onRowClick(record) })"
      @change="tableChange"
    />
  </Modal>
</template>

<style></style>
