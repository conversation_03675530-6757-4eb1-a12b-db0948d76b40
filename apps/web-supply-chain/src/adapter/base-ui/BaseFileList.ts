import { defineComponent } from 'vue';

import { FileList } from '@vben/base-ui';

import { getDownloadFileLinkApi, getFileInfoListApi, getPreviewFileExternalLink } from '#/api';

export const BaseFileList = defineComponent({
  ...FileList,
  props: {
    ...(FileList as any).props,
    fileInfoApi: {
      type: Function,
      ...(FileList as any).props.fileInfoApi,
      required: false,
      default: getFileInfoListApi,
    },
    previewExternalApi: {
      type: Function,
      ...(FileList as any).props.previewExternalApi,
      required: false,
      default: getPreviewFileExternalLink,
    },
    downloadApi: {
      type: Function,
      ...(FileList as any).props.downloadApi,
      default: getDownloadFileLinkApi,
    },
  },
});
