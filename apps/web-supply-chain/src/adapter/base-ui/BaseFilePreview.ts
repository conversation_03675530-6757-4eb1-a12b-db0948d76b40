import { defineComponent } from 'vue';

import { FilePreview } from '@vben/base-ui';

import { getPreviewFileExternalLink } from '#/api';

export const BaseFilePreview = defineComponent({
  ...FilePreview,
  props: {
    ...(FilePreview as any).props,
    previewApi: {
      type: Function,
      ...(FilePreview as any).props.previewApi,
      required: false,
      default: getPreviewFileExternalLink,
    },
  },
});
