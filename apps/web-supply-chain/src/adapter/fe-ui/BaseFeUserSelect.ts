import { defineComponent } from 'vue';

import { FeUserSelect } from '@vben/fe-ui';

import { getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi } from '#/api';

export const BaseFeUserSelect = defineComponent({
  ...FeUserSelect,
  props: {
    ...(FeUserSelect as any).props,
    api: {
      type: Object,
      ...(FeUserSelect as any).props.api,
      required: false,
      default() {
        return {
          getUserInfoByIdsApi,
          getUserListByKeywordApi,
          getUserTreeListApi,
        };
      },
    },
  },
});
