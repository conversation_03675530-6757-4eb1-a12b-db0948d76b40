import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: '',
      order: -1,
      title: $t('page.risk.title'),
    },
    name: 'Risk',
    path: '/risk',
    children: [
      {
        meta: {
          icon: '',
          order: -1,
          title: $t('page.risk.early-warning.title'),
        },
        name: 'RiskEarlyWarning',
        path: '/risk/early-warning',
        children: [
          {
            name: 'RiskEarlyWarningManage',
            path: '/risk/early-warning/rule',
            component: () => import('#/views/risk/early-warning/rule/index.vue'),
            meta: {
              icon: '',
              title: $t('page.risk.early-warning.rule'),
            },
          },
          {
            name: 'RiskEarlyWarningItem',
            path: '/risk/early-warning/item',
            component: () => import('#/views/risk/early-warning/item/index.vue'),
            meta: {
              icon: '',
              title: $t('page.risk.early-warning.item'),
            },
          },
        ],
      },
    ],
  },
];

export default routes;
