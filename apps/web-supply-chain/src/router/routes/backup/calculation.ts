import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '额度测算',
    },
    name: 'Calculation',
    path: '/calculation',
    children: [
      {
        name: 'CalculationManage',
        path: '/calculation/manage',
        component: () => import('#/views/calculation/manage/index.vue'),
        meta: {
          title: '额度测算管理',
        },
      },
      {
        name: 'CalculationConfig',
        path: '/calculation/config',
        component: () => import('#/views/calculation/config/index.vue'),
        meta: {
          title: '额度测算配置',
        },
      },
    ],
  },
];

export default routes;
