import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    name: 'MyNotice',
    path: '/my-notice',
    component: () => import('#/views/dashboard/notice/index.vue'),
    meta: {
      icon: 'carbon:workspace',
      title: $t('page.dashboard.notice'),
      hideInMenu: true,
    },
  },
  {
    name: 'Profile',
    path: '/profile',
    component: () => import('#/views/_core/profile/index.vue'),
    meta: {
      icon: 'ant-design:profile-outlined',
      title: $t('page.profile.title'),
      hideInMenu: true,
    },
  },
];

export default routes;
