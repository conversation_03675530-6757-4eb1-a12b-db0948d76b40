<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { DeliveryBaseInfo, ProjectBaseInfo, TransOrderBaseInfo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { BaseFeOrganizeSelect, BaseFeUserSelect } from '#/adapter/fe-ui';
import {
  addTransportApi,
  detailTransportApi,
  editTransportApi,
  getCompanyApi,
  projectManageListApi,
  submitTransportApi,
} from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);

const { startWorkflow, WorkflowPreviewModal, initWorkflow } = useWorkflowBase();
const pageType = ref('edit');

const belongProjectOptions = ref<ProjectBaseInfo[]>([]);

const loading = reactive({
  submit: false,
});

// 默认数据
const defaultForm: Partial<TransOrderBaseInfo> = {
  id: undefined,
  docCode: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  entrustedCompanyCode: undefined,
  entrustedCompanyName: undefined,
  entrustedDate: undefined,
  status: undefined,
  approvalStatus: undefined,
  originalFileId: undefined,
  signedFileId: undefined,
  userId: undefined,
  userName: undefined,
  organId: undefined,
  organName: undefined,
  description: undefined,
  version: undefined,
  attachmentList: [],
};

const detailForm = reactive<Partial<TransOrderBaseInfo>>(cloneDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

const rules: Record<string, Rule[]> = {
  entrustedCompanyCode: [{ required: true, message: '请选择受托企业', trigger: 'change' }],
  projectName: [{ required: true, message: '请选择项目名称', trigger: 'change' }],
  entrustedCompanyName: [{ required: true, message: '请选择委托企业', trigger: 'change' }],
  entrustedDate: [{ required: true, message: '请选择委托日期', trigger: 'change' }],
};

const title = computed(() => {
  return pageType.value === 'audit' ? '审核' : detailForm.id ? '编辑' : '新增';
});

const init = async (data: TransOrderBaseInfo) => {
  await getCompanyList();
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_transport_order_doc', businessKey: data.id });
  const res = await projectManageListApi({ projectName: '' });
  belongProjectOptions.value = res
    .filter((item) => item.id !== undefined) // 过滤掉 id 为 undefined 的项
    .map((item) => ({
      projectName: item.projectName,
      id: item.id as number | string, // 类型断言
    }));
  if (data.id) {
    const res = await detailTransportApi(data.id);
    Object.assign(detailForm, cloneDeep(res));
  } else {
    Object.assign(detailForm, cloneDeep(defaultForm));
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};

const formRef = ref();
const save = async (type: 'save' | 'submit') => {
  try {
    await formRef.value.validate();
    changeOkLoading(true);

    // 根据是否存在id判断是新增还是编辑
    let api = detailForm.id ? editTransportApi : addTransportApi;
    if (type === 'submit') {
      api = submitTransportApi;
    }

    // 当 type 为 submit 时，在 submitDeliveryApi 成功后调用 startWorkflow
    if (type === 'submit') {
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      detailForm.processDefinitionKey = processDefinitionKey;
      detailForm.startUserSelectAssignees = startUserSelectAssignees;
    }

    const submitData = detailForm as Required<TransOrderBaseInfo>;
    const res = await api(submitData);

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch {
    message.error('保存失败，请检查网络或输入内容');
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
  close();
};

const handleProjectChange = (value: number | string | undefined, _: any) => {
  if (!value) return;

  belongProjectOptions.value.forEach((item: ProjectBaseInfo) => {
    if (item.id === value) {
      detailForm.projectName = item.projectName;
      detailForm.projectCode = item.id;
    }
  });
};
const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup" @close="close">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')"> 保存 </a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')"> 提交 </a-button>
      </a-space>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="运输指令单编号" name="docCode">
            <Input v-model:value="detailForm.docCode" placeholder="留空自动生成" :disabled="!!detailForm.id" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="受托企业" name="entrustedCompanyCode">
            <Select
              v-model:value="detailForm.entrustedCompanyCode"
              :options="companyOptions"
              :field-names="{ label: 'companyName', value: 'companyCode' }"
              show-search
              :filter-option="
                (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
              "
              :disabled="pageType === 'audit'"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="项目名称" name="projectName">
            <Select
              v-model:value="detailForm.projectName"
              :options="belongProjectOptions"
              show-search
              :field-names="{ label: 'projectName', value: 'id' }"
              :filter-option="(input: string, option: any) => option.projectName.includes(input)"
              @change="handleProjectChange"
              :disabled="pageType === 'audit'"
            />
          </FormItem>
        </Col>

        <!--        <Col v-bind="colSpan">-->
        <!--          <FormItem label="所属项目编号" name="projectCode">-->
        <!--            <span>{{ detailForm.projectCode || '-' }}</span>-->
        <!--          </FormItem>-->
        <!--        </Col>-->

        <Col v-bind="colSpan">
          <FormItem label="委托日期" name="entrustedDate">
            <DatePicker
              v-model:value="detailForm.entrustedDate"
              value-format="YYYY-MM-DD hh:mm:ss"
              format="YYYY-MM-DD"
              :disabled="pageType === 'audit'"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="经办人" name="userId">
            <BaseFeUserSelect
              v-model:value="detailForm.userId"
              :disabled="pageType === 'audit'"
              @change="detailForm.userName = $event.userName"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="经办部门" name="organId">
            <BaseFeOrganizeSelect
              v-model:value="detailForm.organId"
              :disabled="pageType === 'audit'"
              @change="detailForm.organName = $event.organName"
            />
          </FormItem>
        </Col>

        <!-- 备注 -->
        <Col v-bind="colSpan">
          <FormItem label="备注" name="description">
            <Textarea v-model:value="detailForm.description" :rows="3" :disabled="pageType === 'audit'" />
          </FormItem>
        </Col>
      </Row>

      <!--   附件信息   -->
      <BaseAttachmentList
        border="inner"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_TRANSPORT_ORDER_DOC"
        :edit-mode="pageType === 'edit'"
      />
    </Form>
    <WorkflowPreviewModal v-if="pageType === 'edit'" />
  </BasicPopup>
</template>

<style scoped>
:deep(.ant-picker) {
  width: 100%;
}
</style>
