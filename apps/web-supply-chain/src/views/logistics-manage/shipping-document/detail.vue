<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ShipmentBaseInfo } from '#/api';

import { nextTick, reactive, ref, watch } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { detailShipmentApi, getCompanyApi, projectManageListApi } from '#/api';

defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();

interface SelectOption {
  projectName?: string;
  id?: number | string;
  projectCode?: string;
  executorCompanyName?: string;
}

const belongProjectOptions = ref<SelectOption[]>([]);

// 默认数据
const defaultForm: Partial<ShipmentBaseInfo> = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  shipmentCode: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  shipmentType: undefined,
  transportMethod: undefined,
  carrierCompanyCode: undefined,
  carrierCompanyName: undefined,
  consigneeCompanyCode: undefined,
  consigneeCompanyName: undefined,
  billingCompanyCode: undefined,
  billingCompanyName: undefined,
  shipmentDate: undefined,
  status: undefined,
  totalShipmentCost: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  plannedDeliveryDate: undefined,
  receiptDistrict: undefined,
  receiptDetailAddress: undefined,
  receiptProvince: undefined,
  receiptCity: undefined,
  remarks: undefined,
  shipmentDeliveryList: [],
  shipmentItemList: [],
  shipmentSourceRelList: [],
  attachmentList: [],
  documentType: undefined,
};
let detailForm = reactive<Partial<ShipmentBaseInfo>>(cloneDeep(defaultForm));

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

const title = '详情';

const init = async (data: any) => {
  const res = await projectManageListApi({ projectName: '' });
  belongProjectOptions.value = res.map((item) => ({
    projectName: item.projectName,
    id: item.id,
  }));
  await getCompanyList();
  if (data.id) {
    const res = await detailShipmentApi(data.id);
    Object.assign(detailForm, res);

    // 强制刷新表格数据
    setTimeout(() => {
      gridApi.grid.loadData(detailForm.shipmentDeliveryList || []);
      gridApiGoods.grid.loadData(detailForm.shipmentItemList || []);
    }, 0);
  } else {
    detailForm = cloneDeep(defaultForm);
    // 清空表格数据
    setTimeout(() => {
      gridApi.grid.loadData([]);
      gridApiGoods.grid.loadData([]);
    }, 0);
  }
};

const [registerPopup] = usePopupInner(init);

const grid: VxeTableGridOptions = {
  ...DETAIL_GRID_OPTIONS,
  data: detailForm.shipmentDeliveryList,
  columns: [
    {
      field: 'deliveryType',
      title: '运输类型',
      minWidth: '150px',
      cellRender: {
        name: 'CellStatus',
        props: { code: 'TRANSPORT_VEHICLE' },
      },
    },
    {
      field: 'deliveryNumber',
      title: '车辆号/船舶号/物流号',
      minWidth: '160px',
    },
    {
      field: 'contactName',
      title: '联系人姓名',
      minWidth: '150px',
    },
    {
      field: 'contactPhone',
      title: '联系人电话',
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', minWidth: '200px' },
  ],
};

const gridGoods: VxeTableGridOptions = {
  ...DETAIL_GRID_OPTIONS,
  data: detailForm.shipmentItemList,
  columns: [
    {
      field: 'productName',
      title: '商品名称',
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '单位',
      minWidth: '150px',
    },
    {
      field: 'quantity',
      title: '数量',
      minWidth: '150px',
    },
    {
      field: 'shippedQuantity',
      title: '本次发运重量',
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentCode',
      title: '源单据编号',
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '源单据商品行号',
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', minWidth: '200px' },
  ],
};

const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};

// 注册表格
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: grid,
});

const [GridGoods, gridApiGoods] = useVbenVxeGrid({
  gridOptions: gridGoods,
});

// 监听 shipmentDeliveryList 变化并更新表格
watch(
  () => detailForm.shipmentDeliveryList,
  (newVal) => {
    if (gridApi.grid) {
      nextTick(() => {
        gridApi.grid.loadData(newVal || []);
      });
    }
  },
  { deep: true },
);

// 监听 shipmentItemList 变化并更新表格
watch(
  () => detailForm.shipmentItemList,
  (newVal) => {
    if (gridApiGoods.grid) {
      nextTick(() => {
        gridApiGoods.grid.loadData(newVal || []);
      });
    }
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup" @close="close">
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="发货单编号">
          <span v-if="detailForm.id">
            {{ detailForm.shipmentCode }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="运输企业">
          <span v-if="detailForm.carrierCompanyName">
            {{ detailForm.carrierCompanyName }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="项目名称">
          <span v-if="detailForm.projectName">
            {{ detailForm.projectName }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="源单类型" name="documentType">
          <StatusTag code="DOCUMENT_TYPE" :value="detailForm.sourceDocumentType" />
        </DescriptionsItem>
        <DescriptionsItem label="发运类型">
          <StatusTag code="TRANSPORT_TYPE" :value="detailForm.shipmentType" />
        </DescriptionsItem>
        <DescriptionsItem label="运输方式">
          <StatusTag code="TRANSPORT_MODE" :value="detailForm.transportMethod" />
        </DescriptionsItem>
        <DescriptionsItem label="收货企业">
          {{ detailForm.consigneeCompanyName }}
        </DescriptionsItem>
        <DescriptionsItem label="结算企业">
          {{ detailForm.billingCompanyName }}
        </DescriptionsItem>
        <DescriptionsItem label="发运日期">
          <span v-if="detailForm.shipmentDate">
            {{ formatDate(detailForm.shipmentDate, 'YYYY-MM-DD') }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="预计收货日期">
          <span v-if="detailForm.plannedDeliveryDate">
            {{ formatDate(detailForm.plannedDeliveryDate, 'YYYY-MM-DD') }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="运输费用">
          <span v-if="detailForm.totalShipmentCost">
            {{ detailForm.totalShipmentCost }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="收货地址">
          <span
            v-if="
              detailForm.receiptProvince ||
              detailForm.receiptCity ||
              detailForm.receiptDistrict ||
              detailForm.receiptDetailAddress
            "
          >
            {{ detailForm.receiptProvince }} / {{ detailForm.receiptCity }} / {{ detailForm.receiptDistrict }}
            {{ detailForm.receiptDetailAddress }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
        <DescriptionsItem label="备注" :span="2">
          <span v-if="detailForm.remarks">
            {{ detailForm.remarks }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
      </Descriptions>

      <BasicCaption content="交货信息" />
      <div>
        <Grid>
          <template #deliveryType="{ row }">
            {{ getDictList('TRANSPORT_VEHICLE').find((item) => item.value === row.deliveryType)?.label }}
          </template>

          <template #deliveryNumber="{ row }">
            {{ row.deliveryNumber }}
          </template>

          <template #contactName="{ row }">
            {{ row.contactName }}
          </template>

          <template #contactPhone="{ row }">
            {{ row.contactPhone }}
          </template>

          <template #remarks="{ row }">
            {{ row.remarks }}
          </template>
        </Grid>
      </div>

      <BasicCaption content="商品信息" />
      <div>
        <GridGoods>
          <template #productName="{ row }">
            {{ row.productName }}
          </template>

          <template #productAlias="{ row }">
            {{ row.productAlias }}
          </template>

          <template #productCode="{ row }">
            {{ row.productCode }}
          </template>

          <template #specifications="{ row }">
            {{ row.specifications }}
          </template>

          <template #measureUnit="{ row }">
            {{ row.measureUnit }}
          </template>

          <template #brandName="{ row }"> {{ row.brandName }} </template>

          <template #originName="{ row }"> {{ row.originName }} </template>

          <template #shippedQuantity="{ row }">
            {{ row.shippedQuantity }}
          </template>

          <template #sourceDocumentItemNumber="{ row }">
            {{ row.sourceDocumentItemNumber }}
          </template>

          <template #sourceDocumentName="{ row }">
            {{ row.sourceDocumentName }}
          </template>

          <template #sourceDocumentCode="{ row }">
            {{ row.sourceDocumentCode }}
          </template>

          <template #remarks="{ row }">
            {{ row.remarks }}
          </template>
        </GridGoods>
      </div>
      <BaseAttachmentList
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_SHIPMENT"
        :edit-mode="false"
      />
    </div>
  </BasicPopup>
</template>

<style></style>
