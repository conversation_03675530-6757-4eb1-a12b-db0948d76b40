<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { paramTransformers, useModalUrl } from '@vben/base-ui';
import { Page, prompt } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { defineFormOptions } from '@vben/utils';

import { Select } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getCommonCompanyListApi, getCompanyIndicatorPageListApi } from '#/api';

import CalculationEdit from './edit.vue';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        options: [],
        allowClear: true,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'scoringDate',
      label: '测算日期',
    },
  ],
  fieldMappingTime: [['scoringDate', ['scoringStartDate', 'scoringEndDate'], 'x']],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'companyName', title: '企业名称' },
    { field: 'companyCode', title: '社会信用代码' },
    { field: 'scoringDate', title: '测算日期', formatter: 'formatDate' },
    { field: 'finalScore', title: '测算总分' },
    { field: 'scoringLimit', title: '测算额度上限' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'SCM_SCORE_TEST_STATUS',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCompanyIndicatorPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerPage, { openPopup: openFormPopup }] = usePopup();
const editSuccess = () => {
  gridApi.reload();
};
const companyList = ref([]);
const getCompanyList = async () => {
  companyList.value = await getCommonCompanyListApi();
};
getCompanyList();
const add = async () => {
  const selectInfo = await prompt({
    component: Select,
    title: '选择企业',
    content: '',
    componentProps: {
      options: companyList.value,
      popupClassName: 'pointer-events-auto',
      fieldNames: {
        label: 'companyName',
        value: 'companyCode',
      },
      labelInValue: true,
    },
    modelPropName: 'value',
  });
  openFormPopup(true, { companyCode: selectInfo?.value, companyName: selectInfo?.label, pageType: 'edit' });
};
const edit = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const view = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'audit' });
};
useModalUrl({
  handlers: {
    detail: (params) => {
      view({ id: params.id });
    },
    audit: (params) => {
      audit({ id: params.id });
    },
  },
  transformParams: paramTransformers.stringToNumber,
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button type="primary" @click="add">新增</a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['DRAFTING'].includes(row.status)" @click="edit(row)">编辑</a-typography-link>
          <a-typography-link @click="view(row)">详情</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <CalculationEdit @register="registerPage" @ok="editSuccess" />
  </Page>
</template>

<style></style>
