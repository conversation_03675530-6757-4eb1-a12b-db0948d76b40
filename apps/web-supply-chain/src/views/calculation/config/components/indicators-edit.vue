<script setup lang="ts">
import type { ScoringIndicatorBO } from '#/api';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import BigNumber from 'bignumber.js';
import { cloneDeep, defaultsDeep, uniqueId } from 'lodash-es';

let globalResolve: ((value: any) => void) | null = null;
let globalReject: ((reason?: unknown) => void) | null = null;
const FormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await FormRef.value.validate();
    if (globalResolve) {
      globalResolve(cloneDeep(formData.value));
    }
    globalResolve = null;
    globalReject = null;
    await modalApi.close();
  },
  onClosed: () => {
    globalReject && globalReject(new Error('取消编辑'));
    globalResolve = null;
    globalReject = null;
    formData.value = {};
  },
});
const rules = {
  indicatorName: [{ required: true, message: '请输入分类名称' }],
  inputType: [{ required: true, message: '请选择输入类型', trigger: 'change' }],
  signType: [{ required: true, message: '请选择符号类型', trigger: 'change' }],
  maxScore: [{ required: true, message: '请输入最大分限制' }],
  weight: [{ required: true, message: '请输入权重' }],
  sortCode: [{ required: true, message: '请输入显示顺序' }],
};
const formData = ref<ScoringIndicatorBO>({});
const weight = computed({
  get() {
    return new BigNumber(formData.value.weight ?? 0).times(100).toNumber();
  },
  set(value: number) {
    formData.value.weight = new BigNumber(value).div(100).toNumber();
  },
});
const modalTitle = computed(() => {
  return formData.value.id ? '编辑指标' : '新增指标';
});
const edit = (data: ScoringIndicatorBO) => {
  return new Promise((resolve, reject) => {
    globalResolve = resolve;
    globalReject = reject;
    formData.value = defaultsDeep(data, {
      editKey: `indicators_temp_${uniqueId()}`,
      sortCode: 0,
      editType: 'indicators',
      indicatorRules: [],
    });
    modalApi.open();
  });
};
defineExpose({
  edit,
});
</script>

<template>
  <Modal :title="modalTitle">
    <a-form ref="FormRef" :model="formData" :rules="rules" :label-col="{ span: 6 }">
      <a-form-item label="指标名称" name="indicatorName">
        <a-input v-model:value="formData.indicatorName" />
      </a-form-item>
      <a-form-item label="输入类型" name="inputType">
        <a-select v-model:value="formData.inputType" placeholder="请选择输入类型" class="w-full">
          <a-select-option value="INPUT">手动输入</a-select-option>
          <a-select-option value="SELECT">下拉选择</a-select-option>
          <a-select-option value="RANGE">范围匹配</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="符号类型" name="signType">
        <a-select v-model:value="formData.signType" placeholder="请选择符号类型" class="w-full">
          <a-select-option :value="0">不限制</a-select-option>
          <a-select-option :value="1">必须为零或正分</a-select-option>
          <a-select-option :value="-1">必须为零或负分</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="最大分限制" name="maxScore">
        <a-input-number v-model:value="formData.maxScore" :controls="false" :min="0" class="w-full" />
      </a-form-item>
      <a-form-item label="权重" name="weight">
        <a-input-number v-model:value="weight" :controls="false" :min="0" class="w-full">
          <template #addonAfter> % </template>
        </a-input-number>
      </a-form-item>
      <a-form-item label="显示顺序" name="sortCode">
        <a-input-number v-model:value="formData.sortCode" :controls="false" :min="0" :precision="0" class="w-full" />
      </a-form-item>
    </a-form>
  </Modal>
</template>

<style></style>
