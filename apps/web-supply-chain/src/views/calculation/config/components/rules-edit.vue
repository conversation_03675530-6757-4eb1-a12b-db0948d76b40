<script setup lang="ts">
import type { ScoringCategoryBO, ScoringIndicatorBO, ScoringIndicatorRuleBO } from '#/api';

import { ref } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';

import { message, Switch, Table } from 'ant-design-vue';
import { cloneDeep, defaultsDeep } from 'lodash-es';

let globalResolve: ((value: any) => void) | null = null;
let globalReject: ((reason?: unknown) => void) | null = null;
const columns = [
  { title: '规则描述', dataIndex: 'ruleName', key: 'ruleName', width: 200 },
  { title: '对应分数', dataIndex: 'ruleScore', key: 'ruleScore', width: 200 },
  { title: '数值范围下限', dataIndex: 'minValue', key: 'minValue', width: 200 },
  { title: '下限是否包含', dataIndex: 'minValueInclusive', key: 'minValueInclusive', width: 200 },
  { title: '数值范围上限', dataIndex: 'maxValue', key: 'maxValue', width: 200 },
  { title: '上限是否包含', dataIndex: 'maxValueInclusive', key: 'maxValueInclusive', width: 200 },
  { title: '显示顺序', dataIndex: 'sortCode', key: 'sortCode', width: 200 },
  { title: '操作', dataIndex: 'action', key: 'action', width: 80 },
];
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    for (const o of formData.value) {
      if (!o.ruleName) {
        return message.error('请输入规则描述');
      }
      if (!o.ruleScore) {
        return message.error('请输入对应分数');
      }
      if (indicatorData.value.inputType === 'RANGE' && !o.minValue) {
        return message.error('请输入数值范围下限');
      }
      if (indicatorData.value.inputType === 'RANGE' && !o.maxValue) {
        return message.error('请输入数值范围上限');
      }
      if (o.sortCode === undefined) {
        return message.error('请输入显示顺序');
      }
    }
    formData.value.sort(
      (a: ScoringIndicatorRuleBO, b: ScoringIndicatorRuleBO) => (a.sortCode ?? 0) - (b.sortCode ?? 0),
    );
    if (globalResolve) {
      indicatorData.value.indicatorRules = cloneDeep(formData.value);
      globalResolve(cloneDeep(indicatorData));
    }
    globalResolve = null;
    globalReject = null;
    await modalApi.close();
  },
  onClosed: () => {
    globalReject && globalReject(new Error('取消编辑'));
    globalResolve = null;
    globalReject = null;
    formData.value = [];
  },
});
const indicatorData = ref<ScoringIndicatorBO>({});
const formData = ref<ScoringIndicatorRuleBO[]>([]);
const edit = (data: ScoringCategoryBO) => {
  return new Promise((resolve, reject) => {
    globalResolve = resolve;
    globalReject = reject;
    indicatorData.value = data;
    formData.value = defaultsDeep(data.indicatorRules, []);
    modalApi.open();
  });
};
const addRule = () => {
  formData.value.push({ sortCode: 0 });
  formData.value.sort((a: ScoringIndicatorRuleBO, b: ScoringIndicatorRuleBO) => (a.sortCode ?? 0) - (b.sortCode ?? 0));
};
const delRule = async (index: number) => {
  await confirm('确定删除此规则?', '确认删除');
  formData.value.splice(index, 1);
};
defineExpose({
  edit,
});
</script>

<template>
  <Modal title="编辑打分标准" class="w-[1200px]">
    <div class="mb-4 text-right">
      <a-button type="primary" @click="addRule">新增规则</a-button>
    </div>
    <Table :data-source="formData" :columns="columns" :pagination="false">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'ruleName'">
          <a-input v-model:value="record.ruleName" />
        </template>
        <template v-if="column.key === 'ruleScore'">
          <a-input-number v-model:value="record.ruleScore" :controls="false" class="w-full" />
        </template>
        <template v-if="column.key === 'minValue'">
          <a-input-number v-model:value="record.minValue" :controls="false" class="w-full" />
        </template>
        <template v-if="column.key === 'minValueInclusive'">
          <Switch v-model:checked="record.minValueInclusive" :checked-value="1" :un-checked-value="0" />
        </template>
        <template v-if="column.key === 'maxValue'">
          <a-input-number v-model:value="record.maxValue" :controls="false" class="w-full" />
        </template>
        <template v-if="column.key === 'maxValueInclusive'">
          <Switch v-model:checked="record.maxValueInclusive" :checked-value="1" :un-checked-value="0" />
        </template>
        <template v-if="column.key === 'sortCode'">
          <a-input-number v-model:value="record.sortCode" :controls="false" :min="0" :precision="0" class="w-full" />
        </template>
        <template v-if="column.key === 'action'">
          <a-typography-link @click="delRule(index)" type="danger">删除</a-typography-link>
        </template>
      </template>
    </Table>
  </Modal>
</template>

<style></style>
