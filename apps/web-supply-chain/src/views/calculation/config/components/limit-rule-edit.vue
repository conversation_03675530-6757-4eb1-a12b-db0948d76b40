<script setup lang="ts">
import type { ScoringLimitRuleBO } from '#/api';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { cloneDeep, defaultsDeep, uniqueId } from 'lodash-es';

let globalResolve: ((value: any) => void) | null = null;
let globalReject: ((reason?: unknown) => void) | null = null;
const FormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await FormRef.value.validate();
    if (globalResolve) {
      globalResolve(cloneDeep(formData.value));
    }
    globalResolve = null;
    globalReject = null;
    await modalApi.close();
  },
  onClosed: () => {
    globalReject && globalReject(new Error('取消编辑'));
    globalResolve = null;
    globalReject = null;
    formData.value = {};
  },
});
const rules = {
  ruleName: [{ required: true, message: '请输入规则名称' }],
  maxLimit: [{ required: true, message: '请输入最大授信上限' }],
  sortCode: [{ required: true, message: '请输入显示顺序' }],
};
const formData = ref<ScoringLimitRuleBO>({});
const modalTitle = computed(() => {
  return formData.value.id ? '编辑评定标准' : '新增评定标准';
});
const edit = (data: ScoringLimitRuleBO) => {
  return new Promise((resolve, reject) => {
    globalResolve = resolve;
    globalReject = reject;
    formData.value = defaultsDeep(data, {
      editKey: `limit_rule_temp_${uniqueId()}`,
      sortCode: 0,
      editType: 'adjustRule',
    });
    modalApi.open();
  });
};
defineExpose({
  edit,
});
</script>

<template>
  <Modal :title="modalTitle">
    <a-form ref="FormRef" :model="formData" :rules="rules" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
      <a-form-item label="规则名称" name="ruleName">
        <a-input v-model:value="formData.ruleName" />
      </a-form-item>
      <a-form-item label="数值范围下限" name="minScore">
        <a-input-number v-model:value="formData.minScore" :controls="false" :min="0" :precision="0" class="w-full" />
      </a-form-item>
      <a-form-item label="下限是否包含" name="minScoreInclusive">
        <a-switch v-model:checked="formData.minScoreInclusive" :checked-value="1" :un-checked-value="0" />
      </a-form-item>
      <a-form-item label="数值范围上限" name="maxScore">
        <a-input-number v-model:value="formData.maxScore" :controls="false" :min="0" :precision="0" class="w-full" />
      </a-form-item>
      <a-form-item label="上限是否包含" name="maxScoreInclusive">
        <a-switch v-model:checked="formData.maxScoreInclusive" :checked-value="1" :un-checked-value="0" />
      </a-form-item>
      <a-form-item label="最大授信上限" name="maxLimit">
        <a-input-number v-model:value="formData.maxLimit" :controls="false" :min="0" :precision="0" class="w-full" />
      </a-form-item>
      <a-form-item label="显示顺序" name="sortCode">
        <a-input-number v-model:value="formData.sortCode" :controls="false" :min="0" :precision="0" class="w-full" />
      </a-form-item>
    </a-form>
  </Modal>
</template>

<style></style>
