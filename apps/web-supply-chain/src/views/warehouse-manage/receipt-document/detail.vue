<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AddInbound } from '#/api';

import { computed, ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { inboundDetailApi } from '#/api';

const detailForm = ref<AddInbound>({});
const gridLocation: VxeTableGridOptions = {
  ...DETAIL_GRID_OPTIONS,
  data: detailForm.value.inboundReceiptItemBOs,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '单位',
      minWidth: '150px',
    },
    // { field: 'locationType', title: '已入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, //  后台反
    // { field: 'locationType', title: '未入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, // 源单减去已入库
    {
      field: 'inQuantity',
      title: '已入库数量',
      minWidth: '150px',
    },
    {
      field: 'unInQuantity',
      title: '未入库数量',
      minWidth: '150px',
    },
    {
      field: 'quantity',
      title: '本次入库数量',
      minWidth: '150px',
      editRender: {},
      slots: { default: 'edit_quantity' },
    },
    {
      field: 'sourceDocumentCode',
      title: '源单据编号',
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentName',
      title: '源单据名称',
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '源单据商品行号',
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', editRender: {}, slots: { default: 'edit_remarks' }, minWidth: '200px' },
    {
      field: 'serialNumbers',
      title: 'SN码',
      editRender: {},
      slots: { default: 'edit_serial_numbers' },
      minWidth: '150px',
    },
  ],
};
const [ProductGrid, productGridApi] = useVbenVxeGrid({
  gridOptions: gridLocation,
});
const init = async (data: any) => {
  console.log('data', data);
  detailForm.value = await inboundDetailApi(data.id);
  await productGridApi.grid.loadData(data.inboundReceiptItemBOs);
};
const [registerPopup] = usePopupInner(init);
const customerCompanyLabel = computed(() => {
  if (detailForm.value.sourceDocumentType === 'SALES_RETURN_ORDER') {
    return '下游企业';
  }
  return '上游企业';
});
</script>

<template>
  <BasicPopup v-bind="$attrs" title="入库单详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="入库单编号">
          {{ detailForm.inboundReceiptCode }}
        </DescriptionsItem>
        <DescriptionsItem label="入库日期">
          {{ detailForm.receiptDate }}
        </DescriptionsItem>
        <DescriptionsItem label="仓库名称">
          {{ detailForm.warehouseName }}
        </DescriptionsItem>
        <DescriptionsItem label="仓库企业">
          {{ detailForm.warehouseCompanyName }}
        </DescriptionsItem>
        <DescriptionsItem label="项目编号">
          {{ detailForm.projectCode }}
        </DescriptionsItem>
        <DescriptionsItem label="项目名称">
          {{ detailForm.projectName }}
        </DescriptionsItem>
        <DescriptionsItem label="源单类型">
          <StatusTag :value="detailForm.sourceDocumentType" code="DOCUMENT_TYPE_IN" />
        </DescriptionsItem>
        <DescriptionsItem label="源单编号">
          {{ detailForm.sourceDocumentCode }}
        </DescriptionsItem>
        <DescriptionsItem :label="customerCompanyLabel">
          {{ detailForm.customerCompanyName }}
        </DescriptionsItem>
        <DescriptionsItem label="备注" :span="2">
          {{ detailForm.remarks }}
        </DescriptionsItem>
      </Descriptions>
      <BasicCaption content="商品信息" />
      <ProductGrid />
    </div>
  </BasicPopup>
</template>

<style></style>
