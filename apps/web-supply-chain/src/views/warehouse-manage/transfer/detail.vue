<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { TransferApplyInfo } from '#/api';

import { ref, watch } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { infoTransferApi } from '#/api';

defineEmits(['register']);
const transDetail = ref<TransferApplyInfo>({});

const init = async (data: TransferApplyInfo) => {
  if (data?.id) {
    transDetail.value = await infoTransferApi({ id: data.id });
  }
};
const [registerPopup] = usePopupInner(init);

const bankGridOptions = {
  footerMethod() {
    const footerRow = {
      total: '合计',
      amountWithTax: transDetail.value.totalTransferValue,
    };
    return [footerRow];
  },
  columns: [
    { field: 'total' },
    { field: 'productName', title: '商品名称', minWidth: '160px' },
    { field: 'productAlias', title: '商品别名', minWidth: '160px' },
    { field: 'specifications', title: '规格型号', minWidth: '160px' },
    { field: 'productCode', title: '商品编码', minWidth: '160px' },
    { field: 'measureUnit', title: '计量单位', minWidth: '160px' },
    { field: 'brandName', title: '商品品牌', minWidth: '160px' },
    { field: 'originName', title: '生产厂家', minWidth: '160px' },
    { field: 'warehouseName', title: '出库仓库', minWidth: '160px' },
    { field: 'quantity', title: '库存重量', minWidth: '160px' },
    { field: 'availableQuantity', title: '可用库存重量', minWidth: '160px' },
    { field: 'outQuamtity', title: '已出库重量', minWidth: '160px' },
    { field: 'quantityBC', title: '本次提货重量', minWidth: '160px' },
    { field: 'priceWithTax', title: '提货单价', minWidth: '160px' },
    { field: 'amountWithTax', title: '提货金额', minWidth: '160px' },
    { field: 'purchasePrice', title: '采购单价', minWidth: '160px' },
    { field: 'purchaseCompanyCode', title: '采购订单编号', minWidth: '160px' },
    { field: 'itemNumber', title: '采购订单行号', minWidth: '160px' },
    { field: 'serialNumber', title: 'SN码', minWidth: '160px' },
    {
      field: '',
      title: '入库单号',
      minWidth: '160px',
      formatter: () => {
        return 'Not';
      },
    },
    { field: 'remarks', title: '备注', minWidth: '160px' },
  ],
  ...DETAIL_GRID_OPTIONS,
  showFooter: true,
} as VxeTableGridOptions;
const [ProducGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
watch(
  () => transDetail.value,
  (val = {}) => {
    bankGridApi.grid.reloadData(val.transferApplyItems ?? []);
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="订单信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="提货申请单编号">
          {{ transDetail.transferApplyCode }}
        </a-descriptions-item>
        <a-descriptions-item label="申请提货日期">
          {{ transDetail.applyDate }}
        </a-descriptions-item>
        <a-descriptions-item label="所属项目名称">
          {{ transDetail.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="所属项目编号">
          {{ transDetail.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="客户企业">
          {{ transDetail.customerCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="控货方">
          {{ transDetail.executorCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="仓库名称">
          {{ transDetail.warehouseName }}
        </a-descriptions-item>
        <a-descriptions-item label="仓储企业名称">
          {{ transDetail.customerCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="备注">
          {{ transDetail.remarks }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ transDetail.applyDate }}
        </a-descriptions-item>
        <a-descriptions-item label="最后修改时间"> not </a-descriptions-item>
        <a-descriptions-item label="业务负责人"> not </a-descriptions-item>
        <a-descriptions-item label="生效时间"> not </a-descriptions-item>
        <a-descriptions-item label="审批状态">
          {{ transDetail.approvalStatus }}
        </a-descriptions-item>
        <a-descriptions-item label="业务状态">
          {{ transDetail.status }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="商品信息" />
      <ProducGrid />
      <BaseAttachmentList :business-id="transDetail.id" business-type="SCM_TRANSFER_APPLY" />
    </div>
  </BasicPopup>
</template>

<style></style>
