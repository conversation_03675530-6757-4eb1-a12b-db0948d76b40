<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { TransferApplyInfo } from '#/api';

import { computed, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';

import { message, Select } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addTransferApi,
  getProjectListApi,
  getQueryItemListApi,
  getWarehouseListApi,
  infoTransferApi,
  updateTransferApi,
} from '#/api';

import ProductInfo from './product-info.vue';

const emit = defineEmits(['register', 'ok']);
const formInfo = ref<TransferApplyInfo>({});
const colSpan = { md: 12, sm: 24 };
const rules: Record<string, Rule[]> = {
  applyDate: [{ required: true, message: '请输入', trigger: 'blur' }],
  projectName: [{ required: true, message: '请输入', trigger: 'blur' }],
  customerCompanyName: [{ required: false, message: '请输入', trigger: 'blur' }],
  warehouseName: [{ required: true, message: '请选择仓库', trigger: 'blur' }],
};
const title = computed(() => {
  return formInfo.value.id ? '编辑提货申请' : '新增提货申请';
});
const formRef = ref();
const productGridRef = ref();
const companyOptions = ref<{ code: string; label: string; value: string }[]>([]); // 客户企业，下游企业

const init = async (data: TransferApplyInfo) => {
  if (data.id) {
    formInfo.value = await infoTransferApi({ id: data.id });
    productGridRef.value.setProductData(formInfo.value.transferApplyItems);
  } else {
    formInfo.value = data;
  }
};
// 加载库存商品信息
const loadQueryItemList = async (checked?: boolean) => {
  const data = await getQueryItemListApi({
    // customerCompanyCode: '1',
    // executorCompanyCode: formInfo.value.executorCompanyCode || '1',
    projectId: formInfo.value.projectId, // '100'
    warehouseId: formInfo.value.warehouseId, // '32',
    businessStructure: formInfo.value.businessStructure,
    sourceDocumentType: 'SALES_ORDER',
    isSnManaged: checked ? 1 : 0,
  });
  productGridRef.value.setProductData(data);
};
// 处理项目选择事件的函数
const handleProjectSelect = async (_value: string, option: any) => {
  formInfo.value.projectId = option.id; // 项目ID
  formInfo.value.projectName = option.label; // 项目名称
  formInfo.value.projectCode = option.value; // 项目编号
  formInfo.value.executorCompanyName = option.executorCompanyName; // 贸易执行企业名称
  formInfo.value.executorCompanyCode = option.executorCompanyCode; // 贸易执行企业代码
  formInfo.value.businessStructure = option.businessStructure;
  const projectPartners = option.projectPartners || []; // 合作企业
  companyOptions.value = projectPartners
    .filter((item) => item.partnerType === '2')
    .map((v) => ({ label: v.companyName, value: v.companyName, code: v.companyCode }));
};
const handleCompanySelect = (value: string) => {
  const selected = companyOptions.value.find((option) => option.value === value);
  if (selected) {
    formInfo.value.customerCompanyCode = selected.code;
    formInfo.value.customerCompanyName = selected.label;
  }
};
const save = async (type: string) => {
  await formRef.value.validate();
  const productRecord = productGridRef.value?.getProductData();
  const items = productRecord.items.map(({ id, ...item }: { [key: string]: any; id: string }) => {
    return formInfo.value.id
      ? {
          id,
          ...item,
        }
      : {
          ...item,
          stockId: id,
        };
  });
  formInfo.value.transferApplyItemRequests = items;
  formInfo.value.totalTransferValue = productRecord.totalTransferValue;
  formInfo.value.status = type;
  changeOkLoading(true);
  let api = addTransferApi;
  if (formInfo.value.id) {
    api = updateTransferApi;
  }
  try {
    const res = await api(formInfo.value as TransferApplyInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" @click="save('DRAFTING')">保存</a-button>
        <a-button type="primary" @click="save('SUBMITTED')">提交</a-button>
      </a-space>
    </template>
    <a-form
      ref="formRef"
      :colon="false"
      :model="formInfo"
      :rules="rules"
      :label-col="{ style: { width: '150px' } }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="提货申请单编号" name="transferApplyCode">
            <a-input v-model:value="formInfo.transferApplyCode" disabled v-if="formInfo.id" />
            <a-input value="自动生成" disabled v-else />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="申请提货日期" name="applyDate">
            <a-date-picker v-model:value="formInfo.applyDate" value-format="YYYY-MM-DD HH:mm:ss" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目名称" name="projectName">
            <ApiComponent
              v-model="formInfo.projectName"
              :component="Select"
              :api="getProjectListApi"
              label-field="projectName"
              value-field="projectCode"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="handleProjectSelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目编号" name="projectCode">
            <a-input v-model:value="formInfo.projectCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="客户企业" name="customerCompanyName">
            <a-select
              v-model:value="formInfo.customerCompanyName"
              :options="companyOptions"
              @select="handleCompanySelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="控货方" name="projectModel">
            <a-input v-model:value="formInfo.executorCompanyName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="仓库名称" name="warehouseName">
            <ApiComponent
              v-model="formInfo.warehouseName"
              :component="Select"
              :api="getWarehouseListApi"
              :params="{ status: 'ACTIVE' }"
              label-field="warehouseName"
              value-field="id"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="
                (_value: string, option: any) => {
                  formInfo.warehouseId = option.value;
                  formInfo.warehouseName = option.label;
                  formInfo.warehouseCompanyName = option.warehouseCompanyName;
                  loadQueryItemList();
                }
              "
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="仓储企业名称" name="warehouseCompanyName">
            <a-input v-model:value="formInfo.warehouseCompanyName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remarks">
            <a-textarea v-model:value="formInfo.remarks" :rows="4" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 商品信息 -->
      <BasicCaption content="库存信息" />
      <ProductInfo
        ref="productGridRef"
        @sn-change="
          (checked: boolean) => {
            loadQueryItemList(checked);
          }
        "
      />
      <div class="bzj-bar">
        <div>保证金比例下限(%)</div>
        <div>提货后保证金比例(%)</div>
      </div>
      <BaseAttachmentList
        :key="formInfo.id"
        v-model="formInfo.attachmentList"
        :business-id="formInfo.id"
        business-type="SCM_TRANSFER_APPLY"
        edit-mode
      />
    </a-form>
  </BasicPopup>
</template>
<style lang="less" scoped>
.bzj-bar {
  display: flex;
  justify-content: space-around;
  padding-bottom: 20px;
}
</style>
