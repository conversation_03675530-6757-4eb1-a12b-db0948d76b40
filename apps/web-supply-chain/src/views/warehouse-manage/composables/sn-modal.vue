<script setup lang="ts">
import { ref } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';

import { Alert, Button, Col, Form, FormItem, Input, Radio, RadioGroup, Row, Table, Textarea, message } from 'ant-design-vue';

const [Modal] = useVbenModal();

const dataSource = ref<{ remarks: string; sn: string }[]>([]);
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: 'SN码',
    dataIndex: 'sn',
  },
  {
    title: 'SN码备注',
    dataIndex: 'remarks',
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
  },
];
const selectedRowKeys = ref<string[]>([]);
const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys;
};

// 手动添加SN码的输入值
const manualSnInput = ref('');

// 检查SN码是否重复
const checkSnDuplicate = (snCode: string): boolean => {
  return dataSource.value.some(item => item.sn === snCode);
};

// 添加SN码
const addManualSn = () => {
  const trimmedSn = manualSnInput.value.trim();

  if (!trimmedSn) {
    message.warning('请输入SN码');
    return;
  }

  // 校验是否重复
  if (checkSnDuplicate(trimmedSn)) {
    message.error(`SN码 "${trimmedSn}" 已存在，不能重复添加`);
    return;
  }

  // 添加到列表
  dataSource.value.push({
    sn: trimmedSn,
    remarks: ''
  });

  manualSnInput.value = '';
  message.success(`成功添加SN码: ${trimmedSn}`);
};

// 删除SN码
const removeSnItem = (snCode: string) => {
  const index = dataSource.value.findIndex(item => item.sn === snCode);
  if (index > -1) {
    dataSource.value.splice(index, 1);
    message.success(`已删除SN码: ${snCode}`);
  }
};

// 处理回车键添加
const handleEnterAdd = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    addManualSn();
  }
};
const [BatchInputModal, batchInputModalApi] = useVbenModal({
  onConfirm: async () => {
    console.log('批量录入:', batchInputForm.value);
    await batchInputModalApi.close();
  },
});
const batchInputForm = ref({
  delimiter: 'comma',
  snList: '',
});
const clear = async () => {
  await confirm('确认清空所有数据吗？', '确认清空');
  batchInputForm.value.snList = '';
};
// 去重校验
const validateInputUnique = (snList: string) => {
  const snArray = snList.split(batchInputForm.value.delimiter);
  const uniqueSnSet = new Set(snArray);
  return uniqueSnSet.size === snArray.length;
};
</script>

<template>
  <Modal>
    <Row :gutter="24">
      <Col :span="12">
        <div class="space-y-4">
          <!-- 方式一：快捷录入 -->
          <div
            class="hover:to-blue-150 rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-blue-100 p-4 transition-all duration-200 hover:from-blue-100 hover:shadow-md"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-500">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-base font-semibold text-gray-800">方式一：快捷录入</h3>
                  <p class="text-sm text-gray-600">快速批量生成SN码</p>
                </div>
              </div>
              <Button type="primary" class="border-blue-500 bg-blue-500 hover:border-blue-600 hover:bg-blue-600">
                批量录入
              </Button>
            </div>
          </div>

          <!-- 方式二：手工添加SN码 -->
          <div
            class="hover:to-green-150 rounded-lg border border-green-200 bg-gradient-to-r from-green-50 to-green-100 p-4 transition-all duration-200 hover:from-green-100 hover:shadow-md"
          >
            <div class="mb-3 flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-green-500">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-base font-semibold text-gray-800">方式二：手工添加SN码</h3>
                  <p class="text-sm text-gray-600">手动逐个添加SN码</p>
                </div>
              </div>
            </div>
            <!-- 输入框区域 -->
            <div class="flex space-x-2">
              <Input
                v-model:value="manualSnInput"
                placeholder="请输入SN码"
                class="flex-1"
                @keydown="handleEnterAdd"
                allow-clear
              />
              <Button
                type="primary"
                class="border-green-500 bg-green-500 hover:border-green-600 hover:bg-green-600"
                @click="addManualSn"
                :disabled="!manualSnInput.trim()"
              >
                添加
              </Button>
            </div>
          </div>

          <!-- 方式三：批量导入 -->
          <div
            class="hover:to-purple-150 rounded-lg border border-purple-200 bg-gradient-to-r from-purple-50 to-purple-100 p-4 transition-all duration-200 hover:from-purple-100 hover:shadow-md"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-purple-500">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-base font-semibold text-gray-800">方式三：批量导入</h3>
                  <p class="text-sm text-gray-600">从文件导入SN码数据</p>
                </div>
              </div>
              <Button
                type="primary"
                class="border-purple-500 bg-purple-500 hover:border-purple-600 hover:bg-purple-600"
              >
                批量导入
              </Button>
            </div>
          </div>
        </div>
      </Col>
      <Col :span="12">
        <div class="h-full rounded-lg border border-gray-200 bg-white p-4">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="text-base font-semibold text-gray-800">SN码列表</h3>
            <span class="text-sm text-gray-500">共 {{ dataSource.length }} 条</span>
          </div>
          <Table
            :columns="columns"
            :data-source="dataSource"
            row-key="sn"
            :row-selection="{ selectedRowKeys, onChange: onSelectChange, fixed: true }"
            :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <Button
                  type="link"
                  danger
                  size="small"
                  @click="removeSnItem(record.sn)"
                >
                  删除
                </Button>
              </template>
            </template>
          </Table>
        </div>
      </Col>
    </Row>
  </Modal>
  <BatchInputModal title="批量录入" class="w-[600px]">
    <Form>
      <FormItem label="分隔符">
        <RadioGroup v-model:value="batchInputForm.delimiter">
          <Radio value="comma">换行</Radio>
          <Radio value="space">空格</Radio>
          <Radio value="other">逗号</Radio>
          <Radio value="other">分号</Radio>
        </RadioGroup>
      </FormItem>
      <Alert message="重复的序列号或一次添加超过5000字符SN码值，将会被自动删除" type="warning" class="mb-4" />
      <FormItem :label-col="{ span: 0 }">
        <Textarea v-model:value="batchInputForm.snList" placeholder="请输入SN码" :rows="4" />
      </FormItem>
    </Form>
    <template #center-footer>
      <Button danger @click="clear">清空</Button>
    </template>
  </BatchInputModal>
</template>

<style></style>
