<script setup lang="ts">
import { useVbenModal } from '@vben/common-ui';

import { Button, Col, Row, Table } from 'ant-design-vue';

const [Modal] = useVbenModal();
</script>

<template>
  <Modal>
    <Row>
      <Col :span="12">
        <div>
          <p>方式一：快捷录入</p>
          <Button>批量录入</Button>
        </div>
        <div>
          <p>方式二：手工添加SN码</p>
          <Button>添加</Button>
        </div>
        <div>
          <p>方式三：批量导入</p>
          <Button>批量录入</Button>
        </div>
      </Col>
      <Col :span="12">
        <Table />
      </Col>
    </Row>
  </Modal>
</template>

<style></style>
