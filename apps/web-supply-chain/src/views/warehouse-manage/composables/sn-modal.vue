<script setup lang="ts">
import { useVbenModal } from '@vben/common-ui';

import { Button, Col, Row, Table } from 'ant-design-vue';

const [Modal] = useVbenModal();
</script>

<template>
  <Modal>
    <Row :gutter="24">
      <Col :span="12">
        <div class="space-y-4">
          <!-- 方式一：快捷录入 -->
          <div
            class="hover:to-blue-150 rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-blue-100 p-4 transition-all duration-200 hover:from-blue-100 hover:shadow-md"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-500">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">方式一：快捷录入</h3>
                  <p class="text-sm text-gray-600">快速批量生成SN码</p>
                </div>
              </div>
              <Button type="primary" class="border-blue-500 bg-blue-500 hover:border-blue-600 hover:bg-blue-600">
                批量录入
              </Button>
            </div>
          </div>

          <!-- 方式二：手工添加SN码 -->
          <div
            class="hover:to-green-150 rounded-lg border border-green-200 bg-gradient-to-r from-green-50 to-green-100 p-4 transition-all duration-200 hover:from-green-100 hover:shadow-md"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-green-500">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">方式二：手工添加SN码</h3>
                  <p class="text-sm text-gray-600">手动逐个添加SN码</p>
                </div>
              </div>
              <Button type="primary" class="border-green-500 bg-green-500 hover:border-green-600 hover:bg-green-600">
                添加
              </Button>
            </div>
          </div>

          <!-- 方式三：批量导入 -->
          <div
            class="hover:to-purple-150 rounded-lg border border-purple-200 bg-gradient-to-r from-purple-50 to-purple-100 p-4 transition-all duration-200 hover:from-purple-100 hover:shadow-md"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-purple-500">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">方式三：批量导入</h3>
                  <p class="text-sm text-gray-600">从文件导入SN码数据</p>
                </div>
              </div>
              <Button
                type="primary"
                class="border-purple-500 bg-purple-500 hover:border-purple-600 hover:bg-purple-600"
              >
                批量导入
              </Button>
            </div>
          </div>
        </div>
      </Col>
      <Col :span="12">
        <div class="h-full rounded-lg border border-gray-200 bg-white p-4">
          <h3 class="mb-4 text-lg font-semibold text-gray-800">SN码列表</h3>
          <Table />
        </div>
      </Col>
    </Row>
  </Modal>
</template>

<style></style>
