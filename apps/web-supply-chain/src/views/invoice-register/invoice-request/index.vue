<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  type InputApplicationPageInfo,
  inputApplicationPageApi,
  inboundDeleteApi,
  inputApplicationLineApi,
} from '#/api';

import Create from './create.vue';

const { getDictList } = useDictStore();
const sortKey = ref<string>('create_time');
const isShowParticular = ref<number>(0);
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'invoiceNo',
      label: '开票申请编号',
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '所属项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目名称',
    },
    {
      component: 'Input',
      fieldName: 'sellerCompanyCode',
      label: '开发方',
    },
    {
      component: 'Input',
      fieldName: 'buyerCompanyCode',
      label: '收票方',
    },
    {
      component: 'Select',
      fieldName: 'invoiceType',
      label: '发票类型',
      componentProps: {
        options: getDictList('INVOICE_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Input',
      fieldName: 'invoiceNumber',
      label: '发票号码',
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: getDictList('REVIEW_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'invoiceAttributes',
      label: '发票属性',
      componentProps: {
        options: getDictList('INVOICE_ATTRIBUTES'),
      },
    },
    {
      component: 'Select',
      fieldName: 'invoiceStatus',
      label: '开票状态',
      componentProps: {
        options: getDictList('INVOICE_STATUS'),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'realInvoiceTime',
      label: '实际开票日期',
    },
  ],
  fieldMappingTime: [
    ['realInvoiceTime', ['realInvoiceStartTime', 'realInvoiceEndTime'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'invoiceNo', title: '开票申请编号' },
    { field: 'sellerCompanyCode', title: '开票方企业' },
    { field: 'buyerCompanyCode', title: '收票方企业' },
    {
      field: 'invoiceType',
      title: '发票类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'INVOICE_TYPE',
        },
      },
    },
    { field: 'invoiceNumber', title: '发票号码' },
    {
      field: 'invoiceAttributes',
      title: '发票属性',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'INVOICE_ATTRIBUTES',
        },
      },
    },
    { field: 'totalAmountTax', title: '含税金额' },
    { field: 'totalAmount', title: '不含税金额' },
    { field: 'taxAmount', title: '税额' },
    {
      field: 'taxType',
      title: '业务类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'INVOICE_BUSINESS_TYPE',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'status',
      title: '业务状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
    },
    {
      field: 'invoiceStatus',
      title: '开票状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'INVOICE_STATUS',
        },
      },
    },
    { field: 'sourceInvoiceNumber', title: '关联蓝字发票号码' },
    { field: 'applyTime', title: '申请开票日期', formatter: 'formatDate' },
    { field: 'realInvoiceTime', title: '实际开票日期', formatter: 'formatDate' },
    { field: 'projectCode', title: '所属项目编号' },
    { field: 'projectName', title: '所属项目名称' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await inputApplicationPageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const gridParticulars: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'invoiceNo', title: '发票记录编号' },
    { field: 'sellerCompanyName', title: '开票方企业' },
    { field: 'buyerCompanyCode', title: '收票方企业' },
    {
      field: 'invoiceType',
      title: '发票类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'INVOICE_TYPE',
        },
      },
    },
    { field: 'invoiceNumber', title: '发票号码' },
    { field: 'productName', title: '商品名称' },
    { field: 'specifications', title: '规格型号' },
    { field: 'measureUnit', title: '计量单位' },
    { field: 'quantity', title: '开票数量' },
    { field: 'taxRate', title: '税率(%)' },
    { field: 'unitPrice', title: '单价' },
    { field: 'totalAmountTax', title: '含税金额' },
    { field: 'totalAmountLine', title: '不含税金额' },
    { field: 'taxAmount', title: '税额' },
    {
      field: 'taxType',
      title: '业务类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'INVOICE_BUSINESS_TYPE',
        },
      },
    },
    {
      field: 'invoiceAttributes',
      title: '发票属性',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'INVOICE_ATTRIBUTES',
        },
      },
    },
    { field: 'sourceInvoiceNumber', title: '关联蓝字发票号码' },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'status',
      title: '业务状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
    },
    { field: 'realInvoiceTime', title: '实际开票日期', formatter: 'formatDate' },
    { field: 'projectCode', title: '所属项目编号' },
    { field: 'projectName', title: '所属项目名称' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await inputApplicationLineApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();

const [GridMain, gridMainApi] = useVbenVxeGrid({
  formOptions,
  gridOptions: gridOptions,
});

const [GridParticulars, gridParticularsApi] = useVbenVxeGrid({
  formOptions,
  gridOptions: gridParticulars,
});

const gridApi = computed(() => {
  return isShowParticular.value === 0 ? gridMainApi : gridParticularsApi;
});

// 创建
const addBlue = () => {
  openFormPopup(true, { invoiceAttributes: 'BLUE' });
};
const addRed = () => {
  openFormPopup(true, { invoiceAttributes: 'RED' });
};
// 编辑
const handleEdit = (row: InputApplicationPageInfo) => {
  openFormPopup(true, row);
};

// 查看
const handleDetail = (row: InputApplicationPageInfo) => {
  openFormPopup(true, row);
  console.log('查看', row);
};

// 删除
const del = (row: InputApplicationPageInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelete'),
    content: $t('base.deleteConfirmContent'),
    okText: $t('base.confirm'),
    cancelText: $t('base.cancel'),
    onOk: async () => {
      // 这里可以调用删除API
      await inboundDeleteApi(row.id);
      // 删除成功后刷新列表
      await gridApi.reload();
    },
  });
};

const invoicing = (row: InputApplicationPageInfo) => {
  console.log(1111, row);
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};
</script>

<template>
  <Page auto-content-height>
    <!-- 发票信息表格 -->
    <GridMain v-if="isShowParticular === 0">
      <template #toolbar-actions>
        <div style="width: 100%; display: flex; justify-content: space-between; align-items: center">
          <div>
            <Button class="mr-2" type="primary" @click="addBlue">
              <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
              蓝字发票
            </Button>
            <Button class="mr-2" type="primary" @click="addRed">
              <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
              红字发票
            </Button>
          </div>

          <a-radio-group v-model:value="isShowParticular">
            <a-radio-button :value="0">发票信息</a-radio-button>
            <a-radio-button :value="1">明细信息</a-radio-button>
          </a-radio-group>
        </div>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="handleEdit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="handleDetail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="invoicing(row)"> 开票 </TypographyLink>
        </Space>
      </template>
    </GridMain>

    <!-- 明细信息表格 -->
    <GridParticulars v-if="isShowParticular === 1">
      <template #toolbar-actions>
        <div style="width: 100%; display: flex; justify-content: space-between; align-items: center">
          <Button class="mr-2" type="primary" @click="add">
            <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
            {{ $t('base.add') }}
          </Button>
          <a-radio-group v-model:value="isShowParticular">
            <a-radio-button :value="0">发票信息</a-radio-button>
            <a-radio-button :value="1">明细信息</a-radio-button>
          </a-radio-group>
        </div>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="handleEdit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="handleDetail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="invoicing(row)"> 开票 </TypographyLink>
        </Space>
      </template>
    </GridParticulars>

    <Create @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
