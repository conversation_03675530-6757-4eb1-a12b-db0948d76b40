<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';
import { ApiComponent } from '@vben/common-ui';
import type { Rule } from 'ant-design-vue/es/form';

import { Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  type InputApplicationPageInfo,
  inputApplicationDetailApi,
  getProjectListApi,
  getCompanyApi,
  inputApplicationEditApi,
  inputApplicationAddApi,
  inputApplicationSubmitApi,
  companyInvoiceListApi,
  inputInvoicePageApi,
} from '#/api';
import { BaseAttachmentList } from '#/adapter/base-ui';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();
const loading = reactive({
  submit: false,
});

const companyListOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyListOptions.value, res);
};

// 默认数据
const defaultForm: Partial<InputApplicationPageInfo> = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  deleteFlag: true,
  invoiceType: undefined,
  taxType: undefined,
  buyerCompanyCode: undefined,
  buyerCompanyName: undefined,
  sellerCompanyCode: undefined,
  sellerCompanyName: undefined,
  buyerCompanyAddress: undefined,
  buyerCompanyContract: undefined,
  sellerCompanyAddress: undefined,
  sellerCompanyContract: undefined,
  totalAmount: undefined,
  totalAmountTax: undefined,
  status: undefined,
  invoiceCode: undefined,
  invoiceNumber: undefined,
  isHc: undefined,
  hcDesc: undefined,
  sourceInvoiceCode: undefined,
  sourceInvoiceNumber: undefined,
  remark: undefined,
  invoiceDate: undefined,
  fileId: undefined,
  checkCode: undefined,
  version: undefined,
  attachmentList: [],
  invoiceAttributes: undefined,
  invoiceStatus: undefined,
  approvalStatus: undefined,
  projectCode: undefined,
  projectName: undefined,
  invoiceNo: undefined,
  applyTime: undefined,
  realInvoiceTime: undefined,
  buyerPhone: undefined,
  buyerAccount: undefined,
  buyerBank: undefined,
  buyerInvoiceTitle: undefined,
  sellerPhone: undefined,
  sellerAccount: undefined,
  sellerBank: undefined,
  sellerInvoiceTitle: undefined,
  outputInvoiceItemBOList: [
    {
      id: undefined,
      invoiceId: undefined,
      itemName: undefined,
      specifications: undefined,
      measureUnit: undefined,
      quantity: undefined,
      unitPrice: undefined,
      totalAmount: undefined,
      taxRate: undefined,
      taxAmount: undefined,
      totalAmountTax: undefined,
      version: undefined,
    },
  ],
  outputInvoiceInputRelBOList: [],
};

let detailForm = reactive<Partial<InputApplicationPageInfo>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const title = computed(() => {
  return detailForm.id ? '新建开票申请' : '编辑开票申请';
});

const init = async (data: any) => {
  detailForm.invoiceAttributes = data.invoiceAttributes;
  await getCompanyList();
  if (data.id) {
    const res = await inputApplicationDetailApi(data.id);

    // 深度复制确保响应性
    Object.keys(res).forEach((key) => {
      detailForm[key] = res[key];
    });

    // 强制校验并转换inboundReceiptItemBOs字段
    if (!Array.isArray(res.inboundReceiptItemBOs) || res.inboundReceiptItemBOs === null) {
      detailForm.inboundReceiptItemBOs = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.inboundReceiptItemBOs = [...res.inboundReceiptItemBOs];
    }

    // 强制刷新表格
    if (gridApi?.grid) {
      gridApi.grid.reloadData(detailForm.inboundReceiptItemBOs);
    }
    if (gridApiLocation?.grid) {
      gridApiLocation.grid.reloadData(detailForm.inboundReceiptItemBOs);
    }
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
    detailForm.inboundReceiptItemBOs = defaultForm.inboundReceiptItemBOs ? [...defaultForm.inboundReceiptItemBOs] : [];
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const labelCol = { style: { width: '150px' } };
const grid: VxeTableGridOptions = {
  data: detailForm.inboundReceiptItemBOs,
  props: {
    key: computed(() => detailForm.id || 'new'), // 添加key属性
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'itemName',
      title: '商品名称',
      slots: { default: 'itemName' },
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { default: 'specifications' },
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      slots: { default: 'measureUnit' },
      minWidth: '150px',
    },
    {
      field: 'quantity',
      title: '本次开票数量',
      slots: { default: 'quantity' },
      minWidth: '150px',
    },
    {
      field: 'unitPrice',
      title: '含税单价',
      slots: { default: 'unitPrice' },
      minWidth: '150px',
    },
    {
      field: 'totalAmount',
      title: '含税金额',
      slots: { default: 'totalAmount' },
      minWidth: '150px',
    },
    {
      field: 'taxAmount',
      title: '税额',
      slots: { default: 'taxAmount' },
      minWidth: '150px',
    },
    {
      field: 'taxRate',
      title: '税率(%)',
      slots: { default: 'taxRate' },
      minWidth: '150px',
    },
    {
      field: 'sourceSalesOrder',
      title: '关联销售订单',
      slots: { default: 'sourceSalesOrder' },
      minWidth: '150px',
    },
    {
      field: 'sourceSettlement',
      title: '关联销售结算单',
      slots: { default: 'sourceSettlement' },
      minWidth: '150px',
    },
    {
      field: 'settlementNumber',
      title: '销售订单行号',
      slots: { default: 'settlementNumber' },
      minWidth: '150px',
    },
  ],
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const gridLocation: VxeTableGridOptions = {
  data: detailForm.outputInvoiceInputRelBOList,
  props: {
    key: computed(() => detailForm.id || 'new'), // 添加key属性
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'invoiceNo',
      title: '发票记录编号',
      slots: { default: 'invoiceNo' },
      minWidth: '150px',
    },
    {
      field: 'invoiceNumber',
      title: '发票号码',
      slots: { default: 'invoiceNumber' },
      minWidth: '150px',
    },
    {
      field: 'sellerCompanyName',
      title: '开票方企业',
      slots: { default: 'sellerCompanyName' },
      minWidth: '150px',
    },
    {
      field: 'invoiceType',
      title: '发票类型',
      slots: { default: 'invoiceType' },
      minWidth: '150px',
    },
    {
      field: 'totalAmountTax',
      title: '含税金额',
      slots: { default: 'totalAmountTax' },
      minWidth: '150px',
    },
    {
      field: 'totalAmount',
      title: '不含税金额',
      slots: { default: 'totalAmount' },
      minWidth: '150px',
    },
    {
      field: 'invoiceDate',
      title: '发票日期',
      slots: { default: 'invoiceDate' },
      minWidth: '150px',
    },
    {
      field: 'invoiceAttributes',
      title: '发票属性',
      slots: { default: 'invoiceAttributes' },
      minWidth: '150px',
    },
    {
      field: 'remark',
      title: '备注',
      slots: { default: 'remark' },
      minWidth: '150px',
    },
  ],
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const formRef = ref();
const save = async (type: 'save' | 'submit') => {
  try {
    await formRef.value.validate();
    changeOkLoading(true);

    // 根据是否存在id判断是新增还是编辑
    let api = detailForm.id ? inputApplicationEditApi : inputApplicationAddApi;
    if (type === 'submit') {
      api = inputApplicationSubmitApi;
    }

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch {
    message.error('保存失败，请检查网络或输入内容');
    changeOkLoading(false); // 防止 loading 卡住
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const companyOptions = ref<{ code: string; label: string; value: string }[]>([]);
const handleProjectSelect = async (_value: string, option: any) => {
  detailForm.projectId = option.id; // 项目ID
  detailForm.projectName = option.label; // 项目名称
  detailForm.projectCode = option.value; // 项目编号
  detailForm.executorCompanyName = option.executorCompanyName; // 贸易执行企业名称
  detailForm.executorCompanyCode = option.executorCompanyCode; // 贸易执行企业代码
  detailForm.businessStructure = option.businessStructure;
  const projectPartners = option.projectPartners || []; // 合作企业
  companyOptions.value = projectPartners
    .filter((item: any) => item.partnerType === '2')
    .map((v: any) => ({ label: v.companyName, value: v.companyName, code: v.companyCode }));
};

const sellerInvoiceTitles = ref([]);
const buyerInvoiceTitles = ref([]);
const handleSellerCompanyChange = async (_: any, option: any) => {
  if (!!option.companyCode) {
    const res = await companyInvoiceListApi(option.companyCode);
    if (res && res.length > 0) {
      sellerInvoiceTitles.value = res;
    }
  }
};

const handleBuyerCompanyChange = async (_: any, option: any) => {
  if (!!option.companyCode) {
    const res = await companyInvoiceListApi(option.companyCode);
    if (res && res.length > 0) {
      buyerInvoiceTitles.value = res;
    }
  }
};

const changeSellerInvoiceTitle = (_: any, option: any) => {
  detailForm.sellerInvoiceTitle = option.title;
  detailForm.sellerCompanyCode = option.taxNumber;
  detailForm.sellerCompanyAddress = option.address;
  detailForm.sellerPhone = option.phone;
  detailForm.sellerBank = option.bank;
  detailForm.sellerAccount = option.account;
};

const changeBuyerInvoiceTitle = (_: any, option: any) => {
  detailForm.buyerInvoiceTitle = option.title;
  detailForm.buyerCompanyCode = option.taxNumber;
  detailForm.buyerCompanyAddress = option.address;
  detailForm.buyerPhone = option.phone;
  detailForm.buyerBank = option.bank;
  detailForm.buyerAccount = option.account;
};

const addRow = async (gridApi: GridApi) => {
  const newRecord = {
    id: detailForm.outputInvoiceItemBOList!.length,
    invoiceId: undefined,
    itemName: undefined,
    specifications: undefined,
    measureUnit: undefined,
    quantity: undefined,
    unitPrice: undefined,
    totalAmount: undefined,
    taxRate: undefined,
    taxAmount: undefined,
    totalAmountTax: undefined,
    version: undefined,
    sourceSalesOrder: undefined,
    sourceSettlement: undefined,
    settlementNumber: undefined,
  };
  const $grid = gridApi.grid;
  if ($grid) {
    await $grid.insertAt(newRecord, -1);
  }
};

const removeRow = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectedRows = $grid.getCheckboxRecords();
    if (selectedRows.length > 0) {
      $grid.remove(selectedRows);
      message.success('删除成功');
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

const selectInvoice = () => {
  console.log(111, '选择进项票');
};

const dataDetail = () => {
  console.log(2222, '数据详情');
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: grid,
});
watch(
  () => detailForm.inboundReceiptItemBOs,
  (newVal) => {
    if (gridApi?.grid && newVal) {
      gridApi.grid.reloadData(newVal);
    }
  },
  { deep: true, immediate: true },
);
const [GridLocation, gridApiLocation] = useVbenVxeGrid({
  gridOptions: gridLocation,
});
watch(
  () => detailForm.inboundReceiptItemBOs,
  (newVal) => {
    if (gridApiLocation?.grid && newVal) {
      gridApiLocation.grid.reloadData(newVal);
    }
  },
  { deep: true, immediate: true },
);

watch(
  () => detailForm.projectCode,
  (val) => {
    console.log('项目编号变化', val);
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')"> 保存 </a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')"> 提交 </a-button>
      </a-space>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="开票申请编号" name="invoiceNo">
            <Input v-model:value="detailForm.invoiceNo" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="发票属性" name="invoiceAttributes">
            <Select
              v-model:value="detailForm.invoiceAttributes"
              :options="getDictList('INVOICE_ATTRIBUTES')"
              :disabled="!!detailForm.invoiceAttributes"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="所属项目名称" name="projectName">
            <ApiComponent
              v-model="detailForm.projectName"
              :component="Select"
              :api="getProjectListApi"
              label-field="projectName"
              value-field="projectCode"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="handleProjectSelect"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="所属项目编号" name="projectCode">
            <span v-if="detailForm.projectCode">
              {{ detailForm.projectCode }}
            </span>
            <span v-else> - </span>
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="申请开票日期" name="applyTime">
            <DatePicker v-model:value="detailForm.applyTime" value-format="YYYY-MM-DD 00:00:00" format="YYYY-MM-DD" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="业务类型" name="taxType">
            <Select v-model:value="detailForm.taxType" :options="getDictList('INVOICE_BUSINESS_TYPE')" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="备注" name="remark">
            <Textarea v-model:value="detailForm.remark" :rows="3" />
          </FormItem>
        </Col>
      </Row>

      <!-- 税票信息 -->
      <BasicCaption content="税票信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="发票类型" name="invoiceType">
            <Select v-model:value="detailForm.invoiceType" :options="getDictList('INVOICE_TYPE')" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <!-- 占位 -->
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="开票方企业" name="sellerCompanyName">
            <Select
              v-model:value="detailForm.sellerCompanyName"
              :options="companyListOptions"
              @change="handleSellerCompanyChange"
              :field-names="{ label: 'companyName', value: 'id' }"
              show-search
              :filter-option="
                (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
              "
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收票方企业" name="buyerCompanyName">
            <Select
              v-model:value="detailForm.buyerCompanyName"
              :options="companyListOptions"
              @change="handleBuyerCompanyChange"
              :field-names="{ label: 'companyName', value: 'id' }"
              show-search
              :filter-option="
                (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
              "
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="发票抬头" name="sellerInvoiceTitle">
            <Select
              v-model:value="detailForm.sellerInvoiceTitle"
              :options="sellerInvoiceTitles"
              :field-names="{ label: 'title', value: 'id' }"
              placeholder="请先选择公司"
              @change="changeSellerInvoiceTitle"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="发票抬头" name="buyerInvoiceTitle">
            <Select
              v-model:value="detailForm.buyerInvoiceTitle"
              :options="buyerInvoiceTitles"
              :field-names="{ label: 'title', value: 'id' }"
              placeholder="请先选择公司"
              @change="changeBuyerInvoiceTitle"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="纳税人识别号" name="sellerCompanyCode">
            <Input v-model:value="detailForm.sellerCompanyCode" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="纳税人识别号" name="buyerCompanyCode">
            <Input v-model:value="detailForm.buyerCompanyCode" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="地址" name="sellerCompanyAddress">
            <Input v-model:value="detailForm.sellerCompanyAddress" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="地址" name="buyerCompanyAddress">
            <Input v-model:value="detailForm.buyerCompanyAddress" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="电话" name="sellerPhone">
            <Input v-model:value="detailForm.sellerPhone" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="电话" name="buyerPhone">
            <Input v-model:value="detailForm.buyerPhone" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="开户行" name="sellerBank">
            <Input v-model:value="detailForm.sellerBank" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="开户行" name="buyerBank">
            <Input v-model:value="detailForm.buyerBank" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="账号" name="sellerAccount">
            <Input v-model:value="detailForm.sellerAccount" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="账号" name="buyerAccount">
            <Input v-model:value="detailForm.buyerAccount" placeholder="选完抬头后自动填充" disabled />
          </FormItem>
        </Col>
      </Row>

      <!-- 发票明细 -->
      <BasicCaption content="发票明细" />
      <div class="invoice-details">
        <Grid>
          <template #toolbarTools>
            <Row>
              <Col v-bind="colSpan">
                <FormItem label="关联销售结算单" name="sourceDocumentType">
                  <Select v-model:value="detailForm.sourceDocumentType" />
                </FormItem>
              </Col>
            </Row>
            <a-space>
              <a-button type="primary" @click="addRow">增行</a-button>
              <a-button danger @click="removeRow">删行</a-button>
            </a-space>
          </template>
          <template #itemName="{ row }">
            {{ row.itemName }}
          </template>

          <template #specifications="{ row }">
            {{ row.specifications }}
          </template>

          <template #measureUnit="{ row }">
            {{ row.measureUnit }}
          </template>

          <template #quantity="{ row }">
            <Input v-model:value="row.quantity" />
          </template>

          <template #unitPrice="{ row }">
            <Input v-model:value="row.unitPrice" />
          </template>

          <template #totalAmount="{ row }">
            <Input v-model:value="row.totalAmount" />
          </template>

          <template #taxAmount="{ row }">
            <Input v-model:value="row.taxAmount" />
          </template>

          <template #taxRate="{ row }">
            <Input v-model:value="row.taxRate" />
          </template>

          <template #sourceSalesOrder="{ row }">
            {{ row.sourceSalesOrder }}
          </template>

          <template #sourceSettlement="{ row }">
            {{ row.sourceSettlement }}
          </template>

          <template #settlementNumber="{ row }">
            {{ row.settlementNumber }}
          </template>
        </Grid>
      </div>

      <!-- 进项票记录 -->
      <BasicCaption content="进项票记录" />
      <div>
        <GridLocation>
          <template #toolbarTools>
            <a-space>
              <a-button type="primary" @click="selectInvoice">选择进项票</a-button>
              <a-button danger @click="dataDetail">数据明细</a-button>
            </a-space>
          </template>
          <template #invoiceNo="{ row }">
            {{ row.invoiceNo }}
          </template>

          <template #invoiceNumber="{ row }">
            {{ row.invoiceNumber }}
          </template>

          <template #sellerCompanyName="{ row }">
            {{ row.sellerCompanyName }}
          </template>

          <template #invoiceType="{ row }">
            {{ row.invoiceType }}
          </template>

          <template #totalAmountTax="{ row }">
            {{ row.totalAmountTax }}
          </template>

          <template #totalAmount="{ row }">
            {{ row.totalAmount }}
          </template>

          <template #invoiceDate="{ row }">
            {{ row.invoiceDate }}
          </template>

          <template #invoiceAttributes="{ row }">
            {{ row.invoiceAttributes }}
          </template>

          <template #remark="{ row }">
            {{ row.remark }}
          </template>
        </GridLocation>
      </div>

      <BaseAttachmentList
        border="inner"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_OUTPUT_INVOICE"
        edit-mode
      />
    </Form>
  </BasicPopup>
</template>

<style scoped>
:deep(.ant-picker) {
  width: 100%;
}
</style>
