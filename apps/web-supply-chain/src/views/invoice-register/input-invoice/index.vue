<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AddInbound } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInboundPageApi, inboundCancelApi, inboundDeleteApi } from '#/api';

import Details from './details.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'inboundReceiptCode',
      label: '入库单编号',
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '所属项目编号',
    },
    {
      component: 'Select',
      fieldName: 'projectName',
      label: '所属项目名称',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'warehouseCompanyName',
      label: '关联单据编号',
    },
    {
      component: 'Select',
      fieldName: 'sourceDocumentType',
      label: '关联单据类型',
      // componentProps: {
      //   options: dictStore.getDictList('supplier_company'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'warehouseName',
      label: '仓库名称',
      // componentProps: {
      //   options: dictStore.getDictList('purchaser_company'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'detailAddress',
      label: '仓储企业',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      // componentProps: {
      //   options: dictStore.getDictList('project_status'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'createBy',
      label: '客户企业111',
    },
    {
      component: 'Input',
      fieldName: 'invoiceStatus',
      label: '开票状态',
    },
    {
      component: 'RangePicker',
      fieldName: 'receiptDate',
      label: '入库日期',
    },
  ],
  fieldMappingTime: [
    // 将 receiptDate 数组映射到 receiptStartDate 和 receiptEndDate 字段
    ['receiptDate', ['receiptStartDate', 'receiptEndDate'], 'YYYY-MM-DD'],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'warehouseCode', title: '仓库编号' },
    { field: 'warehouseName', title: '仓库名称' },
    { field: 'warehouseCompanyName', title: '仓储企业' },
    { field: 'province', title: '控货方' },
    { field: 'detailAddress', title: '客户企业' },
    { field: 'isLocationManaged', title: '入库日期' },
    { field: 'executorCompanyName', title: '货款含税金额' },
    { field: 'approvalStatus', title: '已开票价税合计' },
    { field: 'businessManagerName', title: '币种' },
    { field: 'status', title: '审批状态' },
    { field: 'remarks', title: '业务状态' },
    { field: 'remarks1', title: '开票状态' },
    { field: 'remarks2', title: '关联单据类型' },
    { field: 'remarks3', title: '关联单据名称' },
    { field: 'remarks4', title: '关联单据编号' },
    { field: 'remarks5', title: '所属项目名称' },
    { field: 'remarks6', title: '所属项目编号' },
    { field: 'createTime', title: '创建时间' },
    { field: 'createBy', title: '创建人' },
    { field: 'createDeptName', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getInboundPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 定义正确的类型
interface ProjectItem {
  id: number;
  warehouseCode: string;
  warehouseName: string;
  warehouseCompanyName: string;
  projectModel: string;
  supplierCompanyName: string;
  purchaserCompanyName: string;
  executorCompanyName: string;
  status: string; // 修复字段名
  approvalStatus: string;
  businessManagerName: string;
  businessDate: string;
  createTime: string;
  createBy: string;
  createDeptName: string;
}

// 创建
const add = () => {
  openFormPopup(true, {});
};

// 编辑
const handleEdit = (row: AddInbound) => {
  openFormPopup(true, row);
};

// 查看
const handleDetail = (row: AddInbound) => {
  openFormPopup(true, row);
  console.log('查看', row);
};

// 删除
const del = (row: AddInbound) => {
  AntdModal.confirm({
    title: $t('base.confirmDelete'),
    content: $t('base.deleteConfirmContent'),
    okText: $t('base.confirm'),
    cancelText: $t('base.cancel'),
    onOk: async () => {
      // 这里可以调用删除API
      await inboundDeleteApi(row.id);
      // 删除成功后刷新列表
      await gridApi.reload();
    },
  });
};

const cancel = (row: AddInbound) => {
  AntdModal.confirm({
    title: $t('base.confirmCancel'),
    content: $t('base.cancelConfirmContent'),
    okText: $t('base.confirm'),
    cancelText: $t('base.cancel'),
    onOk: async () => {
      // 这里可以调用删除API
      await inboundCancelApi(row.id);
      // 删除成功后刷新列表
      await gridApi.reload();
    },
  });
};

// 复制
const copy = (row: AddInbound) => {
  console.log('复制', row);
};

// 签章
const signature = (row: AddInbound) => {
  // AntdModal.confirm({
  //   title: $t('base.confirmCancel'),
  //   content: '确定要取消该合同吗？',
  //   okText: $t('base.confirm'),
  //   cancelText: $t('base.cancel'),
  //   onOk: async () => {
  //     // 这里可以调用取消合同的API
  //     // await cancelContractApi(row.id);
  //     // 取消成功后刷新列表
  //     // await gridApi.reload();
  //   },
  // });
  console.log('签章', row);
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="handleEdit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="handleDetail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink v-if="false" type="danger" @click="cancel(row)">l 作废 </TypographyLink>
          <TypographyLink v-if="false" @click="copy(row)">
            {{ $t('base.copy') }}
          </TypographyLink>
          <TypographyLink @click="signature(row)"> 签章 </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Details @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
