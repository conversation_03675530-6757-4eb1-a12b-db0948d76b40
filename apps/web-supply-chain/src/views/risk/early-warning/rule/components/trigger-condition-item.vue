<script setup lang="ts">
import { computed, defineProps } from 'vue';

import BigNumber from 'bignumber.js';

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
});
const value = computed(() => {
  if (!props.item.value) {
    return 0;
  }
  let value = new BigNumber(props.item.value);
  if (props.item.valueType === '2') {
    value = value.times(100);
  }
  return value.toNumber();
});
const conditionParts = computed(() => {
  if (!props.item.conditionDesc || !props.item.value) {
    return [];
  }
  let value = new BigNumber(props.item.value);
  if (props.item.valueType === '2') {
    value = value.times(100);
  }
  return props.item.conditionDesc.split(value.toString());
});
</script>

<template>
  <p class="mt-2">
    <span class="font-bold">预警触发参数：</span>
    <span>{{ conditionParts[0] }}</span>
    <span class="text-lg text-red-500">{{ value }}</span>
    <span>{{ conditionParts[1] }}</span>
  </p>
</template>

<style scoped></style>
