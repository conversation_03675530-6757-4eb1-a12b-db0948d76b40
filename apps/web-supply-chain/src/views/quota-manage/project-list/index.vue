<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProjectListApi } from '#/api/quota-manage';

import Detail from './detail.vue';

const dictStore = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'limitCode',
      label: '额度记录编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '额度主体',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '额度状态',
    },
    {
      component: 'Input',
      fieldName: 'endDate',
      label: '额度到期日',
    },
    {
      component: 'Input',
      fieldName: 'guaranteeCompanyName',
      label: '担保企业',
    },
    {
      component: 'Select',
      fieldName: 'creditType',
      label: '授信类型',
      componentProps: {
        options: dictStore.getDictList('CREDIT_TYPE'),
      },
    },
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'limitCode', title: '额度记录编号' },
    { field: 'projectName', title: '额度主体' },
    { field: 'guaranteeCompanyName', title: '担保企业' },
    {
      field: 'totalLimit',
      title: '项目总额度(元)',
    },
    {
      field: 'usedLimit',
      title: '已用额度(元)',
    },
    {
      field: 'frozenLimit',
      title: '冻结额度(元)',
    },
    {
      field: 'availableLimit',
      title: '可用额度(元)',
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getProjectListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <TypographyLink @click="openDetailPopup(true, row)"> 详情 </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <Detail @register="registerDetail" />
  </Page>
</template>

<style></style>
