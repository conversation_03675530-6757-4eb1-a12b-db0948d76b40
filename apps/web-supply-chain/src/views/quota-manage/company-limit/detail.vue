<script setup lang="ts">
import type { CompanylimitInfo } from '#/api/quota-manage';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { detailCreditApi } from '#/api/quota-manage';

const dictStore = useDictStore();
const infoDetail = ref<CompanylimitInfo>({});

const init = async (data: CompanylimitInfo) => {
  if (data?.id) {
    infoDetail.value = await detailCreditApi({ id: data.id });
  }
};
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="额度信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="企业额度管理编号">
          {{ infoDetail.creditCode }}
        </a-descriptions-item>
        <a-descriptions-item label="额度主体">
          {{ infoDetail.creditCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="统一社会信用代码">{{ infoDetail.creditCompanyCode }} </a-descriptions-item>
        <a-descriptions-item label="授信类型">
          {{ dictStore.formatter(String(infoDetail.creditType), 'CREDIT_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="企业额度上限（元）">
          {{ infoDetail.creditLimit }}
        </a-descriptions-item>
        <a-descriptions-item label="额度到期日">
          {{ infoDetail.expiryDate }}
        </a-descriptions-item>
        <a-descriptions-item label="备注">
          {{ infoDetail.remark }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicPopup>
</template>

<style></style>
