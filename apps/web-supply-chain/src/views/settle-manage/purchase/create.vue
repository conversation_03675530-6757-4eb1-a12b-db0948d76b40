<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { computed, reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, InputNumber, Row, Select, Textarea } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {} from '#/api';
import OrderSelector from '#/components/business/OrderSelector.vue';
import ProjectSelector from '#/components/business/ProjectSelector.vue';

import WarehouseModal from '../components/warehouseModal.vue';

// const emit = defineEmits(['register', 'success']);

const dictStore = useDictStore();

const COL_SPAN = COL_SPAN_PROP;
const FULL_ITEM_PROPS = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
const FORM_ITEM_PROPS = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };

const formRef = ref();
const settleDetailRef = ref();
const otherCostRef = ref();
const dataBackup = ref<any>([]);
const visible = ref(false);
const batchVisible = ref(false);
const settleRows = ref<any[]>([]);
const otherCostRows = ref<any[]>([]);

const baseForm = reactive({
  id: undefined as string | undefined,
  settleNo: '',
  settleName: '',
  projectCode: '',
  salesOrderCode: '',
  sellerCode: '',
  sellerName: '',
  creditCode: '',
  bizType: '',
  bizDate: '',
  refOrderType: '',
  refOrderCode: '',
  remark: '',
  attachmentList: [] as any[],
});

const rules: Record<string, Rule[]> = {
  projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
  salesOrderCode: [{ required: true, message: '请选择关联销售订单编号', trigger: 'change' }],
  sellerCode: [{ required: true, message: '请选择卖方企业', trigger: 'change' }],
  bizType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  refOrderType: [{ required: true, message: '请选择关联单据类型', trigger: 'change' }],
};
const title = computed(() => (baseForm.id ? '编辑' : '新增'));

const init = (data?: any) => {
  if (data && Object.keys(data).length > 0) {
    Object.assign(baseForm, data);
  }
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    /* 此处调用保存 API */
    // console.log('提交 payload', payload);
  } finally {
    changeOkLoading(false);
  }
};

const settleGridOptions: VxeTableGridOptions = {
  pagerConfig: { enabled: false },
  keepSource: true,
  data: settleRows,
  columns: [
    { type: 'checkbox', width: 60 },
    { field: 'skuName', title: '商品名称' },
    { field: 'skuSpec', title: '规格型号' },
    { field: 'skuCode', title: '商品编码' },
    { field: 'unit', title: '计量单位' },
    {
      field: 'taxRate',
      title: '税率(%)',
      formatter: 'formatMoney',
      slots: { default: 'taxRate', header: 'taxRateHeader' },
    },
    {
      field: 'settleQty',
      title: '本次结算数量',
      formatter: 'formatMoney',
      slots: { default: 'settleQty', header: 'settleQtyHeader' },
    },
    {
      field: 'settlePriceExTax',
      title: '结算不含税单价',
      formatter: 'formatMoney',
      slots: { default: 'settlePriceExTax', header: 'settlePriceExTaxHeader' },
    },
    {
      field: 'settlePriceIncTax',
      title: '结算含税单价',
      formatter: 'formatMoney',
      slots: { default: 'settlePriceIncTax', header: 'settlePriceIncTaxHeader' },
    },
    {
      field: 'settleTax',
      title: '本次结算税额',
      formatter: 'formatMoney',
      slots: { default: 'settleTax', header: 'settleTaxHeader' },
    },
    {
      field: 'settleAmtExTax',
      title: '本次结算不含税金额',
      formatter: 'formatMoney',
      slots: { default: 'settleAmtExTax', header: 'settleAmtExTaxHeader' },
    },
    {
      field: 'settleAmtIncTax',
      title: '本次结算价税合计',
      formatter: 'formatMoney',
      slots: { default: 'settleAmtIncTax', header: 'settleAmtIncTaxHeader' },
    },
    { field: 'preSettleAmtExTax', title: '已结算不含税金额', formatter: 'formatMoney' },
    { field: 'preSettleAmtIncTax', title: '已结算价税合计', formatter: 'formatMoney' },
    { field: 'poSettleQty', title: '采购订单已结算数量', formatter: 'formatMoney' },
    { field: 'poQty', title: '采购订单数量', formatter: 'formatMoney' },
    { field: 'poPriceExTax', title: '采购不含税单价', formatter: 'formatMoney' },
    { field: 'poPriceIncTax', title: '采购含税单价', formatter: 'formatMoney' },
    { field: 'refOrderType', title: '关联单据类型' },
    { field: 'refOrderCode', title: '关联单据编号' },
    { field: 'bizDate', title: '业务日期', formatter: 'formatDate' },
    { field: 'poRowNo', title: '采购订单行号' },
  ],
  editRules: {
    taxRate: [{ required: true, content: '请输入税率', trigger: 'change' }],
    settleQty: [{ required: true, content: '请输入本次结算数量', trigger: 'change' }],
    settlePriceExTax: [{ required: true, content: '请输入结算不含税单价' }],
    settlePriceIncTax: [{ required: true, content: '请输入结算含税单价' }],
    settleTax: [{ required: true, content: '请输入本次结算税额' }],
    settleAmtExTax: [{ required: true, content: '请输入本次结算不含税金额' }],
    settleAmtIncTax: [{ required: true, content: '请输入本次结算价税合计' }],
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
    slots: { tools: 'toolbar-tools' },
  },
  showFooter: true,
  footerMethod({ columns, data }) {
    const sums: any[] = [];
    for (const [columnIndex, column] of columns.entries()) {
      if (columnIndex === 0) {
        sums.push('合计');
        continue;
      }

      const field = column.field;
      const sumFields = new Set([
        'poQty',
        'poSettleQty',
        'preSettleAmtExTax',
        'preSettleAmtIncTax',
        'settleAmtExTax',
        'settleAmtIncTax',
        'settleQty',
        'settleTax',
      ]);

      if (sumFields.has(field)) {
        let sum = 0;
        for (const row of data) {
          const val = Number(row[field]);
          sum += Number.isNaN(val) ? 0 : val;
        }
        sums.push(sum.toFixed(2));
      } else {
        sums.push('');
      }
    }
    return [sums];
  },
};

const otherCostGridOptions: VxeTableGridOptions = {
  pagerConfig: { enabled: false },
  keepSource: true,
  data: otherCostRows,

  columns: [
    { type: 'checkbox', width: 60 },
    { field: 'costType', title: '费用类型', slots: { default: 'costType', header: 'costTypeHeader' } },
    { field: 'costName', title: '费用名称', slots: { default: 'costName', header: 'costNameHeader' } },
    { field: 'costDirection', title: '费用方向', slots: { default: 'costDirection', header: 'costDirectionHeader' } },
    {
      field: 'amountIncTax',
      title: '含税金额',
      formatter: 'formatMoney',
      slots: { default: 'amountIncTax', header: 'amountIncTaxHeader' },
    },
    { field: 'tax', title: '税额', formatter: 'formatMoney', slots: { default: 'tax', header: 'taxHeader' } },
    {
      field: 'taxRate',
      title: '税率(%)',
      formatter: 'formatMoney',
      slots: { default: 'taxRate', header: 'taxRateHeader' },
    },
    {
      field: 'amountExTax',
      title: '不含税金额',
      formatter: 'formatMoney',
      slots: { default: 'amountExTax', header: 'amountExTaxHeader' },
    },
    { field: 'refOrderType', title: '关联源单类型' },
    { field: 'refOrderCode', title: '关联源单编号' },
    { field: 'remark', title: '备注', width: 160, slots: { default: 'remark' } },
  ],
  editRules: {
    costType: [{ required: true, content: '请选择费用类型', trigger: 'change' }],
    costDirection: [{ required: true, content: '请选择费用方向', trigger: 'change' }],
    amountIncTax: [{ required: true, content: '请输入含税金额' }],
    tax: [{ required: true, content: '请输入税额' }],
    taxRate: [{ required: true, content: '请输入税率' }],
    amountExTax: [{ required: true, content: '请输入不含税金额' }],
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
    slots: { tools: 'toolbar-tools' },
  },
  showFooter: true,
  footerMethod({ columns, data }) {
    const sums: any[] = [];
    for (const [columnIndex, column] of columns.entries()) {
      if (columnIndex === 0) {
        sums.push('合计');
        continue;
      }

      const field = column.field;
      const sumFields = new Set(['amountExTax', 'amountIncTax', 'tax']);

      if (sumFields.has(field)) {
        let sum = 0;
        for (const row of data) {
          const val = Number(row[field]);
          sum += Number.isNaN(val) ? 0 : val;
        }
        sums.push(sum.toFixed(2));
      } else {
        sums.push('');
      }
    }
    return [sums];
  },
};

const [registerPopup, { changeOkLoading }] = usePopupInner(init);
const [SettleDetails, settleApi] = useVbenVxeGrid({ gridOptions: settleGridOptions });
const [OtherCostTable, otherCostApi] = useVbenVxeGrid({ gridOptions: otherCostGridOptions });

const removeSettleRow = () => {
  const $grid = settleApi.grid;
  if ($grid) {
    const select = $grid.getCheckboxRecords();
    if (select.length > 0) $grid.remove(select);
  }
};

const addOtherCostRow = () => {
  const newRow: any = {
    costType: undefined,
    costName: '',
    costDirection: '',
    amountIncTax: undefined,
    tax: undefined,
    taxRate: '',
    amountExTax: '',
    refOrderType: '',
    refOrderCode: '',
    remark: '',
  };
  const $grid = otherCostApi.grid;
  if ($grid) $grid.insert(newRow);
};

const removeOtherCostRow = () => {
  const $grid = otherCostApi.grid;
  if ($grid) {
    const select = $grid.getCheckboxRecords();
    if (select.length > 0) $grid.remove(select);
  }
};

const handleSelectWarehouse = (rows: any[]) => {
  dataBackup.value = cloneDeep(rows);
  const $grid = settleApi.grid;
  if ($grid) $grid.reloadData(rows);
};

const batchSet = async () => {};
/**
 * 重置
 */
const reset = () => {
  const $grid = settleApi.grid;
  if ($grid) $grid.reloadData(dataBackup.value);
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #centerToolbar>
      <Button type="primary" @click="handleSubmit">提交</Button>
    </template>

    <Form ref="formRef" :model="baseForm" :rules="rules" v-bind="FORM_ITEM_PROPS" class="px-8">
      <Row class="mt-5">
        <Col v-bind="COL_SPAN">
          <FormItem label="采购结算单编号">{{ baseForm.settleNo || '-' }}</FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="采购结算名称" name="settleName">
            <Input v-model:value="baseForm.settleName" placeholder="请输入" />
          </FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="所属项目" name="projectId">
            <ProjectSelector v-model="baseForm.projectId" />
          </FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="关联销售订单" name="salesOrderCode">
            <OrderSelector order-type="sales" v-model="baseForm.salesOrderCode" />
          </FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="卖方企业" name="sellerCode">
            <ApiComponent
              v-model:value="baseForm.sellerCode"
              :component="Select"
              :api="getCompanyListApi"
              label-field="companyName"
              value-field="companyCode"
              placeholder="请选择"
            />
          </FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="卖方企业" name="sellerName">
            {{ baseForm.sellerName || '-' }}
          </FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="统一社会信用代码">{{ baseForm.creditCode || '-' }}</FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="统一社会信用代码">{{ baseForm.creditCode || '-' }}</FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="业务类型" name="bizType">
            <Select
              v-model:value="baseForm.bizType"
              :options="dictStore.getDictList('FCT_MORTGAGE_TYPE')"
              placeholder="请选择"
            />
          </FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="业务日期" name="bizDate">
            <DatePicker v-model:value="baseForm.bizDate" value-format="x" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="关联单据类型" name="refOrderType">
            <Select
              v-model:value="baseForm.refOrderType"
              :options="dictStore.getDictList('DOCUMENT_TYPE')"
              placeholder="请选择"
            />
          </FormItem>
        </Col>
        <Col v-bind="COL_SPAN">
          <FormItem label="关联单据">{{ baseForm.refOrderCode || '-' }}</FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="备注" v-bind="FULL_ITEM_PROPS">
            <Textarea v-model:value="baseForm.remark" :rows="4" placeholder="请输入" />
          </FormItem>
        </Col>
      </Row>

      <BasicCaption content="结算明细" />
      <SettleDetails ref="settleDetailRef">
        <template #toolbar-tools>
          <Button type="primary" class="mr-2" @click="() => (visible = true)">选择结算数据</Button>
          <Button type="primary" class="mr-2" @click="() => (batchVisible = true)">批量设置</Button>
          <Button type="primary" class="mr-2" @click="reset">重置</Button>
          <Button type="primary" danger class="mr-2" @click="removeSettleRow">删行</Button>
        </template>
        <template #taxRate="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.taxRate"
            placeholder="请输入"
          />
        </template>
        <template #settleQty="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.settleQty"
            placeholder="请输入"
          />
        </template>
        <template #settlePriceExTax="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.settlePriceExTax"
            placeholder="请输入"
          />
        </template>
        <template #settlePriceIncTax="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.settlePriceIncTax"
            placeholder="请输入"
          />
        </template>
        <template #settleTax="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.settleTax"
            placeholder="请输入"
          />
        </template>
        <template #settleAmtExTax="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.settleAmtExTax"
            placeholder="请输入"
          />
        </template>
        <template #settleAmtIncTax="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.settleAmtIncTax"
            placeholder="请输入"
          />
        </template>

        <!-- 必填星号-->
        <template #taxRateHeader>
          <span><span style="color: red">*</span> 税率</span>
        </template>
        <template #settleQtyHeader>
          <span><span style="color: red">*</span> 结算数量</span>
        </template>
        <template #settlePriceExTaxHeader>
          <span><span style="color: red">*</span> 结算不含税单价</span>
        </template>
        <template #settlePriceIncTaxHeader>
          <span><span style="color: red">*</span> 结算含税单价</span>
        </template>
        <template #settleTaxHeader>
          <span><span style="color: red">*</span> 本次结算税额</span>
        </template>
        <template #settleAmtExTaxHeader>
          <span><span style="color: red">*</span> 本次结算不含税金额</span>
        </template>
        <template #settleAmtIncTaxHeader>
          <span><span style="color: red">*</span> 本次结算价税合计</span>
        </template>
        <template #remark="{ row }">
          <Input v-model:value="row.remark" placeholder="请输入" class="w-full" />
        </template>
      </SettleDetails>
      <BasicCaption content="其他费用分摊" />
      <OtherCostTable ref="otherCostRef">
        <template #toolbar-actions>
          <FormItem label="其他费用分摊方式" name="costType">
            <Select
              class="w-full"
              v-model:value="baseForm.costType"
              :options="dictStore.getDictList('FCT_MORTGAGE_TYPE')"
              placeholder="请选择"
            />
          </FormItem>
        </template>
        <template #toolbar-tools>
          <Button type="primary" class="mr-2" @click="reset">费用分摊</Button>
          <Button type="primary" class="mr-2" @click="batchSet">批量设置</Button>
          <Button type="primary" class="mr-2" @click="addOtherCostRow">增行</Button>
          <Button type="primary" danger class="mr-2" @click="removeOtherCostRow">删行</Button>
        </template>
        <template #costType="{ row }">
          <Select
            class="w-full"
            v-model:value="row.costType"
            :options="dictStore.getDictList('FCT_MORTGAGE_TYPE')"
            placeholder="请选择"
          />
        </template>
        <template #costName="{ row }">
          <Input v-model:value="row.costName" placeholder="请输入" class="w-full" />
        </template>

        <template #costDirection="{ row }">
          <Select
            class="w-full"
            v-model:value="row.costDirection"
            :options="dictStore.getDictList('FCT_COST_DIRECTION')"
            placeholder="请选择"
          />
        </template>

        <template #amountExTax="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.amountExTax"
            placeholder="请输入"
          />
        </template>
        <template #amountIncTax="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.amountIncTax"
            placeholder="请输入"
            @change="otherCostApi.grid?.updateFooter()"
          />
        </template>
        <template #tax="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.tax"
            placeholder="请输入"
            @change="otherCostApi.grid?.updateFooter()"
          />
        </template>
        <template #taxRate="{ row }">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="row.taxRate"
            placeholder="请输入"
          />
        </template>

        <!-- 必填星号-->
        <template #costTypeHeader>
          <span><span style="color: red">*</span> 费用类型</span>
        </template>
        <template #costNameHeader>
          <span><span style="color: red">*</span> 费用名称</span>
        </template>
        <template #costDirectionHeader>
          <span><span style="color: red">*</span> 费用方向</span>
        </template>
        <template #amountIncTaxHeader>
          <span><span style="color: red">*</span> 含税金额</span>
        </template>
        <template #taxHeader>
          <span><span style="color: red">*</span> 税额</span>
        </template>
        <template #taxRateHeader>
          <span><span style="color: red">*</span> 税率(%)</span>
        </template>
        <template #amountExTaxHeader>
          <span><span style="color: red">*</span> 不含税金额</span>
        </template>
        <template #remark="{ row }">
          <Input v-model:value="row.remark" placeholder="请输入" class="w-full" />
        </template>
      </OtherCostTable>
      <Row class="mt-5">
        <Col v-bind="COL_SPAN">
          <FormItem label="其他费用分摊总金额">{{ baseForm.settleNo || '-' }}</FormItem>
        </Col>
      </Row>
      <BaseAttachmentList
        v-model="baseForm.attachmentList"
        :business-id="baseForm.id"
        business-type="FCT_OPE_OPERATION_INSPECTION"
        edit-mode
      />
    </Form>

    <WarehouseModal v-model:is-visible="visible" @confirm="handleSelectWarehouse" />
    <!-- <BatchSetModal v-model:is-visible="batchVisible" @confirm="handleSelectSettle" /> -->
  </BasicPopup>
</template>

<style scoped>
.site-input-split {
  background-color: #fff;
}

.site-input-right {
  border-left-width: 0;
}

[data-theme='dark'] .site-input-split {
  background-color: transparent;
}
</style>
