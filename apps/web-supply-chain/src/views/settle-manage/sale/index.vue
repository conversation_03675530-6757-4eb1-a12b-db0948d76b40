<script setup lang="ts">
/**
 * 销售结算单
 */
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ProjectBaseInfo } from '#/api';

import { h, onMounted, ref } from 'vue';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { ExportOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Button, message, Space, Spin, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportFile, getCompanyApi, getUserListApi, projectProposalDeleteApi, projectProposalPageApi } from '#/api';

import Create from './create.vue';
import Detail from './detail.vue';

const { getDictList } = useDictStore();

const sortKey = ref<string>('create_time');
const dataLoaded = ref(false); // 添加加载状态
const usersOptions = ref([]);
const companyOptions = ref([]);
// 获取用户列表
const getUserList = async () => {
  const res = await getUserListApi();
  Object.assign(usersOptions.value, res);
};
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};
// 同时执行两个异步请求
const loadData = async () => {
  try {
    await Promise.all([getCompanyList(), getUserList()]);
  } finally {
    dataLoaded.value = true;
  }
};

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '销售结算单编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '卖方企业',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '买方企业',
    },
    {
      component: 'Select',
      fieldName: 'executorCompanyName',
      label: '关联单据类型',
      componentProps: {
        options: getDictList('BUS_STRUCTURE'),
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '关联单据编号',
    },
    {
      component: 'Select',
      fieldName: 'businessStructure',
      label: '业务类型',
      componentProps: {
        options: getDictList('BUS_STRUCTURE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: getDictList('REVIEW_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: getDictList('PROJECT_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '核销状态',
      componentProps: {
        options: getDictList('PROJECT_REVIEW_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'DateRangePicker',
      fieldName: 'businessDate',
      label: '业务日期',
      componentProps: {
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '关联采购订单编号',
    },
  ],
  fieldMappingTime: [['businessDate', ['startDate', 'endDate'], 'x']],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'projectCode', title: '销售结算单编号' },
    {
      field: 'businessStructure',
      title: '业务类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STRUCTURE',
        },
      },
    },
    { field: 'projectName', title: '结算税额', formatter: 'formatMoney' },
    { field: 'projectName', title: '结算不含税金额', formatter: 'formatMoney' },
    { field: 'projectName', title: '结算价税合计', formatter: 'formatMoney' },
    { field: 'projectName', title: '已核销应付金额', formatter: 'formatMoney' },
    { field: 'projectName', title: '未核销应付金额', formatter: 'formatMoney' },
    { field: 'executorCompanyName', title: '买方企业' },
    { field: 'executorCompanyName', title: '卖方企业' },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '业务状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_REVIEW_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '核销状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_REVIEW_STATUS',
        },
      },
    },
    { field: 'createTime', title: '业务日期', formatter: 'formatDate' },
    { field: 'executorCompanyName', title: '所属项目名称' },
    { field: 'executorCompanyName', title: '所属项目编号' },
    {
      field: 'projectModel',
      title: '关联单据类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MODE',
        },
      },
    },
    { field: 'executorCompanyName', title: '关联单据编号' },
    { field: 'executorCompanyName', title: '关联采购订单编号' },
    { field: 'createTime', title: '创建时间', formatter: 'formatDate' },
    { field: 'createName', title: '创建人' },
    { field: 'createName', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 190,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const processedFormValues = { ...formValues };

        // 如果 purchaseMode 是数组，则转换为逗号分隔的字符串
        // if (Array.isArray(processedFormValues.purchaseMode)) {
        //   processedFormValues.purchaseMode = processedFormValues.purchaseMode.join(',');
        // }

        return await projectProposalPageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...processedFormValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
  checkboxConfig: {
    showHeader: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: ProjectBaseInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();

const access = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'audit' });
};
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data = {
        id: params.id,
      };
      access(data);
    },

    // 详情弹层
    detail: (params) => {
      const data = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data = {
        id: params.id,
        formKey: params.formKey,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});

const add = () => {
  openFormPopup(true, { pageType: 'edit' });
};
const projectType = 'initiation';
const edit = (row: ProjectBaseInfo) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const detail = (row: ProjectBaseInfo) => {
  const detailRow = { ...row, projectType, pageType: 'detail' };
  openDetailPopup(true, detailRow);
};
const createSuccess = () => {
  gridApi.formApi.submitForm();
};

const del = async (row: ProjectBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      try {
        await projectProposalDeleteApi(row.id);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch (error: any) {
        message.error(`删除失败: ${error.message}`);
      }
    },
  });
};

const handleExport = async () => {
  const row = gridApi.grid.getCheckboxRecords(true)[0];
  if (!row) {
    message.error('请选择一条数据');
    return;
  }
  await exportFile(row.id);
};
const allWriteOff = () => {};
const partWriteOff = () => {};

onMounted(() => {
  loadData();
});
</script>

<template>
  <Page auto-content-height>
    <Grid v-if="dataLoaded">
      <template #toolbar-actions>
        <Button :icon="h(PlusOutlined)" class="mr-2" type="primary" @click="add">
          {{ $t('base.add') }}
        </Button>
        <Button :icon="h(ExportOutlined)" class="mr-2" type="primary" @click="handleExport"> 应付核销 </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink v-if="row.status === 'DRAFTING' || row.status === 'UNSUBMITTED'" @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink v-if="row.status === 'DRAFTING'" type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="allWriteOff(row)"> 全部核销 </TypographyLink>
          <TypographyLink @click="partWriteOff(row)"> 部分核销 </TypographyLink>
        </Space>
      </template>
    </Grid>
    <div v-else class="flex h-64 items-center justify-center">
      <Spin size="large" />
    </div>
    <Create @register="registerForm" @ok="createSuccess" />
    <Detail @register="registerDetail" />
  </Page>
</template>

<style></style>
