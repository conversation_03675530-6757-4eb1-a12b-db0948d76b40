<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type {} from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate, formatMoney } from '@vben/utils';

import { Button, Descriptions, DescriptionsItem } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {} from '#/api';

import WriteOffDetailModal from '../components/payWriteOffDetailModal.vue';

// const emit = defineEmits(['register', 'replacement']);
const dictStore = useDictStore();
const otherCostTableRef = ref();
const settleDetailRef = ref();
const modalVisible = ref(false);
const otherCostTableData = ref([]);
const settleDetailTableData = ref([]);
const baseFormInfo = reactive({
  id: undefined,
  mortgageType: '',
  salesOrderCode: '',
  otherCostTableData: [],
  settleDetailTableData: [],
  writeOffRecordTableData: [],
  attachmentList: [],
});

const isChange = ref(false);
// 添加初始化方法
const init = (data: any) => {
  if (data && Object.keys(data).length > 0) {
    isChange.value = data.type === 'change';
    Object.assign(baseFormInfo, cloneDeep(data));
  } else {
    otherCostTableData.value = [];
    settleDetailTableData.value = [];
  }
};

const gridOtherCostTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { type: 'checkbox', width: '60px' },
    {
      field: 'projectModel',
      title: '费用类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MODE',
        },
      },
    },
    { field: 'creditorName', title: '费用名称' },
    { field: 'debtorName', title: '费用方向' },
    { field: 'receivableAmount', title: '含税金额', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '税额', formatter: 'formatDate' },
    { field: 'receivableAmount', title: '税率(%)', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '不含税金额', formatter: 'formatDate' },
    {
      field: 'projectModel',
      title: '关联源单类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MODE',
        },
      },
    },
    { field: 'receivableDueDate', title: '关联源单编号' },
    { field: 'remake', title: '备注', width: 160, slots: { default: 'remake' } },
  ],
  data: baseFormInfo.otherCostTableData,
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  showFooter: true,
  footerMethod({ columns, data }) {
    const sums: any[] = [];
    for (const [columnIndex, column] of columns.entries()) {
      if (columnIndex === 0) {
        sums.push('合计');
        continue;
      }

      const field = column.field;
      const sumFields = new Set(['amountIncTax', 'tax', 'amountExTax']);

      if (sumFields.has(field)) {
        let sum = 0;
        for (const row of data) {
          const val = Number(row[field]);
          sum += Number.isNaN(val) ? 0 : val;
        }
        sums.push(sum.toFixed(2));
      } else {
        sums.push('');
      }
    }
    return [sums];
  },
};

const gridHistoryCostTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { field: 'creditorName', title: '销售订单编号' },
    { field: 'debtorName', title: '下游企业' },
    { field: 'receivableAmount', title: '终端企业' },
    { field: 'receivableDueDate', title: '预计合作费用', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '实收合作费用', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '合作费用余额', formatter: 'formatMoney' },
    {
      field: 'projectModel',
      title: '关联源单类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MODE',
        },
      },
    },
    { field: 'receivableDueDate', title: '项目编号' },
    { field: 'receivableDueDate', title: '项目名称' },
    { field: 'remake', title: '备注' },
  ],
  data: otherCostTableData.value,
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const gridSettleDetailTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { field: 'receivableName', title: '商品名称' },
    { field: 'creditorName', title: '规格型号' },
    { field: 'debtorName', title: '商品编码' },
    { field: 'bizType', title: ' 计量单位' },
    { field: 'receivableAmount', title: '税率(%)', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '本次结算数量', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '结算不含税单价', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '结算含税单价', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '本次结算税额', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '本次结算不含税金额', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '本次结算价税合计', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '已结算不含税金额', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '已结算价税合计', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '销售订单已结算数量', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '销售订单数量', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '销售不含税单价', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '销售含税单价', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '关联单据类型', formatter: 'formatMoney' },
    { field: 'receivableAmount', title: '关联单据编号' },
    { field: 'receivableAmount', title: '业务日期', formatter: 'formatDate' },
    { field: 'receivableDueDate', title: '销售订单行号' },
  ],
  data: baseFormInfo.settleDetailTableData,
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  showFooter: true,
  footerMethod({ columns, data }) {
    const sums: any[] = [];
    for (const [columnIndex, column] of columns.entries()) {
      if (columnIndex === 0) {
        sums.push('合计');
        continue;
      }

      const field = column.field;
      const sumFields = new Set([
        'settleQty',
        'amountTax',
        'amountSettleExclTax',
        'amountSettleInclTax',
        'amountPaidExclTax',
        'amountPaidInclTax',
        'qtyOrderedPaid',
        'qtyOrdered',
      ]);

      if (sumFields.has(field)) {
        let sum = 0;
        for (const row of data) {
          const val = Number(row[field]);
          sum += Number.isNaN(val) ? 0 : val;
        }
        sums.push(sum.toFixed(2));
      } else {
        sums.push('');
      }
    }
    return [sums];
  },
};

const gridWriteOffRecordTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { field: 'receivableName', title: '应付核销单编号' },
    { field: 'creditorName', title: '当次核销金额', formatter: 'formatMoney' },
    { field: 'debtorName', title: '核销日期', formatter: 'formatDate' },
    { field: 'bizType', title: ' 关联付款记录编号' },
    { field: 'receivableDueDate', title: '备注' },
  ],
  data: baseFormInfo.writeOffRecordTableData,
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [registerPopup] = usePopupInner((data) => init(data));
const [SettleDetails] = useVbenVxeGrid({ gridOptions: gridSettleDetailTable });
const [OtherCostTable] = useVbenVxeGrid({ gridOptions: gridOtherCostTable });

const [HistoryCostTable] = useVbenVxeGrid({ gridOptions: gridHistoryCostTable });

const [WriteOffRecordTable] = useVbenVxeGrid({ gridOptions: gridWriteOffRecordTable });
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <!-- 基本信息 -->
        <DescriptionsItem label="销售结算单编号">
          {{ baseFormInfo.settleNo || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="销售结算单名称">
          {{ baseFormInfo.settleName || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="所属项目">
          {{ baseFormInfo.projectName || '-' }}
        </DescriptionsItem>
        <DescriptionsItem />
        <DescriptionsItem label="卖方企业">
          {{ baseFormInfo.sellerCompanyName || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="买方企业">
          {{ baseFormInfo.buyerCompanyName || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="统一社会信用代码">
          {{ baseFormInfo.sellerCompanyCreditCode || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="统一社会信用代码">
          {{ baseFormInfo.sellerCompanyCreditCode || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="业务类型">
          {{ dictStore.formatter(baseFormInfo.orderType, 'FCT_RECEIVABLE_POOL_ORDER_TYPE') || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="业务日期">
          {{ formatDate(baseFormInfo.receivableDueDate) || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="关联单据类型">
          {{ dictStore.formatter(baseFormInfo.orderType, 'FCT_RECEIVABLE_POOL_ORDER_TYPE') || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="备注">
          {{ baseFormInfo.remake || '-' }}
        </DescriptionsItem>
      </Descriptions>

      <BasicCaption content="结算明细" />
      <SettleDetails ref="settleDetailRef" />
      <BasicCaption content="结算明细" />
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="合作费率（%/年）">
          {{ baseFormInfo.settleNo || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="账期（天）">
          {{ baseFormInfo.settleNo || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="结算货款总金额">
          {{ baseFormInfo.settleNo || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="预估账期天数">
          {{ baseFormInfo.settleNo || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="本次结算合作费用">
          {{ baseFormInfo.settleNo || '-' }}
        </DescriptionsItem>
      </Descriptions>
      <BasicCaption content="其他费用分摊" />
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="其他费用分摊方式">
          {{ dictStore.formatter(baseFormInfo.orderType, 'FCT_RECEIVABLE_POOL_ORDER_TYPE') || '-' }}
        </DescriptionsItem>
      </Descriptions>
      <OtherCostTable ref="otherCostTableRef" />
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="其他费用分摊总金额">
          {{ formatMoney(baseFormInfo.receivableAmount) || '-' }}
        </DescriptionsItem>
      </Descriptions>

      <BasicCaption content="历史业务余额分摊" />
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="余额分摊方式">
          {{ dictStore.formatter(baseFormInfo.orderType, 'FCT_RECEIVABLE_POOL_ORDER_TYPE') || '-' }}
        </DescriptionsItem>
      </Descriptions>
      <HistoryCostTable ref="otherCostTableRef" />
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="余额分摊总金额">
          {{ formatMoney(baseFormInfo.receivableAmount) || '-' }}
        </DescriptionsItem>
      </Descriptions>

      <BasicCaption content="核销记录" />
      <WriteOffRecordTable>
        <template #toolbar-tools>
          <Button type="primary" class="mr-2" @click="() => (modalVisible = true)">核销明细</Button>
        </template>
      </WriteOffRecordTable>
      <!-- 附件信息 -->
      <BaseAttachmentList
        border="inner"
        v-model="baseFormInfo.attachmentList"
        :business-id="baseFormInfo.id"
        business-type="SCM_PROJECT"
      >
        <template #header>
          <BasicCaption content="附件信息" />
        </template>
      </BaseAttachmentList>
    </div>
    <WriteOffDetailModal v-model:is-visible="modalVisible" />
  </BasicPopup>
</template>

<style scoped>
.site-input-split {
  background-color: #fff;
}

.site-input-right {
  border-left-width: 0;
}

[data-theme='dark'] .site-input-split {
  background-color: transparent;
}
</style>
