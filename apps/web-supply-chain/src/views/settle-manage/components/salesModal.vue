<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref, watch } from 'vue';

import { defineFormOptions } from '@vben/utils';

import { Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInboundPageApi } from '#/api';

const props = defineProps({
  isVisible: {
    type: Boolean,
    required: true,
  },
  poolAssetsMin: {
    type: Number,
    default: null,
  },
  poolAssetsMax: {
    type: Number,
    default: null,
  },
  poolAssetsValidMin: {
    type: Number,
    default: null,
  },
  poolAssetsValidMax: {
    type: Number,
    default: null,
  },
  propCreditorName: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:isVisible', 'confirm']);
// 定义模态框显示状态
const visible = ref(false);

const handleCancel = () => {
  visible.value = false;
  emit('update:isVisible', false);
};

// const dictStore = useDictStore();

// 表单配置
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'receivableName',
      label: '销售订单编号',
    },
    {
      component: 'Input',
      fieldName: 'creditorName',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'debtorName',
      label: '终端企业',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

// 表格配置
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'receivableName', title: '销售订单编号', width: 280 },
    { field: 'projectName', title: '下游企业', width: 280 },
    { field: 'creditorName', title: '终端企业' },
    { field: 'debtorName', title: '预计合作费用', formatter: 'formatMoney' },
    { field: 'debtorName', title: '实收合作费用', formatter: 'formatMoney' },
    { field: 'debtorName', title: '合作费用余额', formatter: 'formatMoney' },
    { field: 'debtorName', title: '项目编号' },
    { field: 'debtorName', title: '项目名称' },
  ],
  // height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getInboundPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

watch(
  () => props.isVisible,
  (newVal) => {
    visible.value = newVal;
    if (newVal && Object.keys(gridApi.grid).length > 0) {
      gridApi.grid.commitProxy('query');
    }
  },
);
</script>

<template>
  <Modal :open="visible" title="历史业务余额明细" width="80%" @cancel="handleCancel" @ok="handleCancel">
    <Grid />
  </Modal>
</template>

<style scoped></style>
