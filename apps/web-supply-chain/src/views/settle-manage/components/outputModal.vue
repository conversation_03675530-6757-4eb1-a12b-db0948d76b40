<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref, watch } from 'vue';

import { defineFormOptions } from '@vben/utils';

import { message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInboundPageApi } from '#/api';

const props = defineProps({
  isVisible: {
    type: Boolean,
    required: true,
  },
  poolAssetsMin: {
    type: Number,
    default: null,
  },
  poolAssetsMax: {
    type: Number,
    default: null,
  },
  poolAssetsValidMin: {
    type: Number,
    default: null,
  },
  poolAssetsValidMax: {
    type: Number,
    default: null,
  },
  propCreditorName: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:isVisible', 'confirm']);
// 定义模态框显示状态
const visible = ref(false);

const handleCancel = () => {
  visible.value = false;
  emit('update:isVisible', false);
};

// const dictStore = useDictStore();

// 表单配置
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'receivableName',
      label: '关联销售订单编号',
    },
    {
      component: 'Input',
      fieldName: 'creditorName',
      label: '出库单编号',
    },
    {
      component: 'Input',
      fieldName: 'debtorName',
      label: '商品名称',
    },
    {
      component: 'Input',
      fieldName: 'debtorName',
      label: '商品编码',
    },
    {
      component: 'Input',
      fieldName: 'debtorName',
      label: '仓库',
    },
    {
      component: 'Input',
      fieldName: 'debtorName',
      label: '仓储企业',
    },
    {
      component: 'DateRangePicker',
      fieldName: 'businessDate',
      label: '业务日期',
    },
  ],
  fieldMappingTime: [['businessDate', ['startTime', 'endTime'], 'x']],
  showCollapseButton: false,
  submitOnEnter: true,
});

// 表格配置
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: 60 }, // 添加单选框列
    { field: 'receivableName', title: '商品名称', width: 280 },
    { field: 'projectName', title: '规格型号', width: 280 },
    { field: 'creditorName', title: '商品编码' },
    { field: 'debtorName', title: '计量单位' },
    { field: 'creditorName', title: '出库数量' },
    { field: 'debtorName', title: '不含税单价', formatter: 'formatMoney' },
    { field: 'debtorName', title: '含税单价', formatter: 'formatMoney' },
    { field: 'debtorName', title: '税率(%)', formatter: 'formatMoney' },
    { field: 'debtorName', title: '不含税金额', formatter: 'formatMoney' },
    { field: 'debtorName', title: '税额', formatter: 'formatMoney' },
    { field: 'debtorName', title: '含税金额', formatter: 'formatMoney' },
    { field: 'debtorName', title: '仓库名称' },
    { field: 'debtorName', title: '仓位' },
    { field: 'debtorName', title: '仓储企业' },
    { field: 'debtorName', title: '出库单编号' },
    { field: 'debtorName', title: '关联销售订单编号' },
    { field: 'debtorName', title: '业务日期', formatter: 'formatDate' },
    { field: 'debtorName', title: 'SN码' },
    { field: 'debtorName', title: '销售订单行号' },
  ],
  // height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getInboundPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 确认方法
const confirmFinanceCompare = async () => {
  const $grid = gridApi.grid;
  const rowList = $grid.getCheckboxRecords();
  // 检查是否有选中的行
  if (!rowList) {
    message.warning('请最少选择一条数据');
    return;
  }
  emit('confirm', rowList);
  // 确认后关闭模态框
  visible.value = false;
  emit('update:isVisible', false);
};

watch(
  () => props.isVisible,
  (newVal) => {
    visible.value = newVal;
    if (newVal && Object.keys(gridApi.grid).length > 0) {
      gridApi.grid.commitProxy('query');
    }
  },
);
</script>

<template>
  <Modal :open="visible" title="选择结算单据" width="80%" @cancel="handleCancel" @ok="confirmFinanceCompare">
    <Grid />
  </Modal>
</template>

<style scoped></style>
