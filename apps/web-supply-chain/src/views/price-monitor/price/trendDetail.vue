<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { PriceProductInfo } from '#/api/price-monitor';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { formatDate } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { getPriceTrendApi } from '#/api/price-monitor';

const chartRef = ref<EchartsUIType>();

const [QueryForm, { resetForm }] = useVbenForm({
  handleSubmit: (values: Record<string, any>) => {
    init({ ...formData.value, ...values });
  },
  handleReset: () => {
    resetForm();
    formData.value = {
      productName: formData.value.productName,
    };
    init(formData.value);
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'RangePicker',
      fieldName: 'rangeDate',
      label: '日期',
    },
  ],
  fieldMappingTime: [['rangeDate', ['beginDate', 'endDate']]],
  submitButtonOptions: {
    content: '查询',
  },
  wrapperClass: 'grid-cols-3 md:grid-cols-2',
});

const formData = ref<PriceProductInfo>({});
const init = async (data: PriceProductInfo) => {
  formData.value = data;
  const list = await getPriceTrendApi({
    productName: formData.value.productName,
    beginDate: formData.value.beginDate,
    endDate: formData.value.endDate,
  });
  const prices = list.map((item: PriceProductInfo) => item.price);
  const maxPrice = Math.max(...prices);
  const { renderEcharts } = useEcharts(chartRef);
  renderEcharts({
    title: {
      text: `${formData.value.productName}价格走势图`,
      left: 'center',
      textStyle: {
        fontSize: 16,
      },
    },
    grid: {
      bottom: 10,
      containLabel: true,
      left: '3%',
      right: '5%',
      top: '15%',
    },
    series: [
      {
        name: '价格',
        data: prices,
        type: 'line',
        smooth: true, // 平滑曲线
        itemStyle: {
          color: 'blue', // 数据点颜色
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#019680',
          width: 1,
        },
      },
      formatter: (params: any) => {
        return `${params[0].name}<br />价格: ${params[0].value} `;
      }, // 自定义提示框内容
    },
    xAxis: {
      type: 'category',
      data: list.map((item: PriceProductInfo) => formatDate(item.priceDate || '')),
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      splitLine: {
        lineStyle: {
          type: 'solid',
          width: 1,
        },
        show: true,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '单位：元',
        nameLocation: 'end',
        nameTextStyle: {
          padding: [0, 0, 0, 10],
        },
        axisLabel: {
          formatter: '{value}',
        },
        axisTick: {
          show: false,
        },
        max: maxPrice,
        splitArea: {
          show: true,
        },
        splitNumber: 4,
      },
    ],
  });
};
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="价格趋势" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <QueryForm />
      <BasicCaption content="搜索结果" /><br />
      <EchartsUI ref="chartRef" />
    </div>
  </BasicPopup>
</template>
