<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ContractClassifyInfo } from '#/api';

import { ref, unref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { FORM_PROP } from '@vben/constants';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addContractClassifyApi,
  deleteContractClassifyApi,
  editContractClassifyApi,
  getContractClassifyPageListApi,
} from '#/api';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'categoryName',
      label: '分类名称',
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    checkStrictly: true,
  },
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'categoryName', title: '分类名称' },
    { field: 'categoryCode', title: '分类编码' },
    { field: 'remark', title: '业务描述' },
    { field: 'updateTime', title: '更新时间' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { categoryName: string },
      ) => {
        return await getContractClassifyPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: ContractClassifyInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const modalTitle = ref('新增分类');
const loading = ref({
  save: false,
});
const classifyFormRef = ref();
const classifyForm = ref<Partial<ContractClassifyInfo>>({});
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await classifyFormRef.value.validate();
    let api = addContractClassifyApi;
    if (classifyForm.value.id) {
      api = editContractClassifyApi;
    }
    await api(unref(classifyForm) as ContractClassifyInfo);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
});
const addClassify = () => {
  classifyForm.value = {};
  modalTitle.value = '新增分类';
  modalApi.open();
};
const editTaxonomy = (row: ContractClassifyInfo) => {
  classifyForm.value = cloneDeep(row);
  modalTitle.value = '编辑分类';
  modalApi.open();
};
const delClassify = async () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '确认删除此分类？',
    async onOk() {
      await deleteContractClassifyApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const rules = {
  categoryName: [{ required: true, message: '请输入分类名称' }],
  categoryCode: [{ required: true, message: '请输入分类编码' }],
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="addClassify">新增分类</a-button>
          <a-button type="primary" danger @click="delClassify">删除</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="editTaxonomy(row)">编辑</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <Modal :title="modalTitle" :confirm-loading="loading.save">
      <a-form ref="classifyFormRef" :model="classifyForm" v-bind="FORM_PROP" :rules="rules">
        <a-form-item label="分类名称" name="categoryName">
          <a-input v-model:value="classifyForm.categoryName" />
        </a-form-item>
        <a-form-item label="分类编码" name="categoryCode">
          <a-input v-model:value="classifyForm.categoryCode" />
        </a-form-item>
        <a-form-item label="业务描述" name="remark">
          <a-textarea v-model:value="classifyForm.remark" :rows="4" />
        </a-form-item>
      </a-form>
    </Modal>
  </Page>
</template>

<style></style>
