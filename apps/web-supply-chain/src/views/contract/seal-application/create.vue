<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { WorkflowStartInfo } from '@vben/types';

import type { SealBaseInfo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { Col, DatePicker, Form, FormItem, Input, InputNumber, message, Row, Select, Textarea } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { BaseFeOrganizeSelect, BaseFeUserSelect } from '#/adapter/fe-ui';
import { detailSealApplyApi, saveSealApplyApi, updateSealApplyApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);
const { startWorkflow, WorkflowPreviewModal, initWorkflow } = useWorkflowBase();
const pageType = ref('edit');

const { getDictList } = useDictStore();
const loading = reactive({
  submit: false,
});

// 默认数据
const defaultForm: Partial<SealBaseInfo> = {
  id: undefined,
  applyCode: undefined,
  userIds: [],
  userName: undefined,
  organId: undefined,
  organName: undefined,
  usageDate: undefined,
  status: undefined,
  approvalStatus: undefined,
  description: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  sealType: undefined,
  documentName: undefined,
  documentType: undefined,
  copies: undefined,
  version: undefined,
  attachmentList: [],
  processDefinitionKey: undefined,
  startUserSelectAssignees: {},
  variables: {},
  summary: [],
  sealTypeList: [],
};

const detailForm = reactive<Partial<SealBaseInfo & WorkflowStartInfo>>(cloneDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  organId: [{ required: true, message: '请选择申请部门', trigger: 'change' }],
  userIds: [{ required: true, message: '请选择申请人', trigger: 'change' }],
  usageDate: [{ required: true, message: '请选择用印日期', trigger: 'change' }],
  copies: [{ required: true, message: '请输入用印份数', trigger: 'change', type: 'number' }],
  projectName: [{ required: true, message: '请选择用印项目', trigger: 'blur' }],
  sealTypeList: [{ required: true, message: '请选择印章种类', trigger: 'change' }],
  description: [{ required: true, message: '请输入用印事别', trigger: 'change' }],
};

const title = computed(() => {
  return detailForm.id ? '编辑' : '新增';
});

const init = async (data: SealBaseInfo & { pageType: string }) => {
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_seal_apply', businessKey: data.id });
  if (data.id) {
    const res = await detailSealApplyApi(data.id as number);
    Object.assign(detailForm, cloneDeep(res));
  } else {
    Object.assign(detailForm, cloneDeep(defaultForm));
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};

const formRef = ref();
const save = async (type: 'save' | 'SUBMITTED') => {
  try {
    await formRef.value.validate();
    changeOkLoading(true);

    if (type === 'SUBMITTED') {
      detailForm.status = 'SUBMITTED';
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      detailForm.processDefinitionKey = processDefinitionKey;
      detailForm.startUserSelectAssignees = startUserSelectAssignees;
    }

    const api = detailForm.id ? updateSealApplyApi : saveSealApplyApi;

    const submitData = detailForm as Required<SealBaseInfo>;
    const res = await api(submitData);

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch {
    message.error('保存失败，请检查网络或输入内容');
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
  close();
};

const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup" @close="close">
    <template #insertToolbar>
      <a-space>
        <a-button v-if="pageType !== 'audit'" type="primary" :loading="loading.submit" @click="save('save')">
          保存
        </a-button>
        <a-button v-if="pageType !== 'audit'" type="primary" :loading="loading.submit" @click="save('SUBMITTED')">
          提交
        </a-button>
      </a-space>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="申请部门" name="organId">
            <BaseFeOrganizeSelect v-model:value="detailForm.organId" :disabled="pageType === 'audit'" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="申请人" name="userIds">
            <BaseFeUserSelect v-model:value="detailForm.userIds" multiple :disabled="pageType === 'audit'" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="用印日期" name="usageDate">
            <DatePicker
              v-model:value="detailForm.usageDate"
              value-format="YYYY-MM-DD hh:mm:ss"
              format="YYYY-MM-DD"
              :disabled="pageType === 'audit'"
              class="w-full"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="用印份数" name="copies">
            <InputNumber
              v-model:value="detailForm.copies"
              :disabled="pageType === 'audit'"
              :controls="false"
              :precision="0"
              :step="1"
              :min="1"
              class="w-full"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="用印项目" name="projectName">
            <Input v-model:value="detailForm.projectName" :disabled="pageType === 'audit'" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="印章种类" name="sealTypeList">
            <Select
              v-model:value="detailForm.sealTypeList"
              mode="multiple"
              :options="getDictList('SEAL_APPLY_TYPE')"
              :disabled="pageType === 'audit'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="文件类型" name="documentType">
            <Select
              v-model:value="detailForm.documentType"
              :options="getDictList('SEAL_DOC_TYPE')"
              :disabled="pageType === 'audit'"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="文件名称" name="documentName">
            <Input v-model:value="detailForm.documentName" :disabled="pageType === 'audit'" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="用印事别" name="description" v-bind="FULL_FORM_ITEM_PROP">
            <Textarea v-model:value="detailForm.description" :rows="3" :disabled="pageType === 'audit'" />
          </FormItem>
        </Col>
      </Row>

      <!--   附件信息   -->
      <BaseAttachmentList
        border="inner"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_SEAL_APPLY"
        :edit-mode="pageType !== 'audit'"
      />
    </Form>
    <WorkflowPreviewModal v-if="pageType === 'edit'" />
  </BasicPopup>
</template>

<style></style>
