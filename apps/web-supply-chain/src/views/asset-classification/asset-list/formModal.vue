<script setup lang="ts">
import type { AssetClassificationInfo } from '#/api/asset-classification';

import { computed, ref } from 'vue';

import { ApiComponent, useVbenModal } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';
import { formatDate } from '@vben/utils';

import { message, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { defaultsDeep } from 'lodash-es';

import { getProjectListApi } from '#/api';
import { addAssetApi, updateAssetApi } from '#/api/asset-classification';

const dictStore = useDictStore();
const modalFormRef = ref();
const modalForm = ref<AssetClassificationInfo>({});

const modalTitle = ref('新增划分/调整分类');
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {
    const data = modalApi.getData() ?? {};
    modalTitle.value = data.id ? '编辑划分/调整分类' : '新增划分/调整分类';
    modalForm.value = defaultsDeep(data, { latestDate: formatDate(Date.now()), latestClassification: 'CLASS_NORMAL' });
    if (data.id) {
      modalForm.value.latestBasis = data.latestBasis.split(' ');
      modalForm.value.latestAdjustBasis = data.latestAdjustBasis.split(' ');
    } else {
      modalForm.value.latestDate = dayjs().format('YYYY-MM-DD HH:mm:ss');
    }
  },
  onConfirm: async () => {
    await modalFormRef.value.validate();
    let api = addAssetApi;
    if (modalForm.value.id) {
      api = updateAssetApi;
    }
    const formValue = { ...modalForm.value };
    if (Array.isArray(formValue.latestBasis)) {
      formValue.latestBasis = formValue.latestBasis.join(' ');
    }
    if (Array.isArray(formValue.latestAdjustBasis)) {
      formValue.latestAdjustBasis = formValue.latestAdjustBasis.join(' ');
    }
    try {
      modalApi.lock();
      await api(formValue);
      message.success('保存成功');
      await modalApi.close();
    } finally {
      modalApi.unlock();
    }
  },
  onBeforeClose: () => {
    modalForm.value = {};
    modalFormRef.value.resetFields();
    return true;
  },
});

const isShowDetail = computed(() => !!modalForm.value.id);

const modalRules = {
  projectCode: [{ required: true, message: '请选择项目名称' }],
  latestBasis: [{ required: true, message: '请选择划分依据' }],
  latestAdjustBasis: [{ required: true, message: '请选择调整依据' }],
};
const basisOptions = computed(() => {
  const list = dictStore.getDictList(modalForm.value.latestClassification || 'CLASS_NORMAL');
  return list.map((item) => item.dictValue);
});
const adjustOptions = computed(() => {
  const list = dictStore.getDictList('ASSET_B_CLASSIFY');
  return list.map((item) => item.dictValue);
});
const selectCompany = (_value: number, data: any) => {
  modalForm.value.projectId = data.id;
  modalForm.value.projectName = data.label;
  modalForm.value.executorCompanyName = data.executorCompanyName;
  modalForm.value.executorCompanyCode = data.executorCompanyCode;
};
</script>

<template>
  <Modal :title="modalTitle" class="w-[80vw]">
    <a-form
      ref="modalFormRef"
      :model="modalForm"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      :colon="false"
      :rules="modalRules"
    >
      <a-form-item label="项目名称" name="projectCode">
        <ApiComponent
          v-model="modalForm.projectCode"
          :component="Select"
          :api="getProjectListApi"
          label-field="projectName"
          value-field="projectCode"
          model-prop-name="value"
          :disabled="isShowDetail"
          @change="selectCompany"
        />
      </a-form-item>
      <a-form-item label="资产五级分类" name="latestClassification">
        <a-select
          v-model:value="modalForm.latestClassification"
          :options="dictStore.getDictList('ASSET_CLASSIFY_TYPE')"
          @change="modalForm.latestBasis = undefined"
        />
      </a-form-item>
      <a-form-item label="划分依据" name="latestBasis">
        <a-checkbox-group v-model:value="modalForm.latestBasis" :options="basisOptions" class="custom-checkbox-group" />
      </a-form-item>
      <a-form-item label="调整依据" name="latestAdjustBasis">
        <a-checkbox-group
          v-model:value="modalForm.latestAdjustBasis"
          :options="adjustOptions"
          class="custom-checkbox-group"
        />
      </a-form-item>
      <a-form-item label="调整说明" name="remarks">
        <a-textarea v-model:value="modalForm.remarks" :rows="3" />
      </a-form-item>
      <a-form-item label="划分/调整时间" name="latestDate">
        <a-date-picker v-model:value="modalForm.latestDate" value-format="YYYY-MM-DD HH:mm:ss" class="w-full" />
      </a-form-item>
    </a-form>
  </Modal>
</template>

<style lang="less" scoped>
.custom-checkbox-group :deep(.ant-checkbox-wrapper) {
  margin-bottom: 13px;
  position: relative;
  margin-left: 20px;
  margin-top: 5px;
}

.custom-checkbox-group :deep(.ant-checkbox + span) {
  white-space: normal;
}
.custom-checkbox-group :deep(.ant-checkbox) {
  position: absolute;
  top: 2.5px;
  left: -15px;
}
.instruction-con {
  color: #aaa;
}
</style>
