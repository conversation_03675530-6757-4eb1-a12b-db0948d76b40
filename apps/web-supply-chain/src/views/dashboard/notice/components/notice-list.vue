<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { NoticeInfo } from '@vben/types';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { usePopup } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getMyNoticePageListApi } from '#/api';

import DetailPopup from './detail-popup.vue';

const emit = defineEmits(['updateStatus']);
const dictStore = useDictStore();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'title',
      label: '标题',
    },
    {
      component: 'Select',
      fieldName: 'noticeType',
      label: '分类',
      componentProps: {
        options: dictStore.getDictList('NOTICE_TYPE'),
      },
    },
  ],
});
const gridOptions: VxeTableGridOptions<NoticeInfo> = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'title', title: '公告标题', maxWidth: 180, slots: { default: 'title' } },
    {
      field: 'noticeType',
      title: '分类',
      cellRender: { name: 'CellStatus', props: { code: 'NOTICE_TYPE' } },
      maxWidth: 100,
    },
    {
      field: 'readFlag',
      title: '状态',
      cellRender: { name: 'CellStatus', props: { code: 'NOTICE_READ_TYPE' } },
      maxWidth: 100,
    },
    { field: 'createByName', title: '发布人', maxWidth: 120 },
    { field: 'publishTime', title: '发布时间', maxWidth: 120 },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getMyNoticePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
// const noticeDetail = ref<NoticeInfo>({});
const view = async (row: NoticeInfo) => {
  // noticeDetail.value = await getMyNoticeDetailApi({ id: row.id! });
  openDetailPopup(true, row);
};
const updateStatus = () => {
  emit('updateStatus');
  gridApi.reload();
};
</script>

<template>
  <div class="h-full">
    <Grid>
      <template #title="{ row }">
        <TypographyLink @click="view(row)">{{ row.title }}</TypographyLink>
      </template>
    </Grid>
    <DetailPopup @register="registerDetail" @ok="updateStatus" />
  </div>
</template>

<style></style>
