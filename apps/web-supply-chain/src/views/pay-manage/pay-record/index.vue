<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
// import { type OperationSuperviseBaseInfo, operationSupervisePageApi, operationSuperviseDeleteApi } from '#/api';

import Create from './create.vue';
import Detail from './detail.vue';
import PayFlow from './pay-flow.vue'

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '付款记录编号',
    },
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '关联付款申请单编号',
    },
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '收款方企业',
    },
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '付款方企业',
    },
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '付款币种',
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '实际付款方式',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '业务状态',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '核销状态',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'reportCode',
      label: '所属项目名称',
    },
    {
      component: 'Input',
      fieldName: 'reportCode',
      label: '所属项目编号',
    },
    {
      component: 'RangePicker',
      fieldName: 'reportName',
      label: '计划付款日期',
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '生成方式',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
  ],
  fieldMappingTime: [
    // 将 receiptDate 数组映射到 receiptStartDate 和 receiptEndDate 字段
    ['receiptDate', ['receiptStartDate', 'receiptEndDate'], 'YYYY-MM-DD'],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'reportName', title: '付款记录编号'},
    { field: 'reportName', title: '收款方企业' },
    { field: 'projectName', title: '付款方企业' },
    { field: 'approvalStatus', title: '确认付款金额' },
    { field: 'remarks', title: '已核销付款金额' },
    { field: 'remarks', title: '未核销付款金额' },
    { field: 'remarks', title: '核销状态' },
    { field: 'remarks', title: '业务状态' },
    { field: 'remarks', title: '审批状态' },
    { field: 'remarks', title: '实际付款方式' },
    { field: 'remarks', title: '确认付款日期' },
    { field: 'remarks', title: '关联付款申请编号' },
    { field: 'remarks', title: '所属项目名称' },
    { field: 'remarks', title: '所属项目编号' },
    { field: 'remarks', title: '生成方式' },
    { field: 'remarks', title: '创建时间' },
    { field: 'remarks', title: '创建人' },
    { field: 'remarks', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await operationSupervisePageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const [registerPayFlow, { openPopup: openPayFlowPopup}] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 创建
const add = () => {
  openFormPopup(true, {});
};

// 应付核销
const payWriteOff = () => {
  // openFormPopup(true, {});
  console.log('应付核销')
};

// 编辑
const edit = (row: OperationSuperviseBaseInfo) => {
  openFormPopup(true, row);
};

// 查看
const detail = (row: OperationSuperviseBaseInfo) => {
  openDetailPopup(true, row);
};

// 删除
const del = (row: OperationSuperviseBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelete'),
    content: $t('base.deleteConfirmContent'),
    okText: $t('base.confirm'),
    cancelText: $t('base.cancel'),
    onOk: async () => {
      // 这里可以调用删除API
      await operationSuperviseDeleteApi(row.id || undefined);
      // 删除成功后刷新列表
      await gridApi.reload();
    },
  });
};

// 添加流水
const addFlow = (row: OperationSuperviseBaseInfo) => {
  openPayFlowPopup(true, row);
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
        <Button class="mr-2" type="primary" @click="payWriteOff">
          <VbenIcon class="mr-1 text-base" />
            应付核销
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="addFlow(row)">
            添加流水
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetail" @ok="editSuccess" />
    <PayFlow @register="registerPayFlow" @ok="editSuccess" />
  </Page>
</template>

<style></style>
