import { requestClient } from '#/api/request';

export async function getUserInfoByIdsApi(ids: number[]) {
  return requestClient.post('/upms/user/selector_by_ids', ids);
}
export async function getUserListByKeywordApi(params: { keyword: string }) {
  return requestClient.post('/upms/user/selector_by_keywords', {}, { params });
}
export async function getUserTreeListApi(params: { name: string; orgId: number }) {
  return requestClient.post('/upms/user/async/selector_by_org_id', {}, { params });
}
export async function validatePassword<PERSON><PERSON>(params: { password: string; userName: string }) {
  return requestClient.post('/upms/user/validate_pwd', {}, { params, responseReturn: 'body' });
}
export async function getOrgListApi(
  params: Partial<{
    code: string;
    name: string;
    parentId: number;
    type: string;
  }>,
) {
  return requestClient.get('/upms/organ/async/list', { params });
}
export async function getOrgListPyIdsApi(ids: string[]) {
  return requestClient.post('/upms/organ/list_by_ids', ids);
}
