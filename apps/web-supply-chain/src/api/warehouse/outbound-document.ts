import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface OutboundPageParams extends BaseDataParams {
  /**
   * 当前页
   */
  current?: string;
  /**
   * 每页的数量
   */
  size?: string;
  /**
   * 正排序规则
   */
  ascs?: string;
  /**
   * 倒排序规则
   */
  descs?: string;
  /**
   * 版本号
   */
  version?: string;
  /**
   * 出库单编号
   */
  outboundReceiptCode?: string;
  /**
   * 出库日期
   */
  receiptDate?: string;
  /**
   * 所属项目ID
   */
  projectId?: string;
  /**
   * 所属项目名称
   */
  projectName?: string;
  /**
   * 所属项目编号
   */
  projectCode?: string;
  /**
   * 仓库ID
   */
  warehouseId?: string;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
  /**
   * 客户企业编码
   */
  customerCompanyCode?: string;
  /**
   * 客户企业名称
   */
  customerCompanyName?: string;
  /**
   * 关联单据类型
   */
  sourceDocumentType?: string;
  /**
   * 关联签收单ID
   */
  deliveryReceiptId?: string;
  /**
   * 关联签收单显示
   */
  deliveryReceiptDisplay?: string;
  /**
   * 货款含税金额
   */
  amountWithTax?: string;
  /**
   * 已开票价税合计
   */
  invoicedAmountWithTax?: string;
  /**
   * 业务状态
   */
  status?: string;
  /**
   * 审批状态
   */
  approvalStatus?: string;
  /**
   * 开票状态
   */
  invoiceStatus?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 是否按SN码管理 (1:是, 0:否)
   */
  isSnManaged?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 创建人
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新人
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface AddOutbound extends BaseDataParams {
  /**
   * 主键
   */
  id: number;
  /**
   * 创建人
   */
  createBy: number;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新人
   */
  updateBy: number;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 版本号
   */
  version: number;
  /**
   * 出库单编号
   */
  outboundReceiptCode: string;
  /**
   * 出库日期
   */
  receiptDate: string;
  /**
   * 所属项目ID
   */
  projectId: number;
  /**
   * 所属项目名称
   */
  projectName: string;
  /**
   * 所属项目编号
   */
  projectCode: string;
  /**
   * 仓库ID
   */
  warehouseId: number;
  /**
   * 仓库编码
   */
  warehouseCode: string;
  /**
   * 仓库名称
   */
  warehouseName: string;
  /**
   * 客户企业编码
   */
  customerCompanyCode: string;
  /**
   * 客户企业名称
   */
  customerCompanyName: string;
  /**
   * 关联单据类型
   */
  sourceDocumentType: string;
  /**
   * 关联签收单ID
   */
  deliveryReceiptId: number;
  /**
   * 关联签收单显示
   */
  deliveryReceiptDisplay: string;
  /**
   * 货款含税金额
   */
  amountWithTax: number;
  /**
   * 已开票价税合计
   */
  invoicedAmountWithTax: number;
  /**
   * 业务状态
   */
  status: string;
  /**
   * 审批状态
   */
  approvalStatus: string;
  /**
   * 开票状态
   */
  invoiceStatus: string;
  /**
   * 备注
   */
  remarks: string;
  /**
   * 是否按SN码管理 (1:是, 0:否)
   */
  isSnManaged: number;
  outboundReceiptSourceRelBOS: OutboundReceiptSourceRelBOS[];
  outboundReceiptItemBOs: OutboundReceiptItemBOs[];
  deleteOutboundReceiptItemBOs: OutboundReceiptItemBOs[];
}

export interface OutboundReceiptItemBOs extends BaseDataParams {
  /**
   * 主键ID
   */
  id: number;
  /**
   * 创建人ID
   */
  createBy: number;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新人ID
   */
  updateBy: number;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 关联的出库单ID
   */
  outboundReceiptId: number;
  /**
   * 本单商品行号
   */
  itemNumber: number;
  /**
   * 商品名称
   */
  productName: string;
  /**
   * 商品编码
   */
  productCode: string;
  /**
   * 商品别名
   */
  productAlias: string;
  /**
   * 计量单位
   */
  measureUnit: string;
  /**
   * 规格文字描述
   */
  specifications: string;
  /**
   * 产地/厂家
   */
  originName: string;
  /**
   * 品牌名称
   */
  brandName: string;
  /**
   * 本次出库数量
   */
  quantity: number;
  /**
   * 出库仓库ID
   */
  warehouseId: number;
  /**
   * SN码/序列号 (多个用逗号分隔)
   */
  serialNumbers: string;
  /**
   * 关联单据类型
   */
  sourceDocumentType: string;
  /**
   * 源单据ID (如采购订单ID, 销售退货单ID)
   */
  sourceDocumentId: number;
  /**
   * 源单据编号 (冗余)
   */
  sourceDocumentDisplay: string;
  /**
   * 源单据商品行ID (如采购订单行ID)
   */
  sourceDocumentItemId: number;
  /**
   * 源单据商品行编号(如采购订单行ID)
   */
  sourceDocumentItemDisplay: number;
  /**
   * 备注
   */
  remarks: string;
  /**
   * 版本号
   */
  version: number;
}

/**
 * 新增的 outboundReceiptSourceRelBOS 类型
 */
export interface OutboundReceiptSourceRelBOS extends BaseDataParams {
  /**
   * 主键
   */
  id: number;
  /**
   * 创建人
   */
  createBy: number;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新人
   */
  updateBy: number;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 版本号
   */
  version: number;
  /**
   * 出库单据ID
   */
  outboundReceiptId: number;
  /**
   * 来源单据ID
   */
  sourceDocumentId?: number;
  /**
   * 来源单据展示编号
   */
  sourceDocumentDisplay: string;
  /**
   * 来源单据类型
   */
  sourceDocumentType: string;
}

// 出库列表分页查询
export async function getOutboundPageApi(params: PageListParams) {
  return requestClient.get<OutboundPageParams[]>('/scm/outbound/receipt/page', { params });
}

// 新增出库信息
export async function addOutboundApi(data: AddOutbound) {
  return requestClient.post<AddOutbound>('/scm/outbound/receipt/add', data);
}

// 出库信息删除
export async function outboundDeleteApi(id: string) {
  return requestClient.post(`/scm/outbound/receipt/delete/${id}`);
}

// 出库信息作废
export async function outboundCancelApi(id: string) {
  return requestClient.post(`/scm/outbound/receipt/cancel/${id}`);
}

// 编辑出库信息
export async function editOutboundApi(data: AddOutbound) {
  return requestClient.post<AddOutbound>('/scm/outbound/receipt/edit', data);
}

// 项目详情查看
export async function outboundDetailApi(id: string) {
  return requestClient.get<AddOutbound>(`/scm/outbound/receipt/detail/${id}`);
}

// 出库信息提交
export async function outboundSubmitApi(id: string) {
  return requestClient.post(`/scm/outbound/receipt/submit/${id}`);
}

// 出库列表条件查询
export async function getOutboundListApi(id: string) {
  return requestClient.get(`/scm/outbound/receipt/list/${id}`);
}
