import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
import type { ProjectPageParams, ProjectBaseInfo, MeetingInfo } from '#/api';

// 支委申请分页查询
export async function projectBranchPageApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/partyBranch/page', { params });
}

// 支委会编辑
export async function projectBranchEditApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/partyBranch/edit', data);
}

// 支委会详情查询
export async function projectBranchDetailApi(code: number) {
  return requestClient.get<ProjectBaseInfo>(`/scm/project/partyBranch/detail/${code}`);
}

// 项目信息删除
export async function projectBranchDeleteApi(id: number) {
  return requestClient.post(`/scm/project/partyBranch/delete/${id}`);
}

// 项目信息作废
export async function projectBranchCancelApi(id: number) {
  return requestClient.post(`/scm/project/partyBranch/cancel/${id}`);
}

// 项目信息提交
export async function projectBranchSubmitApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/partyBranch/submit', data);
}

// 项目条件查询
export async function projectBranchListApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/partyBranch/list', { params });
}

// 支委会上传会议纪要
export async function projectBranchUploadSummaryApi(data: MeetingInfo) {
  return requestClient.post<MeetingInfo>('/scm/project/partyBranch/upload/summary', data);
}
