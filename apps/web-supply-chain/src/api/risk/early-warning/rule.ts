import { requestClient } from '#/api/request';

export interface EarlyWarningRuleInfo {
  // 触发条件描述
  conditionDesc?: string;
  // 主键
  id?: number;
  // 是否短信提醒
  isSms?: number;
  // 通知角色
  notifyRole?: string;
  // 预警规则描述
  ruleDesc?: string;
  // 预警规则名称
  ruleName?: string;
  // 短信接收人用户ID
  smsUserIdList?: number[];
  // 短信接收人用户名称
  smsUserNames?: string;
  // 短信接收人手机号
  smsUserNumbers?: string;
  // 状态
  status?: string;
  // 触发条件值
  value?: number;
  // 触发条件类型
  valueType?: string;
  // 版本号
  version?: number;
  // 预警等级
  warningLevel?: string;
  [property: string]: any;
}
export async function getEarlyWarningRuleListApi() {
  return requestClient.get<EarlyWarningRuleInfo[]>('/scm/risk/warn/rule/list');
}
export async function addEarlyWarningRuleApi(data: EarlyWarningRuleInfo) {
  return requestClient.post('/early-warning/rule/add', data);
}
export async function editEarlyWarningRuleApi(data: EarlyWarningRuleInfo) {
  return requestClient.post('/scm/risk/warn/rule/update', data);
}
