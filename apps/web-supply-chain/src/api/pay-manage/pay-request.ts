import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface PayRequestPageParams extends BaseDataParams {

}

export interface PayRequestBaseInfo extends BaseDataParams {

}

// 分页查询 todo
export async function projectReviewPageApi(params: PageListParams) {
  return requestClient.get<PayRequestPageParams[]>('/scm/project/review/page', { params });
}

// 创建 todo
export async function projectReviewAddApi(data: PayRequestBaseInfo) {
  return requestClient.post<PayRequestBaseInfo>('/scm/project/review/add', data);
}

// 变更 todo
export async function projectReviewEditApi(data: PayRequestBaseInfo) {
  return requestClient.post<PayRequestBaseInfo>('/scm/project/review/edit', data);
}

// 查询 todo
export async function projectReviewDetailApi(code: number) {
  return requestClient.get<PayRequestBaseInfo>(`/scm/project/review/detail/${code}`);
}

// 删除 todo
export async function projectReviewDeleteApi(id: number) {
  return requestClient.post(`/scm/project/review/delete/${id}`);
}

// 作废 todo
export async function projectReviewCancelApi(id: number) {
  return requestClient.post(`/scm/project/review/cancel/${id}`);
}

// 提交 todo
export async function projectReviewSubmitApi(id: number) {
  return requestClient.post(`/scm/project/review/submit/${id}`);
}

// 条件查询 todo
export async function projectReviewListApi(params: PageListParams) {
  return requestClient.get<PayRequestPageParams[]>('/scm/project/review/list', { params });
}

