import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface EvaluatePageParams extends BaseDataParams {
  /** 当前页 */
  current?: string;
  /** 每页的数量 */
  size?: string;
  /** 正排序规则 */
  ascs?: string;
  /** 倒排序规则 */
  descs?: string;
  /** 主键 */
  id?: string;
  /** 创建时间 */
  createTime?: string;
  /** 创建人ID */
  createBy?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 修改人ID */
  updateBy?: string;
  /** 标记删除 */
  deleteFlag?: string;
  /** 项目后评价编号 */
  reportCode?: string;
  /** 项目后评价名称 */
  reportName?: string;
  /** 关联的项目ID */
  projectId?: string;
  /** 关联的项目名称 */
  projectName?: string;
  /** 结清日期 */
  settlementDate?: string;
  /** 审批状态 */
  status?: string;
  /** 状态 */
  approvalStatus?: string;
  /** 备注 */
  remark?: string;
}

export interface EvaluateBaseInfo extends BaseDataParams {
  // 项目后评价编号
  reportCode?: string;
  // 项目后评价名称
  reportName?: string;
  // 关联的项目ID
  projectId?: number;
  // 关联的项目名称
  projectName?: string;
  // 结清日期
  settlementDate?: string;
  // 审批状态
  status?: string;
  // 状态
  approvalStatus?: string;
  // 备注
  remark?: string;
  // 附件
  attachmentList: AttachmentList[];
}

export interface AttachmentList {
  // 主键
  id: number | undefined;
}

// 分页查询
export async function evaluatePageApi(params: PageListParams) {
  return requestClient.get<EvaluatePageParams[]>('/scm/project/evaluate/page', { params });
}

// 新增项目后评价
export async function evaluateAddApi(data: EvaluateBaseInfo) {
  return requestClient.post('/scm/project/evaluate/add', data);
}

// 编辑项目后评价
export async function evaluateEditApi(data: EvaluateBaseInfo) {
  return requestClient.post<EvaluateBaseInfo>('/scm/project/evaluate/edit', data);
}

// 删除项目后评价
export async function evaluateDeleteApi(id: number) {
  return requestClient.post(`/scm/project/evaluate/delete/${id}`);
}

// 批量下载
export async function downloadZipApi(params: { ids: string }) {
  return requestClient.get('/infra/attach/zip-link', { params });
}
