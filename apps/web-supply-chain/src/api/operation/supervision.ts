import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
import type { AttachmentList } from '#/api';

export interface OperationSupervisePageParams extends BaseDataParams {
  // 当前页
  current?: string;
  // 每页的数量
  size?: string;
  // 正排序规则
  ascs?: string;
  // 倒排序规则
  descs?: string;
  // 主键
  id?: string;
  // 创建时间
  createTime?: string;
  // 创建人ID
  createBy?: string;
  // 修改时间
  updateTime?: string;
  // 修改人ID
  updateBy?: string;
  // 标记删除
  deleteFlag?: string;
  // 报告编号
  reportCode?: string;
  // 报告名称
  reportName?: string;
  // 项目Id
  projectId?: string;
  // 项目名称
  projectName?: string;
  // 业务状态
  status?: string;
  // 审批状态
  approvalStatus?: string;
  // 报告日期
  reportDate?: string;
  // 备注
  remarks?: string;
}

export interface OperationSuperviseBaseInfo extends BaseDataParams {
  // 主键
  id?: number;
  // 报告编号
  reportCode?: string;
  // 报告名称
  reportName?: string;
  // 项目Id
  projectId?: number;
  // 项目名称
  projectName?: string;
  // 业务状态
  status?: string;
  // 审批状态
  approvalStatus?: string;
  // 报告日期
  reportDate?: string;
  // 关联检查id
  inspectionId?: string | string[];
  // 备注
  remarks?: string;
  // 附件
  attachmentList?: AttachmentList[];
}

export interface InspectionInfo {
  id?: number;
  reportName?: string;
  value?: string;
  [key: string]: any; // 允许任意属性
}

// 分页查询
export async function operationSupervisePageApi(params: PageListParams) {
  return requestClient.get<OperationSupervisePageParams[]>('/scm/operation/supervise/page', { params });
}

// 新建
export async function operationSuperviseAddApi(data: OperationSuperviseBaseInfo) {
  return requestClient.post('/scm/operation/supervise/add', data);
}

// 编辑
export async function operationSuperviseEditApi(data: OperationSuperviseBaseInfo) {
  return requestClient.post<OperationSuperviseBaseInfo>('/scm/operation/supervise/edit', data);
}

// 详情
export async function operationSuperviseDetailApi(code: number) {
  return requestClient.get<OperationSuperviseBaseInfo>(`/scm/operation/supervise/detail/${code}`);
}

// 删除
export async function operationSuperviseDeleteApi(id: number) {
  return requestClient.post(`/scm/operation/supervise/delete/${id}`);
}

// 作废
export async function operationSuperviseCancelApi(id: number) {
  return requestClient.post(`/scm/operation/supervise/cancel/${id}`);
}

// 提交
export async function operationSuperviseSubmitApi(data: OperationSuperviseBaseInfo) {
  return requestClient.post('/scm/operation/supervise/submit', data);
}

// 关联运营检查
export async function getInspectionListApi(params: OperationSupervisePageParams) {
  return requestClient.get<InspectionInfo[]>('/scm/operation/inspection/list', { params });
}
