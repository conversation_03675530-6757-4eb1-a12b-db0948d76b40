import type { PageListParams } from '@vben/types';

import type { ContractInfo } from '#/api';

import { requestClient } from '#/api/request';

// export async function getContractSignPageListApi(params: PageListParams) {
//   return requestClient.get('/base/contract/pageList', { params });
// }
export async function getContractAuditPageListApi(params: PageListParams) {
  return requestClient.get('/base/contract/approval/page', { params });
}
// export async function saveContractSignApi(data: ContractInfo) {
//   return requestClient.post('/base/contract/save', data);
// }
export async function submitContractSignApi(data: ContractInfo) {
  return requestClient.post('/base/contract/submit', data);
}
export async function revokeContractSignApi(id: number) {
  return requestClient.post('/base/contract/revoke', {}, { params: { id } });
}
export async function deleteContractSignApi(id: number) {
  return requestClient.post('/base/contract/delete', {}, { params: { id } });
}
export async function completeContractSignApi(data: { completedDate: Date; fileCompleteId: number; id: number }) {
  return requestClient.post('/base/contract/complete', data);
}
export async function cancelContractSignApi(data: { cancelReason: string; id: number }) {
  return requestClient.post('/base/contract/cancel', data);
}
// export async function getContractSignInfoApi(params: { id: number }) {
//   return requestClient.get('/base/contract/info', { params });
// }
export async function mergeSubmitContractApi(data: {
  contractIds: number[];
  processDefinitionKey?: string;
  startUserSelectAssignees?: any;
}) {
  return requestClient.post('/base/contract/approval/submit', data);
}
export async function getContractAuditDetailApi(params: { id: number }) {
  return requestClient.get('/base/contract/approval/detail', { params });
}
export async function confirmContractApi(data: ContractInfo) {
  return requestClient.post('/base/contract/submit', data);
}
