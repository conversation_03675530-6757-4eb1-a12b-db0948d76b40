import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface CompanylimitInfo {
  id?: string;
  // 授信编号
  creditCode?: string;
  // 企业额度管理编号
  limitCode?: string;
  // 额度主体code
  creditCompanyCode?: string;
  // 额度主体名称
  creditCompanyName?: string;
  // 授信类型
  creditType?: string;
  // 企业额度上限
  creditLimit?: string;
  // 额度到期日
  expiryDate?: string;
  // 状态
  status?: string;
  // 审批状态
  approvalStatus?: string;
  // 企业额度上限 (总额度)
  totalLimit?: string;
  // 已用额度
  usedLimit?: string;
  // 冻结额度
  frozenLimit?: string;
  // 可用额度
  availableLimit?: string;
  // 累计用款金额
  cumulativeUsage?: string;
  // 累计回款金额
  cumulativeRepayment?: string;
  // 累计项目用款
  totalOutAmount?: string;
  // 累计项目回款
  totalBackAmount?: string;
  remark?: string;
  projectName?: string;
  projectCode?: string;
  // 担保企业
  guaranteeCompanyName?: string;
  guaranteeCompanyCode?: string;
  usedMonthLimitBOList?: { limitAmount: number; month: string }[];
  releaseMonthLimitBOList?: { limitAmount: number; month: string }[];
}
/* 企业授信管理*/
export async function getCreditListApi(params: PageListParams) {
  return requestClient.get('/scm/credit/list', { params });
}
export async function addCreditApi(params: CompanylimitInfo) {
  return requestClient.post('/scm/credit/save', params);
}
export async function updateCreditApi(params: CompanylimitInfo) {
  return requestClient.post('/scm/credit/update', params);
}
export async function deleteCreditApi(id: string) {
  return requestClient.post(`/scm/credit/delete?id=${id}`);
}
export async function detailCreditApi(params: { id: string }) {
  return requestClient.get('/scm/credit/detail', { params });
}

/* 企业额度信息*/
export async function getCompanyLogApi(params: PageListParams) {
  return requestClient.get('/scm/limit/credit/log/list', { params });
}
export async function getCompanyListApi(params: PageListParams) {
  return requestClient.get('/scm/limit/credit/list', { params });
}
export async function addCompanyApi(params: CompanylimitInfo) {
  return requestClient.post('/scm/limit/credit/save', params);
}
export async function updateCompanyApi(params: CompanylimitInfo) {
  return requestClient.post('/scm/limit/credit/update', params);
}
export async function deleteCompanyApi(id: string) {
  return requestClient.post(`/scm/limit/credit/delete?id=${id}`);
}
export async function detailCompanyApi(params: { id: string }) {
  return requestClient.get('/scm/limit/credit/detail', { params });
}
export async function getCompanyRecordApi(params: PageListParams) {
  // 查询企业列表
  return requestClient.get('/base/company/list', { params });
}
/* 项目额度信息*/
export async function getProjectListApi(params: PageListParams) {
  return requestClient.get('/scm/limit/project/list', { params });
}
export async function detailProjectApi(params: { id: string }) {
  return requestClient.get('/scm/limit/project/detail', { params });
}
export async function getProjectLogApi(params: PageListParams) {
  return requestClient.get('/scm/limit/project/list/log', { params });
}
