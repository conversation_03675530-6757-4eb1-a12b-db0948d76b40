import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// 产品信息
export interface PriceProductInfo {
  id?: string;
  // 状态
  status?: string;
  // 商品名称
  productName?: string;
  // 商品别名
  productAlias?: string;
  // 规格型号
  skuCode?: string;
  // 商品编码
  productCode?: string;
  // 计量单位
  measureUnit?: string;
  // 商品品牌
  brandName?: string;
  // 生产厂家
  manufacturerName?: string;
  priceDate?: string;
  price?: string;
  beginDate?: string;
  endDate?: string;
  categoryName?: string;
  createTime?: string;
  productId: string | number;
}

export async function getPriceListApi(params: PageListParams) {
  return requestClient.get('/base/product/monitor/list', { params });
}
export async function getPriceTrendApi(params: PriceProductInfo) {
  return requestClient.post('/base/product/monitor/trend', params);
}
export async function addPriceApi(params: PriceProductInfo) {
  return requestClient.post('/base/product/monitor/add', params);
}

export async function getPriceDetailApi(id: string) {
  return requestClient.get(`/base/product/monitor/details`, { params: { id } });
}

export async function deletePriceApi(params: { productId?: string | number; id?: string | number }) {
  return requestClient.post(`/base/product/monitor/delete`, params);
}
