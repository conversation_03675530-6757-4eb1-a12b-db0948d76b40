import type { ScoringAdjustRuleBO, ScoringIndicatorRuleBO } from '#/api';

import { requestClient } from '#/api/request';

// RestResponseScoringModelVO，返回信息
export interface IndicatorConfigInfo {
  // 状态码
  code: number;
  data?: ScoringModelVO;
  // 返回消息
  msg: string;
  [property: string]: any;
}

// ScoringModelVO，数据
export interface ScoringModelVO {
  // 调整规则列表
  adjustRules?: ScoringAdjustRuleBO[];
  // 评分分类及指标树
  categories?: ScoringCategoryBO[];
  // 评分描述
  description?: string;
  // 测算评定标准
  limitRules?: ScoringLimitRuleBO[];
  // 评分模型ID
  id?: number;
  // 评分模型状态，如果为0，表示不是最新版本，如果为1，表示最新版本
  state?: number;
  [property: string]: any;
}

// ScoringCategoryBO，评分分类及指标树
export interface ScoringCategoryBO {
  // 分类名称 (例如: 基础分, 加分项, 减分项)
  categoryName?: string;
  // 主键
  id?: number;
  // 该分类下的指标列表
  indicators?: ScoringIndicatorBO[];
  // 显示顺序
  sortCode?: number;
  [property: string]: any;
}

// ScoringIndicatorBO，该分类下的指标列表
export interface ScoringIndicatorBO {
  // 主键
  id?: number;
  // 指标名称
  indicatorName?: string;
  // 该指标下的规则列表
  indicatorRules?: ScoringIndicatorRuleBO[];
  // INPUT=手动输入, SELECT=下拉选择, RANGE=范围匹配
  inputType?: string;
  // 最大分限制
  maxScore?: number;
  // 符号类型 (0:不限制, 1:必须为零或正分, -1:必须为零或负分)
  signType?: number;
  // 排序
  sortCode?: number;
  // 权重
  weight?: number;
  [property: string]: any;
}

// ScoringLimitRuleBO，测算评定标准
export interface ScoringLimitRuleBO {
  // 配置Id
  configId?: number;
  // 主键
  id?: number;
  // 最大授信上限
  maxLimit?: number;
  // 数值范围下限 (包含)
  maxScore?: number;
  // 数值范围上限 (不包含)
  minScore?: number;
  // 规则描述 (例如: 10≤总分<12)
  ruleName?: string;
  // 显示顺序
  sortCode?: number;
  [property: string]: any;
}

export async function getIndicatorConfigDetailApi() {
  return requestClient.get<IndicatorConfigInfo>('/scm/scoring/config/model/detail');
}
export async function saveIndicatorConfigApi(data: ScoringModelVO) {
  return requestClient.post('/scm/scoring/config/model/save', data);
}
