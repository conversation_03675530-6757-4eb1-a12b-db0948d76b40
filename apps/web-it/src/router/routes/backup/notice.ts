import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'streamline:log',
      title: $t('page.notice.title'),
    },
    name: 'Notice',
    path: '/notice',
    children: [
      {
        name: 'NoticeManage',
        path: 'manage',
        component: () => import('#/views/notice/manage/index.vue'),
        meta: {
          icon: 'icon-park-outline:log',
          title: $t('page.notice.manage'),
        },
      },
    ],
  },
];
export default routes;
