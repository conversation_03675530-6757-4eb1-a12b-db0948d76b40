import type { BaseDataParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface ButtonPermissionInfo extends BaseDataParams {
  // 名称
  name?: string;
  // 编码
  code?: string;
  // 关联功能id
  moduleId?: string;
  // 扩展属性
  propertyJson?: string;
  // 描述
  description?: string;
  // 有效标志(0-禁用，1-启用)
  enabled?: number;
  // 排序
  sortCode?: number;
}

export async function getButtonPermissionListApi(params: { moduleId: string }) {
  return requestClient.get('/upms/module/button/list', { params });
}
export async function addButtonPermissionApi(data: ButtonPermissionInfo) {
  return requestClient.post('/upms/module/button/add', data);
}
export async function editButtonPermissionApi(data: ButtonPermissionInfo) {
  return requestClient.post('/upms/module/button/edit', data);
}
export async function delButtonPermissionApi(id: string) {
  return requestClient.post('/upms/module/button/delete', {}, { params: { id } });
}
