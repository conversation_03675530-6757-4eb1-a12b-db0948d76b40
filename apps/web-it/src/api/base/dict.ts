import type { RequestClientConfig } from '@vben/request';
import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface DictTypeInfo {
  id: string;
  code: string;
  name: string;
  sortCode: number;
  type: string;
  isTag: number;
  iconType: number;
  tag: string;
}

export interface DictInfo {
  id: string;
  code: string;
  dictName: string;
  dictValue: string;
  sortCode: number;
  enabled: number;
  isEdit: number;
  dictColor: string;
  permission: string;
  dictIcon: string;
  type: string;
}

export interface DictTagInfo {
  id?: number;
  name: string;
}

export async function getDictTypePageListApi(params: PageListParams) {
  return requestClient.get('/upms/dict/type/page', { params });
}
export async function addDictTypeApi(data: DictTypeInfo) {
  return requestClient.post('/upms/dict/type/add', data);
}
export async function editDictTypeApi(data: DictTypeInfo) {
  return requestClient.post('/upms/dict/type/edit', data);
}
export async function delDictTypeApi(id: string) {
  return requestClient.post('/upms/dict/type/delete', {}, { params: { id } });
}
export async function getDictItemListApi(params: { code: string }) {
  return requestClient.get('/upms/dict/item/list', { params });
}
export async function addDictItemApi(data: DictInfo) {
  return requestClient.post('/upms/dict/item/add', data);
}
export async function editDictItemApi(data: DictInfo) {
  return requestClient.post('/upms/dict/item/edit', data);
}
export async function delDictItemApi(id: string) {
  return requestClient.post('/upms/dict/item/delete', {}, { params: { id } });
}
export async function getDictTypeBizPageListApi(params: PageListParams) {
  return requestClient.get('/upms/dict/type/biz/page', { params });
}
export async function getDictItemBizListApi(params: { code: string }) {
  return requestClient.get('/upms/dict/item/biz/list', { params });
}
export async function addDictItemBizApi(data: DictInfo) {
  return requestClient.post('/upms/dict/item/biz/add', data);
}
export async function editDictItemBizApi(data: DictInfo) {
  return requestClient.post('/upms/dict/item/biz/edit', data);
}
export async function delDictItemBizApi(id: string) {
  return requestClient.post('/upms/dict/item/biz/delete', {}, { params: { id } });
}
export async function clearDictCacheApi() {
  return requestClient.get('/upms/dict/clear_cache');
}
export async function getDictTagListApi() {
  return requestClient.get('/upms/dict/tag/list');
}
export async function addDictTagApi(data: { name: string }) {
  return requestClient.post('/upms/dict/tag/add', data);
}
export async function delDictTagApi(id: number) {
  return requestClient.post('/upms/dict/tag/delete', {}, { params: { id } });
}
export async function exportDictItemApi() {
  return requestClient.downloadAndSave('/upms/dict/export');
}
export async function importDictItemApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/upms/dict/import', data, config);
}
