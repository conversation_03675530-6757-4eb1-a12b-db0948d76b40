import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
/**
 * RegionEditRequest，编辑行政区划
 */
export interface RegionInfo {
  // 城市
  city?: string;
  // 城市代码
  cityCode?: string;
  // 城市拼音
  cityPinyin?: string;
  // 城市简称
  cityShortName?: string;
  // 地区
  district?: string;
  // 地区拼音
  districtPinyin?: string;
  // 地区简称
  districtShortName?: string;
  // 拼音首字母
  firstChar?: string;
  // 主键
  id?: string;
  // 拼音简写大写
  jianpin?: string;
  // 纬度
  lat?: string;
  // 级别类型
  levelType?: string;
  // 经度
  lng?: string;
  // 名称
  name?: string;
  // 父节点id
  parentId?: string;
  // 父节点路径
  parentPath?: string;
  // 拼音
  pinyin?: string;
  // 省份
  province?: string;
  // 省份拼音
  provincePinyin?: string;
  // 省份简称
  provinceShortName?: string;
  // 备注1
  remark1?: string;
  // 备注2
  remark2?: string;
  // 短名称
  shortName?: string;
  // 邮政编码
  zipCode?: string;
  [property: string]: any;
}
export async function getRegionPageListApi(params: PageListParams) {
  return requestClient.get('/upms/region/page', { params });
}
export async function getRegionListApi(params: { levelType?: string; name?: string; parentId?: string }) {
  return requestClient.get('/upms/region/list', { params });
}
export async function addRegionApi(data: RegionInfo) {
  return requestClient.post('/upms/region/add', data);
}
export async function editRegionApi(data: RegionInfo) {
  return requestClient.post('/upms/region/edit', data);
}
export async function deleteRegionApi(id: string) {
  return requestClient.post('/upms/region/delete', {}, { params: { id } });
}
