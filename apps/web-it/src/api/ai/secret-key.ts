import type { BaseDataParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface AiSecretKeyInfo extends BaseDataParams {
  // API密钥名称
  name: string;
  // API密钥
  apiKey?: string;
  // 所属平台
  platform:
    | 'DEEP_SEEK'
    | 'DOU_BAO'
    | 'HUN_YUAN'
    | 'MINI_MAX'
    | 'MOONSHOT'
    | 'OPENAI'
    | 'SILICON_FLOW'
    | 'TONG_YI'
    | 'XING_HUO'
    | 'YI_YAN'
    | 'ZHI_PU';
  // 可选, API服务端点地址
  url?: string;
  // 启用状态（0-禁用 1-启用）
  enabled: 0 | 1;
}

export function getAiSecretKeyPageListApi(params: { page: string }) {
  return requestClient.get('/ai/apiKey/page', { params });
}
export function getAiSecretKeyListApi() {
  return requestClient.get('/ai/apiKey/simple-list');
}
export function addAiSecretKeyApi(data: AiSecretKeyInfo) {
  return requestClient.post('/ai/apiKey/create', data);
}
export function editAiSecretKeyApi(data: AiSecretKeyInfo) {
  return requestClient.post('/ai/apiKey/update', data);
}
export function deleteAiSecretKeyApi(id: string) {
  return requestClient.post('/ai/apiKey/delete', {}, { params: { id } });
}
