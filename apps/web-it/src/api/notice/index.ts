import type { NoticeInfo, PageListParams, Pagination } from '@vben/types';

import { requestClient } from '#/api/request';

export function getNoticePageListApi(params: PageListParams) {
  return requestClient.get<Pagination<NoticeInfo>>('/upms/notice/manage/page', { params });
}
export function saveNoticeApi(data: NoticeInfo) {
  return requestClient.post<number>('/upms/notice/manage/save', data);
}
export function getNoticeDetailApi(params: { id: number }) {
  return requestClient.get<NoticeInfo>('/upms/notice/manage/info', { params });
}
export function deleteNoticeApi(data: number[]) {
  return requestClient.post('/upms/notice/manage/delete', data);
}
