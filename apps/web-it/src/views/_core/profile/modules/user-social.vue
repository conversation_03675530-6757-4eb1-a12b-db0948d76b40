<script setup lang="tsx">
import type { UserSocialInfo } from '#/api';

import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { DocumentIcons } from '@vben/base-ui';
import { confirm } from '@vben/common-ui';
import { SystemUserSocialTypeEnum } from '@vben/constants';
import { useDictStore } from '@vben/stores';

import { Button, Card, Image, message } from 'ant-design-vue';

import { bindUserSocialApi, getUserSocialListApi, unbindUserSocialApi } from '#/api';

const emit = defineEmits<{
  (e: 'update:activeName', v: string): void;
}>();

const route = useRoute();
const dictStore = useDictStore();
const { wechatEnterpriseImg } = DocumentIcons;
/** 已经绑定的平台 */
const bindList = ref<UserSocialInfo[]>([]);
const allBindList = computed<any[]>(() => {
  return Object.values(SystemUserSocialTypeEnum).map((social) => {
    const socialUser = bindList.value.find((item) => item.source === social.type);
    return {
      ...social,
      socialUser,
    };
  });
});

/** 解绑账号 */
function onUnbind(row: { socialUser: UserSocialInfo; source: string; type: string }) {
  confirm({
    content: `确定解绑此账号吗？`,
  }).then(async () => {
    await unbindUserSocialApi({ source: row.source });
    // 提示成功
    message.success('解绑成功');
    await getUserBindSocialList();
  });
}

/** 绑定账号（跳转授权页面） */
async function onBind(bind: any) {
  const type = bind.type;
  if (type <= 0) {
    return;
  }
  try {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('source', type);
    const redirectUri = currentUrl.toString();
    // 进行跳转
    window.location.href = `${import.meta.env.VITE_GLOB_API_URL}/auth/oauth/render/${type}?redirectUri=${redirectUri}`;
  } catch (error) {
    console.error('社交绑定处理失败:', error);
  }
}

/** 监听路由变化，处理社交绑定回调 */
async function bindSocial() {
  // 社交绑定
  // const type = Number(getUrlValue('type'));
  const code = route.query.code as string;
  const state = route.query.state as string;
  const source = route.query.source as string;
  if (!code) {
    return;
  }
  try {
    await bindUserSocialApi({ source, code, state });
    // 提示成功
    message.success('绑定成功');
    emit('update:activeName', 'userSocial');
    await getUserBindSocialList();
  } finally {
    // 清理 URL 参数，避免刷新重复触发
    window.history.replaceState({}, '', location.pathname);
  }
}
const getUserBindSocialList = async () => {
  bindList.value = await getUserSocialListApi();
};
getUserBindSocialList();

const img: Record<string, string> = {
  wechat_enterprise: wechatEnterpriseImg,
};

/** 初始化 */
onMounted(() => {
  bindSocial();
});
</script>

<template>
  <div class="flex flex-col">
    <div class="pb-3">
      <div
        class="grid grid-cols-1 gap-2 px-2 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-3"
      >
        <Card v-for="item in allBindList" :key="item.type" class="!mb-2">
          <div class="flex w-full items-center gap-4">
            <Image :src="img[item.source]" :width="40" :height="40" :alt="item.title" :preview="false" />
            <div class="flex flex-1 items-center justify-between">
              <div class="flex flex-col">
                <h4 class="mb-1 text-sm text-black/85 dark:text-white/85">
                  {{ dictStore.formatter(item.type, 'systemSocialType') }}
                </h4>
                <span class="text-black/45 dark:text-white/45">
                  <template v-if="item.socialUser">
                    {{ item.socialUser?.username || item.socialUser?.nickname || item.socialUser?.openid }}
                  </template>
                  <template v-else>
                    绑定
                    {{ dictStore.formatter(item.type, 'systemSocialType') }}
                    账号
                  </template>
                </span>
              </div>
              <Button v-if="!item.socialUser" size="small" type="link" @click="onBind(item)"> 绑定 </Button>
              <Button v-else size="small" type="link" @click="onUnbind(item)"> 解绑 </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>
