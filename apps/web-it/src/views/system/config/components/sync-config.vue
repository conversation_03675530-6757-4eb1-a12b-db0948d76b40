<script setup lang="ts">
import { ref } from 'vue';

import { TabPane, Tabs } from 'ant-design-vue';

import DingTalkConfig from '#/views/system/config/components/ding-talk-config.vue';
import WorkWechatConfig from '#/views/system/config/components/work-wechat-config.vue';

const activeKey = ref('workWechat');
</script>

<template>
  <Tabs v-model:active-key="activeKey" tab-position="left">
    <TabPane key="workWechat" tab="企业微信">
      <WorkWechatConfig />
    </TabPane>
    <TabPane key="dingTalk" tab="阿里钉钉">
      <DingTalkConfig />
    </TabPane>
  </Tabs>
</template>

<style></style>
