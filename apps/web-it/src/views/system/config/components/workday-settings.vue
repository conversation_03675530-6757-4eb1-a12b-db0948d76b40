<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue';
import type { CheckboxValueType } from 'ant-design-vue/es/checkbox/interface';

import { computed } from 'vue';

import {
  Calendar,
  Checkbox,
  CheckboxGroup,
  Col,
  DatePicker,
  Radio,
  RadioGroup,
  Row,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import Nzh from 'nzh';

import { getCustomHolidaySettingsApi, getLegalWorkdaysApi } from '#/api';

const nzhCn = Nzh.cn;

const model = defineModel({
  type: Object,
  default: () => {
    return {
      type: 'STATUTORY',
      template: '',
      weekdays: [],
      year: dayjs().year(),
      workingDays: [],
    };
  },
});
const activeYear = computed({
  get() {
    return dayjs(`${model.value.year}-01-01`).startOf('day');
  },
  set(value) {
    model.value.year = value.year();
  },
});
const activeDays = computed<string[]>({
  get() {
    return model.value.workingDays ?? [];
  },
  set(value) {
    model.value.workingDays = value;
  },
});
const onSelect = (date: dayjs.Dayjs) => {
  const activeIndex = activeDays.value.findIndex((day) => {
    return date.isSame(dayjs(day), 'day');
  });
  if (activeIndex === -1) {
    activeDays.value.push(date.format('YYYY-MM-DD'));
  } else {
    activeDays.value.splice(activeIndex, 1);
  }
};
const isActiveDay = (current: dayjs.Dayjs, month: number) => {
  return activeDays.value.some((day) => {
    return current.isSame(dayjs(day), 'day') && current.month() === month - 1;
  });
};
const setRange = (month: number): [dayjs.Dayjs, dayjs.Dayjs] => {
  return [
    dayjs(activeYear.value)
      .month(month - 1)
      .startOf('month'),
    dayjs(activeYear.value)
      .month(month - 1)
      .endOf('month'),
  ];
};
const setValue = (month: number): dayjs.Dayjs => {
  return dayjs(activeYear.value)
    .month(month - 1)
    .startOf('month');
};
const setCnNumber = (number: number) => {
  return nzhCn.encodeS(number);
};
const getAllDaysOfYear = (year: number, dayOfWeek: number) => {
  if (dayOfWeek < 1 || dayOfWeek > 7) {
    throw new Error('dayOfWeek 必须是 1（周一）到 7（周日）之间的数字');
  }

  const days = [];
  let date = dayjs(`${year}-01-01`).startOf('day'); // 从指定年的第一天开始

  // 找到该年的第一个指定星期
  while (date.day() !== (dayOfWeek === 7 ? 0 : dayOfWeek)) {
    date = date.add(1, 'day').startOf('day');
  }

  // 遍历全年，收集所有符合条件的日期
  while (date.year() === year) {
    days.push(date); // 添加当前日期
    date = date.add(7, 'day').startOf('day'); // 跳到下一周的同一天
  }

  return days;
};
const changeWorkDay = (values: CheckboxValueType[]) => {
  const workDays: dayjs.Dayjs[] = [];
  values.forEach((value) => {
    workDays.push(...getAllDaysOfYear(Number(model.value.year), Number(value)));
  });
  const finalDays: string[] = [];
  workDays.forEach((date) => {
    finalDays.push(dayjs(date).format('YYYY-MM-DD'));
  });
  activeDays.value = finalDays;
};
const getWorkDay = () => {
  return activeDays.value;
};
const changeTemplate = async (e?: RadioChangeEvent) => {
  const value = e ? e.target.value : model.value.template;
  if (value === 'STATUTORY') {
    try {
      activeDays.value = await getLegalWorkdaysApi({ year: model.value.year });
    } catch (error) {
      activeDays.value = [];
      console.error(error);
    }
  } else {
    model.value.weekdays = [1, 2, 3, 4, 5];
    const workdayList = new Set([
      ...getAllDaysOfYear(model.value.year, 1),
      ...getAllDaysOfYear(model.value.year, 2),
      ...getAllDaysOfYear(model.value.year, 3),
      ...getAllDaysOfYear(model.value.year, 4),
      ...getAllDaysOfYear(model.value.year, 5),
    ]);
    const finalDays: string[] = [];
    workdayList.forEach((date) => {
      finalDays.push(dayjs(date).format('YYYY-MM-DD'));
    });
    activeDays.value = finalDays;
  }
};
const changeYear = async (year: dayjs.Dayjs | string) => {
  if (dayjs.isDayjs(year)) {
    model.value = await getCustomHolidaySettingsApi({ year: year.year() });
  }
};
defineExpose({
  getWorkDay,
});
</script>

<template>
  <Row :gutter="16">
    <Col :span="4">
      <div class="mb-4">
        <DatePicker
          v-model:value="activeYear"
          picker="year"
          class="w-full"
          :allow-clear="false"
          @change="changeYear"
        />
      </div>
      <div class="">
        <RadioGroup v-model:value="model.template" @change="changeTemplate">
          <Radio value="STATUTORY" class="flex leading-8">法定工作日</Radio>
          <Radio value="STANDARD" class="flex leading-8">标准工作日</Radio>
        </RadioGroup>
      </div>
      <p
        v-if="model.template === 'STATUTORY'"
        class="px-2 text-sm text-gray-400"
      >
        当前工作日信息为法定工作日勾选及手动点击的综合结果
      </p>
      <div
        v-if="model.template === 'STANDARD'"
        class="ml-2 mt-2 border-l py-2 pl-4"
      >
        <p class="mb-2 text-sm text-gray-400">
          当前工作日信息为标准工作日及手动点击的综合结果
        </p>
        <CheckboxGroup
          v-model:value="model.weekdays"
          @change="changeWorkDay"
          class="flex-col"
        >
          <Checkbox v-for="number in 7" :key="number" :value="number">
            <template v-if="number === 7"> 周日 </template>
            <template v-else> 周{{ setCnNumber(number) }} </template>
          </Checkbox>
        </CheckboxGroup>
      </div>
    </Col>
    <Col :span="20">
      <div class="container">
        <div class="@container">
          <div
            class="@6xl:grid-cols-4 @xs:grid-cols-2 @4xl:grid-cols-3 grid grid-cols-2 gap-4"
          >
            <Calendar
              v-for="month in 12"
              :key="month"
              :fullscreen="false"
              :valid-range="setRange(month)"
              :value="setValue(month)"
              class="work-calendar w-[300px]"
              @select="onSelect"
            >
              <template #headerRender="{ value }">
                <p class="mb-1 mr-1 font-bold">
                  {{ dayjs(value).format('M') }}月
                </p>
              </template>
              <template #dateFullCellRender="{ current }">
                <p
                  :class="{ active: isActiveDay(current, month) }"
                  class="m-auto h-7 w-7 leading-7"
                >
                  {{ dayjs(current).format('D') }}
                </p>
              </template>
            </Calendar>
          </div>
        </div>
      </div>
    </Col>
  </Row>
</template>

<style>
.ant-picker-cell {
  .active {
    color: #0d68ff;
    background: #e9f2ff;
    border-radius: 50%;
  }
}

.work-calendar {
  .ant-picker-panel {
    border-top: 0;
  }
}
</style>
