import type { InjectionKey, Ref } from 'vue';

import type { ConfigInfo } from '#/api';

export const configInfoInjectionKey = Symbol(
  '注入配置信息表单KEY',
) as InjectionKey<Ref<Partial<ConfigInfo>>>;
export const configSaveInjectionKey = Symbol(
  '注入保存配置信息KEY',
) as InjectionKey<(values: Partial<ConfigInfo>) => Promise<void>>;
export const configLoadingInjectionKey = Symbol(
  '注入配置信息加载状态KEY',
) as InjectionKey<Ref<{ [key: string]: boolean }>>;
