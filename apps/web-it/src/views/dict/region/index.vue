<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { RegionInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRegionListApi } from '#/api';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '行政区划名称',
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'name', title: '名称', minWidth: 300, treeNode: true, showOverflow: 'title' },
    { field: 'shortName', title: '短名称' },
    { field: 'id', title: '行政区划编码' },
    { field: 'cityCode', title: '城市代码' },
    { field: 'zipCode', title: '邮政编码' },
    { field: 'province', title: '省份' },
    // { field: 'provincePinyin', title: '省份拼音' },
    { field: 'provinceShortName', title: '省份简称' },
    { field: 'city', title: '城市' },
    // { field: 'cityPinyin', title: '城市拼音' },
    { field: 'cityShortName', title: '城市简称' },
    { field: 'district', title: '地区' },
    // { field: 'districtPinyin', title: '地区拼音' },
    { field: 'districtShortName', title: '地区简称' },
    // { field: 'firstChar', title: '拼音首字母' },
    // { field: 'jianpin', title: '拼音简写大写' },
    { field: 'lat', title: '纬度' },
    // { field: 'levelType', title: '级别类型' },
    { field: 'lng', title: '经度' },
    // { field: 'parentId', title: '父节点id' },
    // { field: 'parentPath', title: '父节点路径' },
    // { field: 'pinyin', title: '拼音' },
    { field: 'remark1', title: '备注1' },
    { field: 'remark2', title: '备注2' },
  ],
  height: 'auto',
  treeConfig: {
    transform: true,
    rowField: 'id',
    parentField: 'parentId',
    lazy: true,
    loadMethod({ row }) {
      return getRegionList(row);
    },
  },
  proxyConfig: {
    ajax: {
      query: async (_: never, formValues) => {
        return await getRegionList(formValues);
      },
    },
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const getRegionList = async (params?: { id?: string; name?: string }) => {
  let query: { levelType?: string; name?: string; parentId?: string } = { levelType: '0' };
  if (params && params.id) {
    query = {
      parentId: params.id,
    };
  } else if (params && params.name) {
    query = {
      name: params.name,
    };
  }
  const res = await getRegionListApi(query);
  res.forEach((item: RegionInfo & { hasChild: boolean }) => {
    item.hasChild = item.levelType !== '3';
  });
  return res;
};
</script>

<template>
  <Page auto-content-height>
    <Grid />
  </Page>
</template>

<style></style>
