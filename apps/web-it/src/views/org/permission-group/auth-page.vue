<script setup lang="ts">
import type { TreeProps } from 'ant-design-vue';

import type { PermissionAppVO, PermissionGroupInfo } from '#/api/org/permission';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Button, message, Space, Steps, Tree, TypographyLink } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { getPermissionAuthAllTreeApi, getPermissionAuthApi, savePermissionAuthApi } from '#/api/org/permission';

const emit = defineEmits(['ok', 'register']);

// --- 辅助函数 ---

const addPrefixToNodeKeys = (nodes: any[], prefix: 'app' | 'button' | 'module'): any[] => {
  if (!nodes || nodes.length === 0) return [];
  return nodes.map((node) => {
    const newNode = { ...node };
    newNode.key = `${prefix}-${node.id}`;
    if (newNode.children && newNode.children.length > 0) {
      let childPrefix: 'button' | 'module' = 'module';
      if (prefix === 'module' && newNode.type === '2') {
        childPrefix = 'button';
      } else if (prefix === 'app') {
        childPrefix = 'module';
      }
      newNode.children = addPrefixToNodeKeys(newNode.children, childPrefix);
    }
    return newNode;
  });
};

const getNodeTypeMap = (apps: PermissionAppVO[]) => {
  const map = new Map<number, string>();
  const traverse = (items: any[], type: string) => {
    if (!items || items.length === 0) return;
    for (const item of items) {
      if (!item.id) continue;
      map.set(item.id, type);
      if (item.moduleList) traverse(item.moduleList, 'module');
      if (item.buttonList) traverse(item.buttonList, 'button');
      if (item.children) traverse(item.children, 'module');
    }
  };
  apps.forEach((app) => {
    if (app.id) {
      map.set(app.id, 'app');
    }
    if (app.moduleList) traverse(app.moduleList, 'module');
  });
  return map;
};

// --- 核心 State 和逻辑 ---

const title = computed(() => {
  return `${groupData.value.name ?? ''}的权限授权`;
});
const allTreeData = ref<PermissionAppVO[]>([]);
const groupData = ref<PermissionGroupInfo>({});
const authForm = ref<{ appIdList: number[]; buttonIdList: number[]; moduleIdList: number[] }>({
  appIdList: [],
  buttonIdList: [],
  moduleIdList: [],
});

const checkedKeys = ref<{ checked: string[]; halfChecked: string[] }>({
  checked: [],
  halfChecked: [],
});
const expandedKeys = ref<string[]>([]);
const nodeTypeMap = ref<Map<number, string>>(new Map());
const state = reactive({
  loading: { save: false },
});

const init = async (data: PermissionGroupInfo) => {
  groupData.value = data;
  allTreeData.value = await getPermissionAuthAllTreeApi();
  authForm.value = await getPermissionAuthApi({ id: data.id as number });

  nodeTypeMap.value = getNodeTypeMap(allTreeData.value);
  await getAppList();
  const initialChecked = authForm.value.appIdList.map((id) => `app-${id}`);
  checkedKeys.value = { checked: initialChecked, halfChecked: [] };
};

const [registerPopup, { closePopup }] = usePopupInner(init);
const current = ref(0);
const stepItems = [
  { title: '应用权限', disabled: true },
  { title: '菜单权限', disabled: true },
  { title: '按钮权限', disabled: true },
];
const activeStepName = computed(() => {
  return stepItems[current.value]?.title ?? '';
});

const onTreeCheck: TreeProps['onCheck'] = (keys, eventInfo) => {
  checkedKeys.value = {
    checked: keys as string[],
    halfChecked: eventInfo.halfCheckedKeys as string[],
  };
};

const handleActiveData = () => {
  const allSelectedKeys = [...checkedKeys.value.checked, ...checkedKeys.value.halfChecked];
  const parseKey = (key: string): number => {
    const numStr = key.split('-')[1];
    return numStr ? Number.parseInt(numStr, 10) : Number.NaN;
  };

  switch (current.value) {
    case 0: {
      authForm.value.appIdList = allSelectedKeys
        .filter((key) => key.startsWith('app-'))
        .map((element) => parseKey(element));
      break;
    }
    case 1: {
      authForm.value.moduleIdList = allSelectedKeys
        .filter((key) => key.startsWith('module-'))
        .map((element) => parseKey(element));
      break;
    }
    case 2: {
      authForm.value.buttonIdList = allSelectedKeys
        .filter((key) => key.startsWith('button-'))
        .map((element) => parseKey(element));
      break;
    }
  }
};

const beforeChangeStep = () => {
  // if (checkedKeys.value.checked.length === 0) {
  //   const msg = '请先选择数据';
  //   message.error(msg);
  //   throw new Error(msg);
  // }
  handleActiveData();
};

const afterChangeStep = async () => {
  expandedKeys.value = [];
  checkedKeys.value = { checked: [], halfChecked: [] };
  switch (current.value) {
    case 0: {
      await getAppList();
      break;
    }
    case 1: {
      await getMenuList();
      break;
    }
    case 2: {
      await getButtonList();
      break;
    }
  }

  // 从刚生成的 treeData 中，递归找出所有父节点的 key
  const parentKeys = new Set<string>();
  const findParentKeys = (nodes: any[]) => {
    if (!nodes || nodes.length === 0) return;
    for (const node of nodes) {
      // 如果一个节点有 children 数组且不为空，那么它就是父节点
      if (node.children && node.children.length > 0) {
        parentKeys.add(node.key);
        findParentKeys(node.children);
      }
    }
  };
  findParentKeys(treeData.value);

  // 根据 authForm 获取当前步骤应该被勾选的所有 ID
  let idsToPreserve: number[] = [];
  let prefix = '';
  switch (current.value) {
    case 0: {
      idsToPreserve = authForm.value.appIdList;
      prefix = 'app';
      break;
    }
    case 1: {
      idsToPreserve = authForm.value.moduleIdList;
      prefix = 'module';
      break;
    }
    case 2: {
      idsToPreserve = authForm.value.buttonIdList;
      prefix = 'button';
      break;
    }
  }
  // 将这些 ID 转换为带前缀的 key
  const allPreservedKeys = idsToPreserve.map((id) => `${prefix}-${id}`);
  // 过滤掉所有父节点的 key，只保留叶子节点的 key
  const initialChecked = allPreservedKeys.filter((key) => !parentKeys.has(key));
  checkedKeys.value = { checked: initialChecked, halfChecked: [] };
  expandAll();
};

const prev = async () => {
  beforeChangeStep();
  current.value = current.value - 1;
  await afterChangeStep();
};
const next = async () => {
  beforeChangeStep();
  current.value = current.value + 1;
  await afterChangeStep();
};

const getAllNodeIds = (treeData: any[]): string[] => {
  const ids: string[] = [];
  const extractIds = (nodes: any[]) => {
    if (!nodes || nodes.length === 0) return;
    for (const node of nodes) {
      if (node.key) ids.push(node.key);
      if (node.children && node.children.length > 0) extractIds(node.children);
    }
  };
  extractIds(treeData);
  return ids;
};

const checkAll = () => {
  checkedKeys.value = { checked: getAllNodeIds(treeData.value), halfChecked: [] };
};
const uncheckAll = () => {
  checkedKeys.value = { checked: [], halfChecked: [] };
};
const expandAll = () => {
  expandedKeys.value = getAllNodeIds(treeData.value);
};
const foldAll = () => {
  expandedKeys.value = [];
};

const TreeRef = ref();
const treeData = ref<any[]>([]);

const getAppList = async () => {
  const apps = cloneDeep(allTreeData.value);
  treeData.value = addPrefixToNodeKeys(apps, 'app');
};

const getMenuList = async () => {
  const authFormAppIdSet = new Set(authForm.value.appIdList);
  const filteredApps = allTreeData.value.filter((app) => authFormAppIdSet.has(app.id as number));
  const appsWithChildren = cloneDeep(filteredApps);
  appsWithChildren.forEach((app) => {
    app.children = app.moduleList;
  });
  treeData.value = addPrefixToNodeKeys(appsWithChildren, 'app');
};

const getButtonList = async () => {
  const selectedAppIdSet = new Set(authForm.value.appIdList);
  const selectedModuleIdSet = new Set(authForm.value.moduleIdList);

  const filterAndBuildModuleTree = (modules: any[]): any[] => {
    if (!modules || modules.length === 0) return [];
    const result: any[] = [];
    for (const module of modules) {
      const validChildren = filterAndBuildModuleTree(module.children);
      const isPageWithButtons =
        module.type === '2' && module.buttonList && module.buttonList.length > 0 && selectedModuleIdSet.has(module.id);
      const isDirectoryWithValidChildren = module.type !== '2' && validChildren.length > 0;
      if (isPageWithButtons || isDirectoryWithValidChildren) {
        const newNode = { ...module };
        newNode.children = isPageWithButtons ? newNode.buttonList : validChildren;
        result.push(newNode);
      }
    }
    return result;
  };

  const buttonTreeSource: PermissionAppVO[] = [];
  const sourceApps = cloneDeep(allTreeData.value);
  for (const app of sourceApps) {
    if (selectedAppIdSet.has(app.id as number) && app.moduleList) {
      const validModules = filterAndBuildModuleTree(app.moduleList);
      if (validModules.length > 0) {
        app.children = validModules;
        buttonTreeSource.push(app);
      }
    }
  }
  treeData.value = addPrefixToNodeKeys(buttonTreeSource, 'app');
};

const close = () => {
  current.value = 0;
  authForm.value = { appIdList: [], buttonIdList: [], moduleIdList: [] };
  checkedKeys.value = { checked: [], halfChecked: [] };
  expandedKeys.value = [];
  treeData.value = [];
};

const save = async () => {
  handleActiveData();
  state.loading.save = true;
  try {
    await savePermissionAuthApi({ id: groupData.value.id as number }, authForm.value);
    closePopup();
    message.success('保存成功');
    emit('ok');
  } finally {
    state.loading.save = false;
  }
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup" @close="close">
    <div class="px-8">
      <BasicCaption :content="activeStepName">
        <template #action>
          <Space class="mr-4">
            <TypographyLink @click="expandAll">展开全部</TypographyLink>
            <TypographyLink @click="foldAll">折叠全部</TypographyLink>
            <TypographyLink @click="checkAll">勾选全部</TypographyLink>
            <TypographyLink @click="uncheckAll">取消全部</TypographyLink>
          </Space>
          <Space>
            <Button :disabled="current === 0" @click="prev">上一步</Button>
            <Button :disabled="current === 2" @click="next">下一步</Button>
            <Button :disabled="current !== 2" :loading="state.loading.save" type="primary" @click="save"> 保存 </Button>
          </Space>
        </template>
      </BasicCaption>
      <div class="">
        <Steps v-model:current="current" :items="stepItems" type="navigation" />
      </div>
      <div class="p-4">
        <Tree
          ref="TreeRef"
          v-model:expanded-keys="expandedKeys"
          :tree-data="treeData"
          :checked-keys="checkedKeys"
          checkable
          :field-names="{ key: 'key', title: 'name', children: 'children' }"
          @check="onTreeCheck"
        >
          <template #title="props">
            <div class="flex items-center">
              <VbenIcon :icon="props.icon" class="mr-1 text-lg" />
              <span>{{ props.name }}</span>
            </div>
          </template>
        </Tree>
      </div>
    </div>
  </BasicPopup>
</template>

<style></style>
