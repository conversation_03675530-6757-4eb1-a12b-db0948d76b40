<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { GroupInfo } from '#/api';

import { ref } from 'vue';

import { defaultsDeep } from '@vben/utils';

import { Form, FormItem, Input, InputNumber, Switch, Textarea } from 'ant-design-vue';

const groupForm = ref<Partial<GroupInfo>>({
  sortCode: 0,
  enabled: 1,
});
const groupFormRef = ref();
const onSubmit = async () => {
  await groupFormRef.value.validate();
  return groupForm.value;
};
const rules: Record<string, Rule[]> = {
  name: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入分组编码', trigger: 'blur' }],
};
const init = (data: Partial<GroupInfo>) => {
  groupForm.value = defaultsDeep(data, { sortCode: 0, enabled: 1 });
};
defineExpose({
  onSubmit,
  init,
});
</script>

<template>
  <Form
    ref="groupFormRef"
    :model="groupForm"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 18 }"
    :colon="false"
    :rules="rules"
    autocomplete="off"
  >
    <FormItem label="分组名称" name="name">
      <Input v-model:value="groupForm.name" />
    </FormItem>
    <FormItem label="分组编码" name="code">
      <Input v-model:value="groupForm.code" />
    </FormItem>
    <FormItem label="排序">
      <InputNumber
        v-model:value="groupForm.sortCode"
        :controls="false"
        :min="0"
        :precision="0"
        :step="1"
        class="w-full"
      />
    </FormItem>
    <FormItem label="状态">
      <Switch v-model:checked="groupForm.enabled" :checked-value="1" :un-checked-value="0" />
    </FormItem>
    <FormItem label="说明">
      <Textarea v-model:value="groupForm.description" :auto-size="{ minRows: 3, maxRows: 5 }" />
    </FormItem>
  </Form>
</template>

<style></style>
