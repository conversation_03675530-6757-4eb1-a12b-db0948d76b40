<script lang="ts" setup>
import type { Nullable } from 'vitest';

import type { ScrollActionType, TreeActionType } from '@vben/fe-ui';
import type { Recordable } from '@vben/types';

import type { PermissionGroupInfo } from '#/api/org/permission';

import { computed, nextTick, reactive, ref, unref, watch } from 'vue';

import { BasicModal, BasicTree, FeEmpty, ScrollContainer, useModalInner } from '@vben/fe-ui';

import { DeleteOutlined } from '@ant-design/icons-vue';
import { Avatar, message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import {
  getGroupListApi,
  getPositionListApi,
  getRoleListApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
} from '#/api';
import { getPermissionGroupDetailApi, savePermissionGroupMemberApi } from '#/api/org/permission';

defineOptions({ name: 'FeUsersSelect', inheritAttrs: false });
defineProps({
  modalTitle: { type: String, default: '选择用户' },
});
defineEmits(['register', 'initData']);
// const organizeStore = useOrganizeStore();
const treeRef = ref<Nullable<TreeActionType>>(null);
const selectedData = ref<any[]>([]);
const nodeId = ref('-1');
const id = ref<number>();
const treeKey = ref(Date.now());
const pagination = reactive({
  keyword: '',
  currentPage: 1,
  pageSize: 20,
});
const finish = ref<boolean>(false);
const isAsync = ref<boolean>(false);
const activeKey = ref('');
const infiniteBody = ref<Nullable<ScrollActionType>>(null);
const treeData = ref<any[]>([]);
const ableList = ref<any[]>([]);
const loading = ref(false);

const [registerModal, { changeLoading, closeModal, changeOkLoading }] = useModalInner(init);

const getFieldNames = computed(() => (activeKey.value === '2' ? { key: 'onlyId' } : { key: 'id' }));

watch(
  () => activeKey.value,
  (val) => {
    if (!val) return;
    pagination.keyword = '';
    nodeId.value = '-1';
    isAsync.value = false;
    initData();
  },
);

async function init(data: PermissionGroupInfo) {
  pagination.keyword = '';
  pagination.currentPage = 1;
  isAsync.value = false;
  nodeId.value = '-1';
  activeKey.value = '1';
  treeData.value = [];
  ableList.value = [];
  finish.value = false;
  selectedData.value = [];
  id.value = data.id ?? 0;
  changeLoading(true);
  try {
    const detailRes = await getPermissionGroupDetailApi({ id: data.id as number });
    const permissionMemberList = detailRes.permissionMemberList ?? [];
    const list: { fullName?: string; id: number; organ?: string; type: string }[] = [];
    permissionMemberList.forEach((item) => {
      list.push({
        ...item,
        id: item.objectId,
        type: item.objectType,
        fullName: item.name,
        organ: item.description,
      });
    });
    selectedData.value = list;
    await initData();
  } finally {
    changeLoading(false);
  }
  //
  // props
  //   .getDataApi(data.id)
  //   .then((res) => {
  //     const list = res.data?.list.map((o) => `${o.id}--${o.type}`);
  //     setValue(list);
  //   })
  //   .catch(() => {
  //     changeLoading(false);
  //   });
}
function handleSearch(val: string) {
  treeKey.value = Date.now();
  nodeId.value = '-1';
  treeData.value = [];
  pagination.currentPage = 1;
  isAsync.value = !!pagination.keyword;
  finish.value = false;
  if (isAsync.value && activeKey.value === '1') {
    nextTick(() => {
      bindScroll();
    });
  }
  if (activeKey.value === '1') return initData();
  nextTick(() => {
    getTree().setSearchValue(val);
  });
}
function bindScroll() {
  const bodyRef = infiniteBody.value;
  const vBody = bodyRef?.getScrollWrap();
  vBody?.addEventListener('scroll', () => {
    if (vBody.scrollHeight - vBody.clientHeight - vBody.scrollTop <= 200 && !loading.value && !finish.value) {
      pagination.currentPage += 1;
      getAllList();
    }
  });
}
function handleSelect(keys: string[], nodeInfo: any) {
  if (keys.length === 0) return;
  const data = nodeInfo?.node?.dataRef ?? nodeInfo?.node ?? null;
  if (!data) return;
  handleNodeClick(data);
}
function handleNodeClick(data: any) {
  const boo = selectedData.value.some((o) => o.id === data.id && o.type === data.type);
  if (boo || ['COMPANY'].includes(data.type)) return;
  if (['COMPANY', 'ORGAN'].includes(data.type)) {
    const ids = data.id.split('_');
    data.id = Number(ids[ids.length - 1]);
  }
  selectedData.value.push(data);
}
function removeAll() {
  selectedData.value = [];
}
function removeMulData(index: number) {
  selectedData.value.splice(index, 1);
}
function getTree() {
  const tree = unref(treeRef);
  if (!tree) {
    throw new Error('tree is null!');
  }
  return tree;
}
async function handleSubmit() {
  const formData: { objectId: number; objectType: 'GROUP' | 'ORGAN' | 'POST' | 'ROLE' | 'USER' }[] = [];
  selectedData.value.forEach((item) => {
    formData.push({
      objectId: item.id,
      objectType: item.type,
    });
  });
  changeOkLoading(true);
  await savePermissionGroupMemberApi({ id: id.value as number }, formData);
  changeOkLoading(false);
  message.success('保存成功');
  closeModal();
}
function onLoadData(node: any) {
  nodeId.value = node.id;
  return new Promise((resolve: (value?: unknown) => void) => {
    getTreeList({ orgId: nodeId.value }).then((res) => {
      const list = res;
      getTree().updateNodeByKey(node.eventKey, { children: list, isLeaf: list.length === 0 });
      resolve();
    });
  });
}
const iconList = {
  COMPANY: 'clarity:organization-line',
  DEPARTMENT: 'clarity:group-line',
  USER: 'lucide:user',
};
const getTreeList = async (params: Recordable<any>): Promise<any[]> => {
  if (params.orgId && params.orgId.includes('_')) {
    const idList = params.orgId.split('_');
    params.orgId = idList[idList.length - 1];
  }
  let api = getUserTreeListApi;
  if (params.keyword) {
    api = getUserListByKeywordApi;
    params.keywords = params.keyword.trim();
  }
  const res = await api(params);
  (res as any[]).forEach((item: { icon: any; id: string; isLeaf: any; type: number | string }) => {
    item.isLeaf = Boolean(item.isLeaf);
    item.icon = iconList[item.type as keyof typeof iconList];
    if (item.type !== 'USER') {
      item.id = `${item.type}_${item.id}`;
    }
  });
  return res;
};
function getAllList() {
  loading.value = true;
  if (pagination.keyword) nodeId.value = '';
  getTreeList({ orgId: nodeId.value, ...pagination }).then((res) => {
    if (pagination.keyword) {
      if (res.length < pagination.pageSize) finish.value = true;
      treeData.value = [...treeData.value, ...res];
    } else {
      treeData.value = res;
      if (treeData.value.length > 0 && nodeId.value === '-1') {
        getTree().setExpandedKeys([treeData.value[0].id]);
      }
    }
    loading.value = false;
  });
}
const defaultSelectedList = [
  { id: 'department', type: 'ORGAN', fullName: '部门', icon: 'icon-ym icon-ym-tree-department1', children: [] },
  { id: 'position', type: 'POST', fullName: '岗位', icon: 'icon-ym icon-ym-tree-position1', children: [] },
  { id: 'user', type: 'USER', fullName: '用户', icon: 'icon-ym icon-ym-tree-user2', children: [] },
  { id: 'group', type: 'GROUP', fullName: '分组', icon: 'icon-ym icon-ym-generator-group1', children: [] },
  { id: 'role', type: 'ROLE', fullName: '角色', icon: 'icon-ym icon-ym-generator-role', children: [] },
];
const getSelectedTree = computed(() => {
  const selectedTree: any[] = cloneDeep(defaultSelectedList);
  for (let i = 0; i < selectedData.value.length; i++) {
    const item = selectedData.value[i];
    // Map COMPANY to ORGAN for grouping labels
    const type = item.type === 'COMPANY' ? 'ORGAN' : item.type;
    const group = selectedTree.find((element) => element.type === type);
    if (group) {
      group.children.push({
        ...item,
        type,
      });
    }
  }
  return selectedTree.filter((item) => item.children.length > 0);
});
async function initData() {
  ableList.value = [];
  if (activeKey.value === '1') return getAllList();
  loading.value = true;
  if (activeKey.value === '2') ableList.value = await getRoleList();
  if (activeKey.value === '3') ableList.value = await getPositionList();
  if (activeKey.value === '4') ableList.value = await getGroupList();
  await nextTick(() => {
    getTree().setSearchValue('');
  });
  loading.value = false;
}
async function getRoleList() {
  const list = await getRoleListApi();
  list.forEach((item: any) => {
    item.type = 'ROLE';
    item.fullName = item.name;
  });
  return list;
}
async function getPositionList() {
  const list = await getPositionListApi();
  list.forEach((item: any) => {
    item.type = 'POST';
    item.fullName = item.name;
  });
  return list;
}
async function getGroupList() {
  const list = await getGroupListApi();
  list.forEach((item: any) => {
    item.type = 'GROUP';
    item.fullName = item.name;
  });
  return list;
}
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    class="transfer-modal"
    @register="registerModal"
    :width="800"
    :title="modalTitle"
    @ok="handleSubmit"
    destroy-on-close
  >
    <div class="transfer__body">
      <div class="transfer-pane left-pane">
        <div class="transfer-pane__tool">
          <a-input-search
            placeholder="请输入关键字"
            allow-clear
            v-model:value="pagination.keyword"
            @search="handleSearch"
          />
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab">
          <a-tabs v-model:active-key="activeKey" :tab-bar-gutter="10" size="small" class="pane-tabs">
            <a-tab-pane key="1" tab="部门" />
            <a-tab-pane key="2" tab="角色" />
            <a-tab-pane key="3" tab="岗位" />
            <a-tab-pane key="4" tab="分组" />
          </a-tabs>
          <template v-if="activeKey === '1'">
            <BasicTree
              class="tree-main"
              :tree-data="treeData"
              :load-data="onLoadData"
              @select="handleSelect"
              ref="treeRef"
              :key="treeKey"
              :loading="loading"
              :field-names="{ title: 'fullName' }"
              v-if="!isAsync"
            />
            <ScrollContainer v-loading="loading && pagination.currentPage === 1" v-else ref="infiniteBody">
              <div
                v-for="item in treeData"
                :key="item.id"
                class="selected-item selected-item-user-multiple"
                @click="handleNodeClick(item)"
              >
                <div class="selected-item-main">
                  <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                  <div class="selected-item-text">
                    <p class="name">{{ item.fullName }}</p>
                    <p class="organize" :title="item.organ">{{ item.organ }}</p>
                  </div>
                </div>
              </div>
              <FeEmpty v-if="treeData.length === 0" />
            </ScrollContainer>
          </template>
          <BasicTree
            class="tree-main"
            :tree-data="ableList"
            @select="handleSelect"
            ref="treeRef"
            :field-names="getFieldNames"
            default-expand-all
            :loading="loading"
            v-if="['2', '3', '4', 'system'].includes(activeKey)"
          />
        </div>
      </div>
      <div class="transfer-pane right-pane">
        <div class="transfer-pane__tool">
          <span>已选</span>
          <span class="remove-all-btn" @click="removeAll">清空列表</span>
        </div>
        <div class="transfer-pane__body">
          <ScrollContainer>
            <template v-if="selectedData.length > 0">
              <div v-for="(group, index) in getSelectedTree" :key="index" class="selected-item-user-multiple">
                <p class="selected-item-title">
                  <span>{{ group.fullName }}</span>
                </p>
                <div v-for="(item, i) in group.children" :key="i" class="selected-item selected-item-user">
                  <div class="selected-item-main">
                    <Avatar
                      :size="36"
                      :src="item.headIcon"
                      class="selected-item-headIcon"
                      v-if="item.type === 'USER'"
                    />
                    <div class="selected-item-icon" v-else>{{ item.fullName?.substring(0, 1) }}</div>
                    <div class="selected-item-text">
                      <p class="name">{{ item.fullName }}</p>
                      <p class="organize" :title="item.organ">{{ item.organ }}</p>
                    </div>
                    <DeleteOutlined class="delete-btn" @click="removeMulData(i)" />
                  </div>
                </div>
              </div>
            </template>
            <FeEmpty v-if="selectedData.length === 0" />
          </ScrollContainer>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
