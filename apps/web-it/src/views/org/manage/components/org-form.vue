<script setup lang="ts">
import type { OrgInfo } from '#/api';

import { ref } from 'vue';

import { Form, FormItem, Input, InputNumber, Select, SelectOption, Textarea } from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { BaseFeOrganizeSelect, BaseFeUserSelect } from '#/adapter/fe-ui';

const departmentFormRef = ref();
const formModel = ref<Partial<OrgInfo>>({});
const rules = {
  parentId: [{ required: true, message: '请选择所属组织' }],
  name: [{ required: true, message: '请输入部门名称' }],
  code: [{ required: true, message: '请输入部门编码' }],
};
const init = (data: Partial<OrgInfo>) => {
  departmentFormRef.value.clearValidate();
  formModel.value = defaultsDeep(data, { managerIds: [] });
};
const submit = () => {
  departmentFormRef.value.validate();
  return formModel.value;
};
defineExpose({ init, submit });
</script>

<template>
  <Form
    ref="departmentFormRef"
    :model="formModel"
    :rules="rules"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 18 }"
    :colon="false"
  >
    <!-- 所属组织 -->
    <FormItem label="所属组织" name="parentId">
      <BaseFeOrganizeSelect v-if="formModel.parentId !== -1" v-model:value="formModel.parentId" />
      <Select v-else v-model:value="formModel.parentId" placeholder="">
        <SelectOption :value="-1">顶级节点</SelectOption>
      </Select>
    </FormItem>
    <!-- 部门名称 -->
    <FormItem label="部门名称" name="name">
      <Input v-model:value="formModel.name" />
    </FormItem>
    <!-- 部门编码 -->
    <FormItem label="部门编码" name="code">
      <Input v-model:value="formModel.code" />
    </FormItem>
    <!-- 部门主管 -->
    <FormItem label="部门主管" name="managerId">
      <BaseFeUserSelect v-model:value="formModel.managerId" />
    </FormItem>
    <!-- 排序 -->
    <FormItem label="排序" name="sortCode">
      <InputNumber v-model:value="formModel.sortCode" :min="0" :precision="0" :controls="false" class="w-full" />
    </FormItem>
    <!-- 说明 -->
    <FormItem label="说明" name="description">
      <Textarea v-model:value="formModel.description" :auto-size="{ minRows: 3 }" />
    </FormItem>
  </Form>
</template>

<style></style>
