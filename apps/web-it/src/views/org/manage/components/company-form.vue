<script setup lang="ts">
import type { OrgInfo } from '#/api';

import { ref } from 'vue';

import { Form, FormItem, Input, InputNumber, Select, SelectOption, Textarea } from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { BaseFeOrganizeSelect } from '#/adapter/fe-ui';

const companyFormRef = ref();
const formModel = ref<Partial<OrgInfo>>({});
const rules = {};
const init = (data: Partial<OrgInfo>) => {
  companyFormRef.value.clearValidate();
  formModel.value = defaultsDeep(data, {});
};
const submit = () => {
  companyFormRef.value.validate();
  return formModel.value;
};
defineExpose({ init, submit });
</script>

<template>
  <Form
    ref="companyFormRef"
    :model="formModel"
    :rules="rules"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 18 }"
    :colon="false"
  >
    <!-- 上级公司 -->
    <FormItem label="上级公司" name="parentId">
      <BaseFeOrganizeSelect v-if="formModel.parentId !== -1" v-model:value="formModel.parentId" />
      <Select v-else v-model:value="formModel.parentId" placeholder="">
        <SelectOption :value="-1">顶级节点</SelectOption>
      </Select>
    </FormItem>
    <!-- 公司名称 -->
    <FormItem label="公司名称" name="name">
      <Input v-model:value="formModel.name" />
    </FormItem>
    <!-- 公司编码 -->
    <FormItem label="公司编码" name="code">
      <Input v-model:value="formModel.code" />
    </FormItem>
    <!-- 排序 -->
    <FormItem label="排序" name="sortCode">
      <InputNumber v-model:value="formModel.sortCode" :min="0" :precision="0" :controls="false" class="w-full" />
    </FormItem>
    <!-- 说明 -->
    <FormItem label="说明" name="description">
      <Textarea v-model:value="formModel.description" :auto-size="{ minRows: 3 }" />
    </FormItem>
  </Form>
</template>

<style></style>
