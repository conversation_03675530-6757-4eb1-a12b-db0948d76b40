<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { PositionInfo } from '#/api';

import { ref } from 'vue';

import { FeOrganizeSelect } from '@vben/fe-ui';
import { defaultsDeep } from '@vben/utils';

import { Form, FormItem, Input, InputNumber, Switch, Textarea } from 'ant-design-vue';

import { getOrgListApi, getOrgListPyIdsApi } from '#/api';

const positionForm = ref<Partial<PositionInfo>>({
  sortCode: 0,
  enabled: 1,
});
const positionFormRef = ref();
const onSubmit = async () => {
  await positionFormRef.value.validate();
  return positionForm.value;
};
const rules: Record<string, Rule[]> = {
  name: [{ required: true, message: '请输入岗位名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入岗位编码', trigger: 'blur' }],
  organId: [{ required: true, message: '请选择所属组织', trigger: 'change' }],
};
const init = (data: Partial<PositionInfo>) => {
  positionForm.value = defaultsDeep(data, { sortCode: 0, enabled: 1 });
};
defineExpose({
  onSubmit,
  init,
});
</script>

<template>
  <Form
    ref="positionFormRef"
    :model="positionForm"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 18 }"
    :colon="false"
    :rules="rules"
    autocomplete="off"
  >
    <FormItem label="所属组织" name="organId">
      <FeOrganizeSelect v-model:value="positionForm.organId" :api="{ getOrgListApi, getOrgListPyIdsApi }" />
    </FormItem>
    <FormItem label="岗位名称" name="name">
      <Input v-model:value="positionForm.name" />
    </FormItem>
    <FormItem label="岗位编码" name="code">
      <Input v-model:value="positionForm.code" />
    </FormItem>
    <FormItem label="排序">
      <InputNumber
        v-model:value="positionForm.sortCode"
        :controls="false"
        :min="0"
        :precision="0"
        :step="1"
        class="w-full"
      />
    </FormItem>
    <FormItem label="状态">
      <Switch v-model:checked="positionForm.enabled" :checked-value="1" :un-checked-value="0" />
    </FormItem>
    <FormItem label="说明">
      <Textarea v-model:value="positionForm.description" :auto-size="{ minRows: 3, maxRows: 5 }" />
    </FormItem>
  </Form>
</template>

<style></style>
