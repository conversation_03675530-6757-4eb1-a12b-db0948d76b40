<script lang="ts" setup>
// import { useRouter } from 'vue-router';

import { WorkbenchHeader } from '@vben/common-ui';
import { preferences } from '@vben/preferences';
import { useUserStore } from '@vben/stores';

const userStore = useUserStore();

// 同样，这里的 url 也可以使用以 http 开头的外部链接
// const quickNavItems: WorkbenchQuickNavItem[] = [
//   {
//     color: '#1fdaca',
//     icon: 'ion:home-outline',
//     title: '首页',
//     url: '/',
//   },
//   {
//     color: '#bf0c2c',
//     icon: 'ion:grid-outline',
//     title: '仪表盘',
//     url: '/dashboard',
//   },
//   {
//     color: '#e18525',
//     icon: 'ion:layers-outline',
//     title: '组件',
//     url: '/demos/features/icons',
//   },
//   {
//     color: '#3fb27f',
//     icon: 'ion:settings-outline',
//     title: '系统管理',
//     url: '/demos/features/login-expired', // 这里的 URL 是示例，实际项目中需要根据实际情况进行调整
//   },
//   {
//     color: '#4daf1bc9',
//     icon: 'ion:key-outline',
//     title: '权限管理',
//     url: '/demos/access/page-control',
//   },
//   {
//     color: '#00d8ff',
//     icon: 'ion:bar-chart-outline',
//     title: '图表',
//     url: '/analytics',
//   },
// ];

// const router = useRouter();

// 这是一个示例方法，实际项目中需要根据实际情况进行调整
// This is a sample method, adjust according to the actual project requirements
// function navTo(nav: WorkbenchQuickNavItem) {
//   if (nav.url?.startsWith('http')) {
//     openWindow(nav.url);
//     return;
//   }
//   if (nav.url?.startsWith('/')) {
//     router.push(nav.url).catch((error) => {
//       console.error('Navigation failed:', error);
//     });
//   } else {
//     console.warn(`Unknown URL for navigation item: ${nav.title} -> ${nav.url}`);
//   }
// }
</script>

<template>
  <div class="p-5">
    <WorkbenchHeader :avatar="userStore.userInfo?.headIcon || preferences.app.defaultAvatar">
      <template #title> 您好, {{ userStore.userInfo?.realName }}, 开始您一天的工作吧！ </template>
      <!--<template #description> 今日晴，20℃ - 32℃！ </template>-->
    </WorkbenchHeader>

    <!--<div class="mt-5 flex flex-col lg:flex-row">-->
    <!--  <div class="w-full">-->
    <!--    <WorkbenchQuickNav :items="quickNavItems" class="mt-5 lg:mt-0" title="快捷导航" @click="navTo" />-->
    <!--  </div>-->
    <!--</div>-->
  </div>
</template>
