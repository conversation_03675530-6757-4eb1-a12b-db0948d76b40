<script setup lang="ts">
import type { DriveFileInfo } from '@vben/types';

import { computed, h, onMounted, onUnmounted, ref, watch } from 'vue';

import { FilePreviewDialog, FileUploader } from '@vben/base-ui';
import { confirm, Page } from '@vben/common-ui';
import { BasicForm, BasicTable, FeEmpty, ScrollContainer, useForm, useModal, useTable } from '@vben/fe-ui';
import { toFileSize } from '@vben/fe-ui/utils/fe';
import { $t as t } from '@vben/locales';
import { downloadAndSaveWithAutoFileName } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import {
  AppstoreOutlined,
  BarsOutlined,
  CloudDownloadOutlined,
  DeleteOutlined,
  DeliveredProcedureOutlined,
  EditOutlined,
  FolderAddOutlined,
  ShareAltOutlined,
  UndoOutlined,
} from '@ant-design/icons-vue';
import {
  Breadcrumb,
  BreadcrumbItem,
  Button,
  Checkbox,
  CheckboxGroup,
  message,
  Space,
  TabPane,
  Tabs,
  Tooltip,
} from 'ant-design-vue';
import { find } from 'lodash-es';

import {
  calculateCloudDiskFolderApi,
  cancelCloudDiskShareApi,
  checkCloudDiskFileApi,
  confirmDelCloudDiskFileApi,
  delCloudDiskFileApi,
  downloadCloudDiskFileApi,
  downloadCloudDiskMultiFileApi,
  getCloudDiskListApi,
  getCloudDiskMyShareListApi,
  getCloudDiskRecycleListApi,
  getCloudDiskShareToMeListApi,
  getDownloadFileLinkApi,
  getPreviewFileExternalLink,
  preCheckCloudDiskFileApi,
  restoreCloudDiskFileApi,
  uploadCloudDiskFileApi,
} from '#/api';
// import * as cloudDiskApi from '#/api/cloud-disk';
import audioImg from '#/assets/images/document/audio.png';
import blankImg from '#/assets/images/document/blank.png';
import codeImg from '#/assets/images/document/code.png';
import excelImg from '#/assets/images/document/excel.png';
import folderImg from '#/assets/images/document/folder.png';
import imageImg from '#/assets/images/document/image.png';
import pdfImg from '#/assets/images/document/pdf.png';
import pptImg from '#/assets/images/document/ppt.png';
import rarImg from '#/assets/images/document/rar.png';
import txtImg from '#/assets/images/document/txt.png';
import wordImg from '#/assets/images/document/word.png';

import FolderTree from './components/FolderTree.vue';
import Form from './components/Form.vue';
import UserBox from './components/UserBox.vue';

// 用于监听拖拽事件的 DOM 元素引用
const dropZoneRef = ref<HTMLDivElement | null>(null);
// 新增：拖拽计数器，解决闪烁问题
const dragCounter = ref(0);

const activeKey = ref<'all' | 'shareOut' | 'shareTome' | 'trash'>('all');
const leftList = [
  { id: 'all', fileName: '我的文档', icon: 'akar-icons:file' },
  { id: 'shareOut', fileName: '我的共享', icon: 'gg:share' },
  { id: 'shareTome', fileName: '共享给我', icon: 'ri:user-received-line' },
  { id: 'trash', fileName: '回收站', icon: 'streamline:recycle-bin-2-solid' },
];
const levelList = ref<{ fileName?: string; id: number }[]>([]);
const searchInfo = ref<{ keyword: string; parentId: number }>({
  keyword: '',
  parentId: 0,
});
const handleReturnToPrevious = () => {
  if (levelList.value.length > 1) {
    const previousItem = levelList.value[levelList.value.length - 2];
    if (previousItem) {
      handleJump(previousItem as { id: number }, levelList.value.length - 2);
    }
  }
};
const handleJump = (item: { id: number }, i: number) => {
  searchInfo.value.parentId = item.id;
  levelList.value = levelList.value.slice(0, i + 1);
  handleReset();
};
const handleReset = () => {
  searchInfo.value.keyword = '';
  initData();
};
const loading = ref(false);
const fileList = ref<DriveFileInfo[]>([]);
const selectedRowKeys = ref<number[]>([]);
const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowSelection: { type: 'checkbox' },
  clickToRowSelect: false,
  showTableSetting: false,
  pagination: false,
  immediate: false,
  resizeHeightOffset: 10,
});
const checkAll = ref(false);
const isIndeterminate = ref(false);
// 缩略模式下单选
const handleCheckedChange = (val: any[]) => {
  const checkedCount = val.length;
  checkAll.value = !!checkedCount && checkedCount === fileList.value.length;
  isIndeterminate.value = !!checkedCount && checkedCount < fileList.value.length;
};
const initData = () => {
  loading.value = true;
  selectedRowKeys.value = [];
  clearSelectedRowKeys();
  handleCheckedChange(selectedRowKeys.value);
  const api = {
    all: getCloudDiskListApi,
    shareOut: getCloudDiskMyShareListApi,
    shareTome: getCloudDiskShareToMeListApi,
    trash: getCloudDiskRecycleListApi,
  };
  api[activeKey.value](searchInfo.value).then((res) => {
    fileList.value = res;
    loading.value = false;
  });
};
const [registerForm, { resetFields }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: true,
  showAdvancedButton: true,
  compact: true,
  schemas: [
    {
      field: 'keyword',
      label: t('common.keyword'),
      component: 'Input',
      componentProps: {
        placeholder: t('common.enterKeyword'),
        submitOnPressEnter: true,
      },
    },
  ],
});
function handleSubmit(values: { keyword?: string }) {
  searchInfo.value.keyword = values?.keyword || '';
  if (searchInfo.value.keyword) resetBreadcrumb();
  initData();
}
function resetBreadcrumb() {
  const activeItem = leftList.find((o) => o.id === activeKey.value);
  levelList.value = [{ id: 0, fileName: activeItem?.fileName }];
  searchInfo.value.parentId = 0;
}
function addFolder() {
  openFormModal(true, { parentId: searchInfo.value.parentId });
}
const [registerFormModal, { openModal: openFormModal }] = useModal();
const fileUploaderRef = ref();
const showMode = ref(1);
// 切换展示模式
function toggleShowMode(type: number) {
  showMode.value = type;
  handleCheckedChange(selectedRowKeys.value);
}
const [registerUserBox, { openModal: openUserBox }] = useModal();
// 批量共享
function handleShare() {
  openUserBox(true, { ids: selectedRowKeys.value, isBatch: true });
}
// 下载
function handleDownload() {
  let api = downloadCloudDiskMultiFileApi({ ids: selectedRowKeys.value });
  if (selectedRowKeys.value.length === 1) {
    const fileInfo = find(fileList.value, { id: selectedRowKeys.value[0] });
    api =
      fileInfo?.fileType === 1
        ? downloadCloudDiskMultiFileApi({ ids: selectedRowKeys.value })
        : downloadCloudDiskFileApi({ id: selectedRowKeys.value[0] as number });
  }
  api.then((res) => {
    downloadAndSaveWithAutoFileName({ url: res });
  });
}
// 删除
async function handleDelete() {
  await confirm('您确定要把所选文件放入回收站, 是否继续?', t('common.tipTitle'));
  delCloudDiskFileApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('删除成功');
    initData();
  });
}
function handleRename() {
  openFormModal(true, { parentId: searchInfo.value.parentId, id: selectedRowKeys.value[0] });
}
const [registerFolderTree, { openModal: openFolderTree }] = useModal();
// 移动到
function handleMoveTo() {
  openFolderTree(true, { ids: selectedRowKeys.value, parentId: searchInfo.value.parentId });
}
// 取消共享
async function handleUnshare() {
  await confirm('您确定要取消共享, 是否继续?', t('common.tipTitle'));
  cancelCloudDiskShareApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('取消成功');
    initData();
  });
}
// 还原
async function handleRecovery() {
  await confirm('您确定要还原选中的文件?', t('common.tipTitle'));
  restoreCloudDiskFileApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('还原成功');
    initData();
  });
}
// 彻底删除
async function handleTrashDel() {
  await confirm('文件删除后将无法恢复，您确定要彻底删除所选文件吗?', '提示');
  confirmDelCloudDiskFileApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('删除成功');
    initData();
  });
}
function onSelectionChange({ keys }: { keys: number[] }) {
  selectedRowKeys.value = keys;
}
const allColumns = [
  { title: '文件名', dataIndex: 'fileName' },
  { title: '', dataIndex: 'isShare', width: 35 },
  { title: '大小', dataIndex: 'fileSize', width: 90 },
  { title: '创建日期', dataIndex: 'createTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
];
const shareOutColumns = [
  { title: '文件名', dataIndex: 'fileName' },
  { title: '大小', dataIndex: 'fileSize', width: 90 },
  { title: '共享日期', dataIndex: 'shareTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
  { title: '操作', dataIndex: 'action', width: 50 },
];
const shareTomeColumns = [
  { title: '文件名', dataIndex: 'fileName' },
  { title: '大小', dataIndex: 'fileSize', width: 90 },
  { title: '共享日期', dataIndex: 'shareTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
  { title: '共享人员', dataIndex: 'userName', width: 120 },
];
const trashColumns = [
  { title: '文件名', dataIndex: 'fileName' },
  { title: '大小', dataIndex: 'fileSize', width: 90 },
  { title: '删除日期', dataIndex: 'deleteTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
];
function getColumns() {
  if (activeKey.value === 'shareOut') return shareOutColumns;
  if (activeKey.value === 'shareTome') return shareTomeColumns;
  if (activeKey.value === 'trash') return trashColumns;
  return allColumns;
}
const getTableBindValue = computed(() => ({
  loading: loading.value,
  onSelectionChange,
  dataSource: fileList.value,
  columns: getColumns(),
}));
const imgTypeList = new Set(['bmp', 'gif', 'jpeg', 'jpg', 'png']);
const wordTypeList = new Set(['doc', 'docx']);
const excelTypeList = new Set(['xls', 'xlsx']);
const pptTypeList = new Set(['ppt', 'pptx']);
const pdfTypeList = new Set(['pdf']);
const zipTypeList = new Set(['7z', 'arj', 'rar', 'z', 'zip']);
const txtTypeList = new Set(['log', 'txt']);
const codeTypeList = new Set(['cs', 'html', 'xml']);
const videoTypeList = new Set([
  'avi',
  'avi',
  'flv',
  'flv',
  'mkv',
  'mov',
  'mp3',
  'mp4',
  'mpeg',
  'mpg',
  'mpg',
  'ram',
  'rm',
  'rm',
  'rmvb',
  'swf',
  'wma',
  'wmv',
]);
const FilePreviewDialogRef = ref();
// 预览
function handlePreview(record: DriveFileInfo) {
  // 文件预览
  FilePreviewDialogRef.value.init(record.fileId);
}
function openFolder(record: DriveFileInfo) {
  if (record.id) {
    searchInfo.value.parentId = record.id;
    levelList.value.push({ id: record.id, fileName: record.fileName });
    selectedRowKeys.value = [];
    handleReset();
  }
}
function onRecordClick(record: DriveFileInfo) {
  if (['shareOut', 'trash'].includes(activeKey.value)) return;
  record.fileType ? openFolder(record) : handlePreview(record);
}
function getRecordImg(ext?: string) {
  if (!ext) return folderImg;
  if (ext) ext = ext.replace('.', '');
  if (wordTypeList.has(ext)) return wordImg;
  if (excelTypeList.has(ext)) return excelImg;
  if (pptTypeList.has(ext)) return pptImg;
  if (pdfTypeList.has(ext)) return pdfImg;
  if (zipTypeList.has(ext)) return rarImg;
  if (txtTypeList.has(ext)) return txtImg;
  if (codeTypeList.has(ext)) return codeImg;
  if (imgTypeList.has(ext)) return imageImg;
  if (videoTypeList.has(ext)) return audioImg;
  return blankImg;
}
// 单个共享
function handleSingleShare(id: number) {
  if (!id) return;
  openUserBox(true, { ids: [id], isBatch: false });
}
// 缩略模式下全选
function handleCheckAllChange(e: { target: { checked: any } }) {
  const val = e.target.checked;
  selectedRowKeys.value = val ? fileList.value.map((o) => o.id as number) : [];
  isIndeterminate.value = false;
}
async function calculateFolder(record: DriveFileInfo) {
  if (record.id) {
    record.fileSize = await calculateCloudDiskFolderApi({ folderId: record.id });
  }
}
// const CloudDiskFilePickerRef = ref();
watch(
  () => activeKey.value,
  () => init(),
);
function init() {
  resetBreadcrumb();
  resetFields();
}

// --- 拖拽上传逻辑优化 ---
const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value++;
  if (dragCounter.value === 1) {
    dropZoneRef.value?.classList.add('drag-over-active');
  }
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value--;
  if (dragCounter.value === 0) {
    dropZoneRef.value?.classList.remove('drag-over-active');
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value = 0; // 重置计数器
  dropZoneRef.value?.classList.remove('drag-over-active');

  const items = event.dataTransfer?.items;
  if (items) {
    for (const item of items) {
      const entry = item.webkitGetAsEntry();
      if (entry && entry.isDirectory) {
        message.warning('不支持拖拽文件夹上传，请直接拖拽文件。');
        return;
      }
    }
  }

  const files = event.dataTransfer?.files;
  if (fileUploaderRef.value && files && files.length > 0) {
    fileUploaderRef.value.uploadFiles([...files]);
  }
};

const handleDragOver = (event: DragEvent) => {
  // 必须阻止默认行为，否则 drop 事件不会触发
  event.preventDefault();
};

onMounted(() => {
  init();
  if (dropZoneRef.value) {
    dropZoneRef.value.addEventListener('dragenter', handleDragEnter);
    dropZoneRef.value.addEventListener('dragleave', handleDragLeave);
    dropZoneRef.value.addEventListener('drop', handleDrop);
    dropZoneRef.value.addEventListener('dragover', handleDragOver);
  }
});

onUnmounted(() => {
  if (dropZoneRef.value) {
    dropZoneRef.value.removeEventListener('dragenter', handleDragEnter);
    dropZoneRef.value.removeEventListener('dragleave', handleDragLeave);
    dropZoneRef.value.removeEventListener('drop', handleDrop);
    dropZoneRef.value.removeEventListener('dragover', handleDragOver);
  }
});
</script>

<template>
  <Page auto-content-height>
    <div class="document-wrapper flex h-full" ref="dropZoneRef">
      <Tabs v-model:active-key="activeKey" tab-position="left" class="mr-3 mt-3 h-full border-r">
        <TabPane v-for="tab in leftList" :key="tab.id">
          <template #tab>
            <div class="flex items-center">
              <VbenIcon :icon="tab.icon" class="mr-1" />
              <span>{{ tab.fileName }}</span>
            </div>
          </template>
        </TabPane>
      </Tabs>
      <div class="document-container">
        <Breadcrumb class="mb-3">
          <BreadcrumbItem v-if="levelList.length > 1" @click="handleReturnToPrevious">
            <a>返回上一级</a>
          </BreadcrumbItem>
          <BreadcrumbItem v-for="(item, i) in levelList" :key="i">
            <span v-if="i + 1 >= levelList.length">{{ item.fileName }}</span>
            <a v-else @click="handleJump(item, i)">{{ item.fileName }}</a>
          </BreadcrumbItem>
        </Breadcrumb>
        <div class="fe-common-search-box">
          <BasicForm class="search-form" @register="registerForm" @submit="handleSubmit" @reset="handleReset" />
          <div class="fe-common-search-box-right">
            <template v-if="selectedRowKeys.length === 0">
              <template v-if="activeKey === 'all'">
                <!--<Button :icon="h(FolderAddOutlined)" @click="CloudDiskFilePickerRef.pick()" class="mr-2">-->
                <!--  选择文件-->
                <!--</Button>-->
                <Button :icon="h(FolderAddOutlined)" @click="addFolder()" class="mr-2">新建文件夹</Button>
                <FileUploader
                  ref="fileUploaderRef"
                  :upload-api="uploadCloudDiskFileApi"
                  :pre-check-api="preCheckCloudDiskFileApi"
                  :check-api="checkCloudDiskFileApi"
                  :folder-id="searchInfo.parentId"
                  class="mr-2"
                  @all-completed="initData"
                />
              </template>
              <Tooltip>
                <template #title>{{ showMode === 1 ? '缩略模式' : '列表模式' }}</template>
                <a-button v-show="showMode === 1" @click="toggleShowMode(2)" :icon="h(AppstoreOutlined)" />
                <a-button v-show="showMode === 2" @click="toggleShowMode(1)" :icon="h(BarsOutlined)" />
              </Tooltip>
            </template>
            <template v-else>
              <Space.Compact block>
                <template v-if="activeKey === 'all'">
                  <Button :icon="h(ShareAltOutlined)" @click="handleShare">共享</Button>
                  <Button :icon="h(CloudDownloadOutlined)" @click="handleDownload">下载</Button>
                  <Button :icon="h(DeleteOutlined)" @click="handleDelete">删除</Button>
                  <Button :icon="h(EditOutlined)" @click="handleRename" v-if="selectedRowKeys.length === 1">
                    重命名
                  </Button>
                  <Button :icon="h(DeliveredProcedureOutlined)" @click="handleMoveTo">移动</Button>
                </template>
                <template v-if="activeKey === 'shareOut'">
                  <Button :icon="h(ShareAltOutlined)" @click="handleUnshare" v-if="levelList.length <= 1">
                    取消共享
                  </Button>
                </template>
                <template v-if="activeKey === 'shareTome'">
                  <Button :icon="h(CloudDownloadOutlined)" @click="handleDownload">下载</Button>
                </template>
                <template v-if="activeKey === 'trash'">
                  <Button :icon="h(UndoOutlined)" @click="handleRecovery">还原</Button>
                  <Button :icon="h(DeleteOutlined)" @click="handleTrashDel">删除</Button>
                </template>
              </Space.Compact>
            </template>
          </div>
        </div>
        <BasicTable @register="registerTable" v-bind="getTableBindValue" v-show="showMode === 1">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'fileName'">
              <span class="document-fileName" :class="{ 'link-fullName': record.type }" @click="onRecordClick(record)">
                <img :src="getRecordImg(record.fileExtension)" class="file-img" alt="" />
                {{ record.fileName }}
              </span>
            </template>
            <template v-if="column.key === 'isShare'">
              <span v-if="record.isShare" title="共享文件">
                <VbenIcon icon="gg:share" />
              </span>
              <span v-else></span>
            </template>
            <template v-if="column.key === 'fileSize'">
              <template v-if="record.fileType === 1">
                <span v-if="record.fileSize">{{ toFileSize(record.fileSize) }}</span>
                <a-typography-link v-else @click="calculateFolder(record)">计算</a-typography-link>
              </template>
              <template v-else>
                {{ toFileSize(record.fileSize) }}
              </template>
            </template>
            <template v-if="column.key === 'action'">
              <Button
                type="link"
                size="small"
                @click="handleSingleShare(record.id)"
                class="!px-0"
                v-if="levelList.length <= 1"
              >
                设置
              </Button>
            </template>
          </template>
        </BasicTable>
        <div class="document-list-header" v-show="showMode === 2">
          <Checkbox
            :indeterminate="isIndeterminate"
            v-model:checked="checkAll"
            :disabled="fileList.length === 0"
            @change="handleCheckAllChange"
          >
            全选
          </Checkbox>
        </div>
        <CheckboxGroup
          v-model:value="selectedRowKeys"
          class="document-list"
          @change="handleCheckedChange"
          v-show="showMode === 2"
        >
          <ScrollContainer v-loading="loading">
            <div class="document-list-main">
              <div
                class="document-item"
                :class="{ active: record.id && selectedRowKeys.includes(record.id) }"
                v-for="record in fileList"
                :key="record.id"
                @click="onRecordClick(record)"
              >
                <img :src="getRecordImg(record.fileExtension)" class="document-item-img" alt="图标" />
                <p class="document-item-title" :title="record.fileName">{{ record.fileName }}</p>
                <div class="check-icon" @click.stop>
                  <Checkbox :value="record.id" />
                </div>
              </div>
            </div>
            <FeEmpty v-if="fileList.length === 0" />
          </ScrollContainer>
        </CheckboxGroup>
      </div>
    </div>
    <Form @register="registerFormModal" @reload="initData" />
    <!--<FileUploader ref="fileUploaderRef" :parent-id="searchInfo.parentId" @file-success="initData" />-->
    <UserBox @register="registerUserBox" @reload="initData" />
    <FolderTree @register="registerFolderTree" @reload="initData" />
    <FilePreviewDialog
      ref="FilePreviewDialogRef"
      :preview-api="getPreviewFileExternalLink"
      :download-api="getDownloadFileLinkApi"
    />
    <!--<CloudDiskFilePicker ref="CloudDiskFilePickerRef" :api-suite="cloudDiskApi" />-->
  </Page>
</template>

<style lang="less">
@import '#/style/index.less';
.document-wrapper {
  background-color: @app-content-background;
  :deep(.ant-table-container),
  .ant-table-container {
    .ant-table-cell::before {
      display: none !important;
    }
  }
  .ant-tabs-content-holder {
    display: none;
  }
  .ant-checkbox-group {
    cursor: default;
  }
  .document-container {
    flex: 1;
    padding-top: 20px;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .fe-common-search-box {
    margin-bottom: 10px;
    position: relative;
    .fe-common-search-box-right {
      position: absolute;
      right: 10px;
      top: 0;
      display: flex;
      align-items: center;
      .mode-icon {
        margin-left: 10px;
        font-size: 18px;
        line-height: 32px;
        cursor: pointer;
        &:hover {
          color: @primary-color;
        }
      }
    }
  }
  .document-list-header {
    margin-top: -10px;
    margin-right: 10px;
    line-height: 40px;
    flex-shrink: 0;
    border-bottom: 1px solid @border-color-base1;
  }
  .document-list {
    flex: 1;
    width: 100%;
    overflow: hidden;
    padding-bottom: 10px;
    .document-list-main {
      padding: 20px 10px 0 0;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-content: flex-start;
      flex-wrap: wrap;
    }
    .document-item {
      width: 100px;
      height: 100px;
      border-radius: var(--border-radius);
      overflow: hidden;
      margin: 0 20px 40px;
      padding: 5px;
      cursor: pointer;
      position: relative;
      &:hover {
        background-color: @app-content-background;
        .check-icon {
          display: block;
        }
      }
      &.active {
        .check-icon {
          display: block;
        }
      }
      .document-item-img {
        width: 60px;
        height: 60px;
        margin: 0 auto 6px;
      }
      .document-item-title {
        color: @text-color-label;
        font-size: 14px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .check-icon {
        position: absolute;
        top: 2px;
        right: 4px;
        display: none;
      }
    }
  }
  .document-fileName {
    cursor: pointer;
    &.link-fullName {
      &:hover {
        color: @primary-color;
      }
    }
    .file-img {
      width: 16px;
      height: 16px;
      display: inline-block;
      vertical-align: -3px;
    }
  }
}

// 修改：更新拖拽高亮样式的选择器
.document-wrapper,
.cloud-disk-file-picker {
  // 为子元素的定位做准备
  position: relative;
}

// 定义一个名为 'drag-over-active' 的高亮类
.drag-over-active {
  // 使用 ::after 伪元素创建一个覆盖层
  &::after {
    content: '拖放到此处以上传'; // 覆盖层上显示的文字
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    // 视觉样式
    background-color: rgba(24, 144, 255, 0.1); // 淡蓝色半透明背景
    border: 2px dashed #1890ff; // 蓝色虚线边框
    border-radius: 8px;
    box-sizing: border-box;
    z-index: 10; // 确保在最上层显示

    // 文字和图标样式
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 500;
    color: #1890ff; // 蓝色文字
    pointer-events: none; // 关键：让覆盖层不影响鼠标事件
  }
}
</style>
