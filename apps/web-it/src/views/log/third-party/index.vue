<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ThirdPartyLogInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getThirdPartyListApi, getThirdPartyLogPageListApi } from '#/api';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'ApiSelect',
      fieldName: 'serviceName',
      label: '服务名称',
      componentProps: {
        api: getThirdPartyListApi,
        afterFetch(res: string[]) {
          const list: { label: string; value: string }[] = [];
          res.forEach((item) => {
            list.push({
              label: item,
              value: item,
            });
          });
          return list;
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'userName',
      label: '调用人',
    },
    {
      component: 'DateRangePicker',
      fieldName: 'callTime',
      label: '调用时间',
    },
  ],
  fieldMappingTime: [['callTime', ['callStartTime', 'callEndTime'], 'x']],
});
const gridOptions: VxeTableGridOptions<ThirdPartyLogInfo> = {
  columns: [
    { field: 'callTime', title: '调用时间' },
    { field: 'serviceName', title: '服务名称' },
    { field: 'callCount', title: '调用次数' },
    { field: 'status', title: '调用状态', formatter: ['formatBoolean', { true: '成功', false: '失败' }] },
    { field: 'userName', title: '调用人' },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getThirdPartyLogPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <Grid />
  </Page>
</template>

<style></style>
