<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { LogErrorInfo } from '#/api';

import { reactive } from 'vue';

import { confirm, Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { message, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { batchRemoveErrorLogApi, clearErrorLogApi, getErrorLogPageListApi } from '#/api';

import DetailPopup from './detail-popup.vue';

const state = reactive({
  loading: false,
});
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'keyword',
      label: '关键词',
    },
    {
      component: 'Input',
      fieldName: 'method',
      label: '请求方式',
    },
    {
      component: 'DateRangePicker',
      fieldName: 'operateTime',
      label: '操作时间',
      componentProps: {
        showTime: true,
      },
    },
  ],
  fieldMappingTime: [['operateTime', ['startTime', 'endTime'], 'x']],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions<LogErrorInfo> = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'serviceId', title: '服务Id' },
    { field: 'serverHost', title: '服务Host' },
    { field: 'serverIp', title: '服务Ip' },
    { field: 'remoteIp', title: '请求Ip' },
    { field: 'method', title: '请求方式' },
    { field: 'requestUri', title: '请求接口' },
    { field: 'createTime', title: '操作时间' },
    { field: 'userName', title: '操作人' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getErrorLogPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: LogErrorInfo) => {
  openDetailPopup(true, row);
};
const del = async () => {
  const rows = gridApi.grid.getCheckboxRecords();
  if (rows.length === 0) {
    return message.warning('请先选择一条数据');
  }
  await confirm('确认删除所选数据吗？', '确认删除');
  const ids = rows.map((row) => row.id);
  state.loading = true;
  try {
    await batchRemoveErrorLogApi(ids);
    message.success('删除成功');
    await gridApi.reload();
  } finally {
    state.loading = false;
  }
};
const clear = async () => {
  await confirm('确认清空所有数据吗？', '确认清空');
  state.loading = true;
  try {
    await clearErrorLogApi();
    message.success('清空成功');
    await gridApi.reload();
  } finally {
    state.loading = false;
  }
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button :loading="state.loading" type="primary" danger @click="del">删除</a-button>
          <a-button :loading="state.loading" danger @click="clear">一键清空</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <TypographyLink @click="viewDetail(row)">
          {{ $t('base.detail') }}
        </TypographyLink>
      </template>
    </Grid>
    <DetailPopup @register="registerDetail" />
  </Page>
</template>

<style></style>
