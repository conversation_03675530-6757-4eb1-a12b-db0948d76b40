import type { ConfigInfo, RegionInfo } from '@vben/types';

import { requestClient } from '#/api/request';

export async function getDictListApi() {
  return requestClient.get('/upms/dict/list');
}
// 获取公开配置信息
export async function getPublicConfigInfoApi(): Promise<ConfigInfo> {
  return requestClient.get('/upms/config/simple-list');
}
// 行政区划
export async function getRegionListApi(params: { city: string; province: string }): Promise<RegionInfo[]> {
  return requestClient.get('/upms/region/list', { params });
}
