const mockData = {
  pid: '76237320-27e1-4086-bb72-2fc0a75d5fe3',
  type: '编辑',
  title: '合同模板编辑',
  platform: 'User-Type',
  time: 111,
  serviceId: 'service-contract',
  serverIp: '************',
  serverHost: '************:80',
  status: '200',
  env: 'demo',
  remoteIp: '*************',
  remoteAddress: '未知',
  userAgent:
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  client: 'Chrome *********',
  os: 'Windows 10 (x64)',
  device: 'desktop',
  path: '/contract/template/update',
  requestUrl: '/contract/template/update?_t=1726209930',
  method: 'PUT',
  request:
    '{"pid":"4O2BNK0000000","templateName":"测试","classificationId":"3OT9H60000000","appCode":"factoring_application","appName":"保理系统","filePath":"file-tiewu/01040ae1a6984d68b33cf794c692d0be-4O2BNK0000000.docx","state":"0","createTime":1726209909000,"updateTime":1726209909000,"deleteFlag":0,"version":1,"approvalState":"1","isApproval":"0","sourceURI":"file-tiewu/01040ae1a6984d68b33cf794c692d0be-4O2BNK0000000.docx","paramList":[{"pid":"3SDIGS0000000","classificationId":"3OT9H60000000","paramId":"CSNZ20230831058GC94","createTime":*************,"createBy":"3QST1O0000000","updateTime":*************,"updateBy":"3QST1O0000000","createDept":"000000","updateDept":"000000","deleteFlag":0,"paramName":"保理申请人全称","paramCode":"transfer","paramDemo":"应收账款转让申请保理商审核意见","paramDescribe":"应收账款转让申请保理商审核意见","paramType":"STRING","appCode":"factoring_application","appName":"保理业务管理"},{"pid":"3SDIGS0000001","classificationId":"3OT9H60000000","paramId":"CSNZ20230831058GC96","createTime":*************,"createBy":"3QST1O0000000","updateTime":*************,"updateBy":"3QST1O0000000","createDept":"000000","updateDept":"000000","deleteFlag":0,"paramName":"融资款金额大写","paramCode":"financingAmountChn","paramDemo":"应收账款转让申请保理商审核意见","paramDescribe":"应收账款转让申请保理商审核意见","paramType":"STRING","appCode":"factoring_application","appName":"保理业务管理"},{"pid":"3SDIGS0000002","classificationId":"3OT9H60000000","paramId":"CSNZ20230831058GC97","createTime":*************,"createBy":"3QST1O0000000","updateTime":*************,"updateBy":"3QST1O0000000","createDept":"000000","updateDept":"000000","deleteFlag":0,"paramName":"融资利率（年化）","paramCode":"financingRate","paramDemo":"应收账款转让申请保理商审核意见","paramDescribe":"应收账款转让申请保理商审核意见","paramType":"STRING","appCode":"factoring_application","appName":"保理业务管理"},{"pid":"3SDIGS0000003","classificationId":"3OT9H60000000","paramId":"CSNZ20230831058GC98","createTime":*************,"createBy":"3QST1O0000000","updateTime":*************,"updateBy":"3QST1O0000000","createDept":"000000","updateDept":"000000","deleteFlag":0,"paramName":"融资期限","paramCode":"factoringTerm","paramDemo":"应收账款转让申请保理商审核意见","paramDescribe":"应收账款转让申请保理商审核意见","paramType":"STRING","appCode":"factoring_application","appName":"保理业务管理"},{"pid":"3SDIGS0000004","classificationId":"3OT9H60000000","paramId":"CSNZ20230831058GC99","createTime":*************,"createBy":"3QST1O0000000","updateTime":*************,"updateBy":"3QST1O0000000","createDept":"000000","updateDept":"000000","deleteFlag":0,"paramName":"保理公司全称","paramCode":"assigneeFullName","paramDemo":"应收账款转让申请保理商审核意见","paramDescribe":"应收账款转让申请保理商审核意见","paramType":"STRING","appCode":"factoring_application","appName":"保理业务管理"},{"pid":"3SDIGS0000005","classificationId":"3OT9H60000000","paramId":"CSNZ20231011057GC27","createTime":*************,"createBy":"3QST1O0000000","updateTime":*************,"updateBy":"3QST1O0000000","createDept":"000000","updateDept":"000000","deleteFlag":0,"paramName":"保理融资金额(万)","paramCode":"financingAmountWan","paramDemo":"放款凭证","paramDescribe":"放款凭证","paramType":"STRING","appCode":"factoring_application","appName":"保理业务管理"}]}',
  response:
    '{\n    "code": 200,\n    "msg": "操作成功",\n    "data": "4O2BNK0000000"\n}',
  userId: '3S6J6N0000000',
  userName: '王艺霖',
  account: 'wechat_1342',
  operateTime: 1_726_209_930_000,
  tenantId: '000000',
};

export default eventHandler(async () => {
  return useResponseSuccess(mockData);
});
