export default eventHandler(async () => {
  const listData = [
    {
      pid: 'ZR010',
      code: 'ACCESS_STATE',
      name: '准入状态',
      dictName: '未提交',
      dictValue: 'ZR010',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR020',
      code: 'PROJECT_MODE',
      name: '项目模式',
      dictName: '采购业务',
      dictValue: '10',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR021',
      code: 'PROJECT_MODE',
      name: '项目模式',
      dictName: '销售业务',
      dictValue: '20',
      sort: 2,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR022',
      code: 'PROJECT_MODE',
      name: '项目模式',
      dictName: '一般贸易',
      dictValue: '30',
      sort: 3,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR030',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '待提交',
      dictValue: '00',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR031',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '审批中',
      dictValue: '10',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR032',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '已生效',
      dictValue: '20',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR033',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '已完成',
      dictValue: '30',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR034',
      code: 'PROJECT_STATUS',
      name: '项目模式',
      dictName: '已作废',
      dictValue: '40',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR040',
      code: 'AUDIT_STATUS',
      name: '审批状态',
      dictName: '待审批',
      dictValue: '00',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR041',
      code: 'AUDIT_STATUS',
      name: '审批状态',
      dictName: '已通过',
      dictValue: '10',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'ZR042',
      code: 'AUDIT_STATUS',
      name: '审批状态',
      dictName: '已驳回',
      dictValue: '20',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO001',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '投标信息',
      dictValue: '00',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO002',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '订单信息',
      dictValue: '01',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO003',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '进口信息',
      dictValue: '02',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO004',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '出口信息',
      dictValue: '03',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO005',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '合同信息',
      dictValue: '04',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO006',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '物流信息',
      dictValue: '05',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO007',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '仓储信息',
      dictValue: '06',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO008',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '货权信息',
      dictValue: '07',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO009',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '处置信息',
      dictValue: '08',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO010',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '资金信息',
      dictValue: '09',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO011',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '发票信息',
      dictValue: '10',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'NO012',
      code: 'NODE_TYPE',
      name: '环节配置',
      dictName: '融资信息',
      dictValue: '11',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'DM001',
      code: 'DELIVERY_METHOD',
      name: '交货方式',
      dictName: '混合',
      dictValue: '01',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'DM002',
      code: 'DELIVERY_METHOD',
      name: '交货方式',
      dictName: '非直发',
      dictValue: '02',
      sort: 2,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'DM003',
      code: 'DELIVERY_METHOD',
      name: '交货方式',
      dictName: '直发',
      dictValue: '03',
      sort: 3,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'BP001',
      code: 'BID_PROGRESS',
      name: '投标进度',
      dictName: '进行中',
      dictValue: '01',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'BP002',
      code: 'BID_PROGRESS',
      name: '投标进度',
      dictName: '已完成',
      dictValue: '02',
      sort: 2,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'BR001',
      code: 'BID_RESULT',
      name: '投标结果',
      dictName: '未中标',
      dictValue: '01',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'BR002',
      code: 'BID_RESULT',
      name: '投标结果',
      dictName: '已中标',
      dictValue: '02',
      sort: 2,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'RT01',
      code: 'RELATED_TYPE',
      name: '关联类型',
      dictName: '项目',
      dictValue: '01',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'RT02',
      code: 'RELATED_TYPE',
      name: '关联类型',
      dictName: '采购订单',
      dictValue: '02',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'RT03',
      code: 'RELATED_TYPE',
      name: '关联类型',
      dictName: '销售订单',
      dictValue: '03',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'C01',
      code: 'CURRENCY',
      name: '币种',
      dictName: '人民币',
      dictValue: '01',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'IM01',
      code: 'INVOICE_METHOD',
      name: '收票方式',
      dictName: '无限制',
      dictValue: '01',
      sort: 1,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'IM02',
      code: 'INVOICE_METHOD',
      name: '收票方式',
      dictName: '先收票后付款',
      dictValue: '02',
      sort: 2,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
    {
      pid: 'IM03',
      code: 'INVOICE_METHOD',
      name: '收票方式',
      dictName: '先付款后收票',
      dictValue: '03',
      sort: 3,
      editable: true,
      enable: true,
      appCode: 'risk_biz_application',
      deleteFlag: 0,
      type: 'SYS',
    },
  ];
  return useResponseSuccess(listData);
});
