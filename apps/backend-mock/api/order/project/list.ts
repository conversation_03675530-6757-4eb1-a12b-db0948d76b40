import { fakerZH_CN as faker } from '@faker-js/faker';

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem = {
      pid: faker.string.uuid(),
      code: faker.string.uuid(),
      name: faker.commerce.productName(),
      auditStatus: faker.helpers.arrayElement(['00', '10', '20']),
      reason: faker.lorem.lines(),
      status: faker.helpers.arrayElement(['00', '10', '20', '30', '40']),
      mode: faker.helpers.arrayElement(['10', '20', '30']),
      tradeExecutionEnterpriseName: faker.company.name(),
      upEnterpriseName: faker.company.name(),
      downEnterpriseName: faker.company.name(),
      projectType: faker.helpers.arrayElement(['类型A', '类型B', '类型C']),
      businessDate: faker.date.past().toISOString(),
      createDepartment: faker.commerce.department(),
      createBy: faker.person.fullName(),
      createTime: faker.date.recent().toISOString(),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(20);

export default eventHandler(async () => {
  const listData = structuredClone(mockData);
  return useResponseSuccess(listData);
});
