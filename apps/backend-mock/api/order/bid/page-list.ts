import { fakerZH_CN as faker } from '@faker-js/faker';

function generateMockDataList(count: number) {
  const dataList = [];
  const bindProgressValues = ['01', '02'];
  const bidResultValues = ['01', '02'];
  const relatedTypeValues = ['', '01', '03'];
  for (let i = 0; i < count; i++) {
    const dataItem = {
      pid: faker.string.uuid(),
      code: faker.string.uuid(),
      name: faker.commerce.productName(),
      auditStatus: faker.helpers.arrayElement(['00', '10', '20']),
      bindProgress: faker.helpers.arrayElement(bindProgressValues),
      bindResult: faker.helpers.arrayElement(bidResultValues),
      relatedType: faker.helpers.arrayElement(relatedTypeValues),
      relatedNumber: faker.string.uuid(),
      tenderName: faker.commerce.productName(),
      tenderEntity: faker.company.name(),
      totalQuotedAmount: faker.commerce.price({ min: 1000, max: 9999 }),
      createDepartment: faker.commerce.department(),
      createBy: faker.person.fullName(),
      createTime: faker.date.recent().toISOString(),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(20);

export default eventHandler(async (event) => {
  const { page, pageSize } = getQuery(event);
  const listData = structuredClone(mockData);
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
