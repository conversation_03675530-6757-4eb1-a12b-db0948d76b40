export default eventHandler(async (event) => {
  const listData = {
    list: [
      {
        id: '684372947510537605',
        fullName: '测试文件夹',
        type: 0,
        creatorTime: 1_745_303_628_000,
        isShare: null,
        fileSize: null,
        parentId: '0',
        fileExtension: null,
        uploaderUrl: null,
        filePath: null,
        isPreview: null,
        creatorUserId: '349057407209541',
        shareTime: null,
      },
      {
        id: '684371976726293893',
        fullName: 'country-9401550_640.jpg',
        type: 1,
        creatorTime: 1_745_303_396_000,
        isShare: null,
        fileSize: '36537',
        parentId: '0',
        fileExtension: 'jpg',
        uploaderUrl: 'https://java.jnpfsoft.com/api/file/Image/document/68073764c5d8f75674a36cce.jpg',
        filePath: '68073764c5d8f75674a36cce.jpg',
        isPreview: 'jpg',
        creatorUserId: '349057407209541',
        shareTime: null,
      },
      {
        id: '682940282181822917',
        fullName: 'cliffs-9444605_640.jpg',
        type: 1,
        creatorTime: 1_744_962_054_000,
        isShare: null,
        fileSize: '98130',
        parentId: '0',
        fileExtension: 'jpg',
        uploaderUrl: 'https://java.jnpfsoft.com/api/file/Image/document/68020205c5d8511456f9b2d8.jpg',
        filePath: '68020205c5d8511456f9b2d8.jpg',
        isPreview: 'jpg',
        creatorUserId: '349057407209541',
        shareTime: null,
      },
    ],
  };
  return useResponseSuccess(listData);
});
