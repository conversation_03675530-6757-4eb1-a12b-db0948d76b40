export default eventHandler(async (event) => {
  const { page, pageSize } = getQuery(event);
  const listData = [
    {
      pid: 'wlwjg_temp',
      code: 'wlwjg_temp',
      name: '物联网监管系统',
      iconType: '1',
      icon: 'ant-design:android-filled',
      appType: 'PLATFORM',
      url: 'http://supervisor.lr.demo2.fero.com.cn?demoLogin=eyJhY2NvdW50IjoiaHJkZW1vMyIsInBhc3N3b3JkIjoiMzQwZTA5Y2Q5MDk2ZjdlMSIsImhpcmVMaW5rSWQiOjUwM30=',
      topFlag: 0,
      enable: 1,
      createTime: 1_686_319_025_000,
      createBy: '3I0T2S0000000',
      updateTime: 1_703_460_281_000,
      updateBy: '3I0T2S0000000',
      version: 1,
      sort: 0,
    },
    {
      pid: 'policy',
      code: 'policy',
      name: '保险经纪业务',
      iconType: '1',
      icon: 'icon-jiaoyiguanlipingtai-o',
      appType: 'PLATFORM',
      url: 'http://policy.platform.ff360.com.cn',
      topFlag: 1,
      enable: 1,
      createTime: 1_731_470_461_000,
      createBy: '3I0T2S0000000',
      updateTime: 1_731_564_512_000,
      updateBy: '4T01KB0000000',
      version: 2,
      sort: 2,
    },
    {
      pid: 'pa_application',
      code: 'pa_application',
      name: '供应链数字化',
      iconType: '1',
      icon: ' icon-jiaoyiguanlipingtai-o',
      appType: 'PLATFORM',
      url: 'http://pa.yijia.fero.com.cn/visual-dashboard/worktable',
      topFlag: 1,
      enable: 1,
      createTime: 1_684_208_863_000,
      createBy: '000000',
      updateTime: 1_730_772_963_000,
      updateBy: '3I0T2S0000000',
      version: 9,
      sort: 10,
    },
    {
      pid: 'self_operated_application',
      code: 'self_operated_application',
      name: '自营贸易管理',
      iconType: '1',
      icon: 'icon-daikuanhexinx-o',
      appType: 'PLATFORM',
      url: 'http://self-operated.yijia.fero.com.cn',
      topFlag: 0,
      enable: 1,
      createTime: 1_685_689_854_000,
      createBy: '000000',
      updateTime: 1_712_247_520_000,
      updateBy: '3I0T2S0000000',
      version: 5,
      sort: 15,
    },
    {
      pid: 'factoring_application',
      code: 'factoring_application',
      name: '保理业务管理',
      iconType: '1',
      icon: 'icon-baolix-o',
      appType: 'PLATFORM',
      url: 'http://factoring.yijia.fero.com.cn/visual-dashboard/worktable',
      topFlag: 1,
      enable: 1,
      createTime: 1_686_035_710_000,
      createBy: '000000',
      updateTime: 1_713_489_591_000,
      updateBy: '3I0T2S0000000',
      version: 6,
      sort: 20,
    },
    {
      pid: 'risk_biz_application',
      code: 'risk_biz_application',
      name: '风控业务管理',
      iconType: '1',
      icon: ' icon-fengkongyinqing-o',
      appType: 'PLATFORM',
      url: 'http://risk-biz.yijia.fero.com.cn',
      topFlag: 1,
      enable: 1,
      createTime: 1_686_035_889_000,
      createBy: '000000',
      updateTime: 1_731_564_552_000,
      updateBy: '4T01KB0000000',
      version: 5,
      sort: 25,
    },
    {
      pid: 'middle_end_application',
      code: 'middle_end_application',
      name: '合同服务中台',
      iconType: '1',
      icon: ' icon-yewuzhuti-o',
      appType: 'PLATFORM',
      url: 'http://middle.yijia.fero.com.cn',
      topFlag: 1,
      enable: 1,
      createTime: 1_684_131_862_000,
      createBy: '000000',
      updateTime: 1_712_247_585_000,
      updateBy: '3I0T2S0000000',
      version: 4,
      sort: 30,
    },
    {
      pid: 'risk',
      code: 'risk',
      name: '风控引擎',
      iconType: '1',
      icon: 'icon-fengkongyinqing-o',
      appType: 'PLATFORM',
      url: 'http://risk2.demo.fero.com.cn/',
      topFlag: 1,
      enable: 1,
      createTime: 1_685_932_016_000,
      createBy: '000000',
      updateTime: 1_712_247_595_000,
      updateBy: '3I0T2S0000000',
      version: 3,
      sort: 35,
    },
    {
      pid: 'insight',
      code: 'insight',
      name: '数据分析监管',
      iconType: '1',
      icon: 'icon-chakan-o',
      appType: 'PLATFORM',
      url: 'https://insight.yijia.fero.com.cn',
      topFlag: 0,
      enable: 1,
      createTime: 1_711_685_086_000,
      createBy: '3I0T2S0000000',
      updateTime: 1_712_247_628_000,
      updateBy: '3I0T2S0000000',
      version: 2,
      sort: 40,
    },
    {
      pid: 'it_application',
      code: 'it_application',
      name: '系统服务中台',
      iconType: '1',
      icon: ' icon-jiaoyiguanlipingtai-o',
      appType: 'PLATFORM',
      url: 'http://it.yijia.fero.com.cn',
      topFlag: 1,
      enable: 1,
      createTime: 1_683_511_965_000,
      createBy: '000000',
      updateTime: 1_731_564_522_000,
      updateBy: '4T01KB0000000',
      version: 8,
      sort: 45,
    },
  ];
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
