import type { ComponentRecordType, GenerateMenuAndRoutesOptions, RouteRecordStringComponent } from '@vben/types';

import { generateAccessible } from '@vben/access';
import { preferences } from '@vben/preferences';
import { useUserStore } from '@vben/stores';
import { findDataByUrl } from '@vben/utils';

import { BasicLayout, IFrameView } from '#/layouts';

const forbiddenComponent = () => import('#/views/_core/fallback/forbidden.vue');

async function generateAccess(options: GenerateMenuAndRoutesOptions) {
  const pageMap: ComponentRecordType = import.meta.glob('../views/**/*.vue');

  const layoutMap: ComponentRecordType = {
    BasicLayout,
    IFrameView,
  };

  return await generateAccessible(preferences.app.accessMode, {
    ...options,
    fetchMenuListAsync: async () => {
      // message.loading({
      //   content: `${$t('common.loadingMenu')}...`,
      //   duration: 1.5,
      // });
      const allMenuList = useUserStore().menuList;
      let appMenu = findDataByUrl(window.location.href, allMenuList);

      if (!appMenu) {
        appMenu = allMenuList.find((item: any) => {
          return item.name === import.meta.env.VITE_APP_KEY;
        });
      }
      return appMenu ? (appMenu.children as RouteRecordStringComponent[]) : [];
      // return await getAllMenusApi();
    },
    // 可以指定没有权限跳转403页面
    forbiddenComponent,
    // 如果 route.meta.menuVisibleWithForbidden = true
    layoutMap,
    pageMap,
  });
}

export { generateAccess };
