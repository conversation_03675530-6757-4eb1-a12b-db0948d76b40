import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '应收账款管理',
    },
    name: 'AccountsReceivable',
    path: '/accountsReceivable',
    children: [
      {
        name: 'AccountsReceivableEnter',
        path: '/accountsReceivable/enter',
        component: () => import('#/views/accounts-receivable/enter/index.vue'),
        meta: {
          title: '应收账款录入',
        },
      },
      {
        name: 'AccountsReceivablePool',
        path: '/accountsReceivable/pool',
        component: () => import('#/views/accounts-receivable/pool/index.vue'),
        meta: {
          title: '应收账款池',
        },
      },
    ],
  },
];

export default routes;
