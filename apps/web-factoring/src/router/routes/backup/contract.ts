import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '合同管理',
    },
    name: 'FctContract',
    path: '/contract',
    children: [
      {
        name: 'ContractInfo',
        path: '/contract/info',
        component: () => import('#/views/contract/info/index.vue'),
        meta: {
          title: '合同信息',
        },
      },
    ],
  },
];

export default routes;
