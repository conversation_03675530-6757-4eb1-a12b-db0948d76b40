import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '企业管理',
    },
    name: 'FctCompanyManage',
    path: '/companyManage',
    children: [
      {
        name: 'CompanyRating',
        path: '/companyManage/companyRating',
        component: () => import('#/views/company-rating/index.vue'),
        meta: {
          title: '企业评级',
        },
      },
    ],
  },
];

export default routes;
