import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '额度管理',
    },
    name: 'LimitAdmin',
    path: '/limitAdmin',
    children: [
      {
        name: 'RegionalCalculation',
        path: '/limitAdmin/regionalCalculation',
        component: () => import('#/views/limit-admin/area/index.vue'),
        meta: {
          title: '地区额度测算',
        },
      },
      {
        name: 'EnterpriseCalculation',
        path: '/limitAdmin/enterpriseCalculation',
        component: () => import('#/views/limit-admin/company/index.vue'),
        meta: {
          title: '企业额度测算',
        },
      },
    ],
  },
];

export default routes;
