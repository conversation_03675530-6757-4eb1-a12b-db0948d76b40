import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: '',
      title: $t('page.fund.title'),
    },
    name: 'Fund',
    path: '/fund',
    children: [
      {
        name: 'FundPaymentApply',
        path: '/fund/payment-apply',
        component: () => import('#/views/fund/payment-apply/index.vue'),
        meta: {
          icon: '',
          title: $t('page.fund.paymentApply'),
        },
      },
      {
        name: 'FundPaymentRecord',
        path: '/fund/payment-record',
        component: () => import('#/views/fund/payment-record/index.vue'),
        meta: {
          icon: '',
          title: $t('page.fund.paymentRecord'),
        },
      },
      {
        name: 'FundRepaymentPlan',
        path: '/fund/repayment-plan',
        component: () => import('#/views/fund/repayment-plan/index.vue'),
        meta: {
          icon: '',
          title: $t('page.fund.repaymentPlan'),
        },
      },
      {
        name: 'FundRepaymentRecord',
        path: '/fund/repayment-record',
        component: () => import('#/views/fund/repayment-record/index.vue'),
        meta: {
          icon: '',
          title: $t('page.fund.repaymentRecord'),
        },
      },
      {
        name: 'FundRepaymentChange',
        path: '/fund/repayment-change',
        component: () => import('#/views/fund/repayment-change/index.vue'),
        meta: {
          icon: '',
          title: $t('page.fund.repaymentChange'),
        },
      },
      {
        name: 'FundInvoiceApply',
        path: '/fund/invoice-apply',
        component: () => import('#/views/fund/invoice-apply/index.vue'),
        meta: {
          icon: '',
          title: $t('page.fund.invoiceApply'),
        },
      },
      {
        name: 'FundSettlementRecord',
        path: '/fund/settlement-record',
        component: () => import('#/views/fund/settlement-record/index.vue'),
        meta: {
          icon: '',
          title: $t('page.fund.settlementRecord'),
        },
      },
    ],
  },
];

export default routes;
