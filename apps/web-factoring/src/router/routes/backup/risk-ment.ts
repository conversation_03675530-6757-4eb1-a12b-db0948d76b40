import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '风险管理',
    },
    name: 'RiskAdmin',
    path: '/riskAdmin',
    children: [
      {
        name: 'WarningRules',
        path: '/riskAdmin/warningRules',
        component: () => import('#/views/risk-admin/warningRules/index.vue'),
        meta: {
          title: '预警规则',
        },
      },
      {
        name: 'RiskWarning',
        path: '/riskAdmin/riskWarning',
        component: () => import('#/views/risk-admin/riskWarning/index.vue'),
        meta: {
          title: '风险预警',
        },
      },
      {
        name: 'PoolWarning',
        path: '/riskAdmin/poolWarning',
        component: () => import('#/views/risk-admin/poolWarning/index.vue'),
        meta: {
          title: '池保理预警',
        },
      },
      {
        name: 'RepaymentReminder',
        path: '/riskAdmin/repayment-reminder',
        component: () => import('#/views/risk-admin/repayment-reminder/index.vue'),
        meta: {
          title: '还款提醒',
        },
      },
      {
        name: 'OverdueCollection',
        path: '/riskAdmin/overdue-collection',
        component: () => import('#/views/risk-admin/overdue-collection/index.vue'),
        meta: {
          title: '逾期催收',
        },
      },
    ],
  },
];

export default routes;
