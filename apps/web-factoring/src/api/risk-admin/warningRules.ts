import type { BaseDataParams } from '@vben/types';

import { requestClient } from '#/api/request';
// 应收账款分页视图对象
export interface warningRulesPageVO extends BaseDataParams {
  /**
   * 主键
   */
  id?: number;
  /**
   * 预警规则描述
   */
  ruleDesc?: string;
  /**
   * 预警规则名称
   */
  ruleName?: string;
  /**
   * 状态 0-禁用 1-启用
   */
  status?: string;
  /**
   * 预警等级
   */
  warningLevel?: string;
  /**
   * 状态 0-禁用 1-启用
   */
  checked?: boolean;

  [property: string]: any;
}

export interface rulesVo {
  /**
   * 主键
   */
  id: number | undefined;

  /**
   * 是否短信提醒
   */
  isSms: number | undefined;

  /**
   * 通知角色
   */
  notifyRole: string | string[] | undefined;
  /**
   * 短信接收人用户ID
   */
  smsUserIdList: string[];
  /**
   * 短信接收人用户名称
   */
  smsUserNameList: string[];
  /**
   * 触发条件值
   */
  value: number;
  /**
   * 预警等级
   */
  warningLevel: string;
  [property: string]: any;
}

export interface maintenanceVo {
  /**
   * 主键
   */
  id?: number;
  /**
   * 净资产
   */
  netAsset?: number;
  /**
   * 风险资产
   */
  riskAsset?: number;
  [property: string]: any;
}

export async function getWarningRulesListApi() {
  return requestClient.get<warningRulesPageVO[]>('/factoring/warn/rule/list');
}

export async function enableOrDisableWarningRulesApi(id: number) {
  return requestClient.get(`/factoring/warn/rule/enableOrDisable/${id}`);
}
export async function getWarningRulesDetailApi(id: number) {
  return requestClient.get(`/factoring/warn/rule/detail/${id}`);
}
export async function editWarningRulesApi(data: rulesVo) {
  return requestClient.post('/factoring/warn/rule/edit', data);
}

export async function editMaintenanceRulesApi(data: maintenanceVo) {
  return requestClient.post('/factoring/warn/rule/maintenance', data);
}

export async function getMaintenanceRulesApi() {
  return requestClient.get<maintenanceVo>('/factoring/warn/rule/indicator');
}
// // 新增应收账款

// // 编辑应收账款
// export async function editReceivableApi(data: ReceivableVO) {
//   return requestClient.post<string>('/factoring/receivable/edit', data);
// }

// // 删除应收账款
// export async function delReceivableApi(id: string) {
//   return requestClient.get(`/factoring/receivable/delete/${id}`);
// }

// export async function invoiceOcrApi(data: any) {
//   return requestClient.post<InvoiceOcrBatchVO>('/infra/thirdparty/invoice/ocr/batch', data);
// }
// export async function invoiceCheckApi(data: ReceivableInvoiceVO) {
//   return requestClient.post<ReceivableInvoiceVO>('/infra/thirdparty/invoice/check', data);
// }
// export async function invoiceImportApi(data: any, config: RequestClientConfig) {
//   return requestClient.upload('/factoring/receivable/invoice/import', data, config);
// }
