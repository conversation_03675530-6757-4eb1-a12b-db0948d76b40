import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
// 应收账款分页视图对象
export interface poolWarningPageVO extends BaseDataParams {
  /**
   * 预警相关企业名称
   */
  companyName?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 预警规则描述
   */
  ruleDesc?: string;
  /**
   * 监管类型规则名称
   */
  ruleName?: string;
  /**
   * 处置状态
   */
  status?: string;
  /**
   * 预警日期
   */
  warningDate?: Date;
  /**
   * 预警状态
   */
  warningStatus?: string;
  [property: string]: any;
}

export interface poolSupervisionDisposalVo {
  id?: number;
  monitoringMeasures?: string[];
  remarks?: string;
}
// 获取分页列表
export async function getPoolWarningPageListApi(params: PageListParams) {
  return requestClient.get<poolWarningPageVO>('/factoring/warn/pool/page', { params });
}
export async function editPoolSupervisionDisposalApi(data: poolSupervisionDisposalVo) {
  return requestClient.post('/factoring/warn/pool/handle', data);
}
