import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface DistrictQuotaInfo {
  /**
   * 地级市
   */
  cityName?: string;
  /**
   * 区县
   */
  districtName?: string;
  /**
   * 可用额度余额（元）
   */
  regionAvailableAmount?: string;
  /**
   * 最近年度一般公共预算收入（元）
   */
  regionBudgetAmount?: string;
  /**
   * 地区额度上限（元）
   */
  regionCreditAmount?: string;
  /**
   * 区域授信比例（%）
   */
  regionCreditRatio?: string;
  /**
   * 已结清额度（元）
   */
  regionSettlementAmount?: string;
  /**
   * 存量业务余额（元）
   */
  regionStockBalanceAmount?: string;
  /**
   * 已用信额度（元）
   */
  regionUsedAmount?: string;
  [property: string]: any;
}

// 获取分页列表
export async function getDistrictQuotaListApi(params: PageListParams) {
  return requestClient.get<DistrictQuotaInfo[]>('/factoring/ledger/limit/region/page', { params });
}

// 导出
export async function exportLedgerRegionApi(data: DistrictQuotaInfo) {
  return requestClient.downloadAndSave('/factoring/ledger/limit/region/export', {
    config: { params: data },
  });
}
