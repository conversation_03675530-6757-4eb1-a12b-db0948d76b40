import type { RequestClientConfig } from '@vben/request';
import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
// 应收账款分页视图对象
export interface ReceivablePageVO extends BaseDataParams {
  // 业务类型
  bizType?: string;
  // 债权人
  creditorName?: string;
  // 债务人
  debtorName?: string;
  // 主键
  id?: number | undefined;
  // 关联项目
  projectName?: string;
  // 应收账款金额
  receivableAmount?: number;
  // 应收账款到期日
  receivableDueDate?: Date;
  // 应收账款名称
  receivableName?: string;
  // 操作状态
  status?: string;
  // 中登网登记状态
  zdStatus?: string;
}

// 发票信息视图对象
export interface ReceivableInvoiceVO {
  fileId?: string;
  // 购买方
  buyerName?: string;
  // 校验码后6位
  checkCode?: string;
  // 主键
  id?: number | undefined;
  // 发票代码
  invoiceCode?: string;
  // 开票日期
  invoiceDate?: Date | string;
  // 发票号码
  invoiceNumber?: string;
  // 发票类型
  invoiceType?: string;
  // 应收账款ID
  receivableId?: number;
  // 销售方
  sellerName?: string;
  // 不含税金额
  totalAmount?: number;
  // 含税金额
  totalAmountTax?: number;
  // 验真结果
  verifyResult?: string;
}

// 合同信息视图对象
export interface ReceivableContractVO {
  // 基础合同编号
  contractCode?: string;
  // 基础合同名称
  contractName?: string;
  // 基础合同类型
  contractType?: string;
  // 主键
  id?: number | undefined;
  // 应收账款ID
  receivableId?: number;
  // 合同签署日期
  signedDate?: Date;
  // 合同金额
  totalAmount?: number;
  // 拟转让应收账款金额
  transferAmount?: number;
  // 未付款项金额
  unpaidAmount?: number;
  // 凭证名称
  voucherName?: string;
  // 凭证号码
  voucherNumber?: string;
}

export interface ReceivableCompanyVO {
  companyCode?: string;
  companyName?: string;
}

// 应收账款视图对象
export interface ReceivableVO {
  attachmentList: any[];
  // 关联项目id
  projectId?: string[];

  // 关联项目名称
  projectName?: string;
  // 所属ABS基础资产ID
  absBasisAssetId?: number;
  // 业务类型
  bizType?: string;
  // 基础合同列表
  contractList?: ReceivableContractVO[];
  // 债权人、债务人关系
  creditorDebtorDel?: string;
  // 债权人
  creditorList?: ReceivableCompanyVO[];
  creditorName?: string;
  // 债务人
  debtorList?: ReceivableCompanyVO[];
  debtorName?: string;

  // 主键
  id?: number | undefined;
  // 发票列表
  invoiceList?: ReceivableInvoiceVO[];
  // 应收账款金额
  receivableAmount?: number;
  // 应收账款编号
  receivableCode?: string;
  // 应收账款到期日
  receivableDueDate?: string;
  // 应收账款名称
  receivableName?: string;
  // 应收账款期限(月)
  receivableTerm?: number;
  // 应收账款说明
  remarks?: string;
  // 操作状态
  status?: string;
  // 关联登记编号
  zdCode?: string;
  // 中登网登记状态
  zdStatus?: string;
  isSubmit?: boolean;
}

export interface InvoiceOcrBatchVO {
  /**
   * 失败识别数量
   */
  failedCount?: number;
  /**
   * 失败的文件ID列表
   */
  failedFileIds?: number[];
  /**
   * 成功识别数量
   */
  successCount?: number;
  /**
   * 成功识别的结果列表
   */
  successResults?: ReceivableInvoiceVO[];
  /**
   * 总识别数量
   */
  totalCount?: number;
}

// 获取应收账款分页列表
export async function getReceivablePageListApi(params: PageListParams) {
  return requestClient.get<ReceivablePageVO[]>('/factoring/receivable/page', { params });
}

// 应收账款详情
export async function infoReceivableApi(id: number) {
  return requestClient.get<ReceivableVO>(`/factoring/receivable/detail/${id}`);
}

// 新增应收账款
export async function addReceivableApi(data: ReceivableVO) {
  return requestClient.post<string>('/factoring/receivable/add', data);
}

// 编辑应收账款
export async function editReceivableApi(data: ReceivableVO) {
  return requestClient.post<string>('/factoring/receivable/edit', data);
}

// 删除应收账款
export async function delReceivableApi(id: string) {
  return requestClient.get(`/factoring/receivable/delete/${id}`);
}

export async function invoiceOcrApi(data: any) {
  return requestClient.post<InvoiceOcrBatchVO>('/infra/thirdparty/invoice/ocr/batch', data);
}
export async function invoiceCheckApi(data: ReceivableInvoiceVO) {
  return requestClient.post<ReceivableInvoiceVO>('/infra/thirdparty/invoice/check', data, {
    responseReturn: 'body',
  });
}
export async function invoiceBatchCheckApi(data: any) {
  return requestClient.post<ReceivableInvoiceVO>('/infra/thirdparty/invoice/check/batch', data);
}
export async function invoiceImportApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/factoring/receivable/invoice/import', data, config);
}
export async function invoiceDownloadTemplateApi() {
  return requestClient.downloadAndSave('/factoring/receivable/download');
}
