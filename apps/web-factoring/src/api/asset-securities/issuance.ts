import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface AssetIssueInfo {
  /**
   * 关联ABS项目ID
   */
  absProjectId?: number;
  /**
   * 信用支持（%）
   */
  creditSupport?: number;
  /**
   * 当前余额（万元）
   */
  currentBalance?: number;
  /**
   * 披露材料文件ID
   */
  disclosureMaterialFileId?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 利率类型
   */
  interestRateType?: string;
  /**
   * 发行金额（万元）
   */
  issueAmount?: number;
  /**
   * 发行登记编号
   */
  issueCode?: string;
  /**
   * 发行日期
   */
  issueDate?: string;
  /**
   * 发行利率（%）
   */
  issueRate?: number;
  /**
   * 发行起始日
   */
  issueStartDate?: string;
  /**
   * 监管机构
   */
  regulatoryAgency?: string;
  /**
   * 还本方式
   */
  repaymentMethod?: string;
  /**
   * ABS发行证券代码
   */
  securityCode?: string;
  /**
   * ABS发行证券名称
   */
  securityName?: string;
  /**
   * 特殊条款
   */
  specialTerms?: string;
  /**
   * 状态
   */
  status?: string;
  /**
   * 交易场所
   */
  tradingVenue?: string;
  /**
   * 分层比例（%）
   */
  trancheRatio?: number;
}

// 获取分页列表
export async function getAssetIssuePageListApi(params: PageListParams) {
  return requestClient.get<AssetIssueInfo[]>('/factoring/abs/issue/page', { params });
}

// 发行
export async function editAssetIssueApi(data: AssetIssueInfo) {
  return requestClient.post<AssetIssueInfo>('/factoring/abs/issue/issue', data);
}

// 上传信息披露材料
export async function issueUploadApi(data: AssetIssueInfo) {
  return requestClient.post<AssetIssueInfo>('/factoring/abs/issue/upload', data);
}

// 获取详情
export async function getAssetIssueInfoApi(id: number) {
  return requestClient.get(`/factoring/abs/issue/detail/${id}`);
}
