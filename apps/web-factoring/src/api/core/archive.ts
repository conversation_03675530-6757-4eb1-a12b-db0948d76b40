import type { RequestClientConfig } from '@vben/request';

import { requestClient } from '#/api/request';

/**
 * ArchiveTreeInfo，数据
 */
export interface ArchiveTreeInfo {
  // 子节点列表，递归结构
  children?: ArchiveTreeInfo[];
  // 如果节点是文件夹，这里存放文件夹ID
  folderId?: number;
  // 树节点的唯一Key，建议使用'类型-ID'格式，如 'project-001', 'folder-101'
  key: string;
  // 存放项目ID
  projectId: number;
  // 节点显示的标题
  title?: string;
  // 节点类型 (PROJECT, FOLDER)，用于前端显示不同图标
  type?: string;
  [property: string]: any;
}

export async function getArchiveTreeApi(params: { projectId: number }) {
  return requestClient.get<ArchiveTreeInfo[]>('/infra/archive/tree', { params });
}
export async function confirmArchiveFileApi(data: { ids: number[] }) {
  return requestClient.post('/infra/archive/confirm-archive', data);
}
export async function getArchiveListApi(params: { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/archive/list', { params });
}
export async function getArchiveMyShareListApi(params: { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/archive/shares/list/my', { params });
}
export async function getArchiveShareToMeListApi(params: { keyword?: string; parentId: number; shareId?: number }) {
  return requestClient.get('/infra/archive/shares/list/to-my', { params });
}
export async function getArchiveRecycleListApi(params: { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/archive/trash/list', { params });
}
export async function createArchiveFolderApi(data: { fileName: string; parentId: number }) {
  return requestClient.post('/infra/archive/create-folder', data);
}
export async function shareArchiveFileApi(data: { ids: number[]; userIds: number[] }) {
  return requestClient.post('/infra/archive/shares/create', data);
}
export async function getArchiveFileInfoApi(params: { id: number }) {
  return requestClient.get('/infra/archive/detail', { params });
}
export async function renameArchiveFileApi(params: { fileName: string; id: number }) {
  return requestClient.post('/infra/archive/rename', {}, { params });
}
export async function delArchiveFileApi(data: { ids: number[] }) {
  return requestClient.post('/infra/archive/delete-batch', data);
}
export async function getArchiveShareDetail(params: { id: number }) {
  return requestClient.get('/infra/archive/shares/detail', { params });
}
export async function editShareArchiveFileApi(data: { ids: number[]; userIds: number[] }) {
  return requestClient.post('/infra/archive/shares/adjust', { id: data.ids[0], userIds: data.userIds });
}
export async function cancelArchiveShareApi(data: { ids: number[] }) {
  return requestClient.post('/infra/archive/shares/cancel', data);
}
export async function uploadArchiveFileApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/infra/archive/upload', data, config);
}
export async function preCheckArchiveFileApi(params: { sliceHash: string }) {
  return requestClient.get('/infra/archive/pre-check/slice', { params });
}
export async function checkArchiveFileApi(params: { folderId: number; fullHash: string; newFileName: string }) {
  return requestClient.get('/infra/archive/pre-check/full', { params, responseReturn: 'body' });
}
export async function downloadArchiveFileApi(params: { id: number }) {
  return requestClient.get('/infra/archive/download-link', { params });
}
export async function downloadArchiveMultiFileApi(params: { ids: number[] }) {
  return requestClient.post('/infra/archive/download-zip-link', params);
}
export async function getArchiveFolderTreeApi(params: { projectId: number }) {
  return requestClient.get('/infra/archive/folder/tree', { params });
}
export async function moveArchiveFileToApi(data: { ids: number[]; toId: number }) {
  return requestClient.post('/infra/archive/move', data);
}
export async function restoreArchiveFileApi(data: { ids: number[] }) {
  return requestClient.post('/infra/archive/trash/restore', data);
}
export async function confirmDelArchiveFileApi(data: { ids: number[] }) {
  return requestClient.post('/infra/archive/trash/delete', data);
}
export async function calculateArchiveFolderApi(params: { folderId: number }) {
  return requestClient.get('/infra/archive/folder/size', { params });
}
