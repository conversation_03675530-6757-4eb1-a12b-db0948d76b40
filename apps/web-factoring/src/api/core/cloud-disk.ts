import type { RequestClientConfig } from '@vben/request';

import { requestClient } from '#/api/request';

export async function getCloudDiskListApi(params: { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/drive/list', { params });
}
export async function getCloudDiskMyShareListApi(params: { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/drive/shares/list/my', { params });
}
export async function getCloudDiskShareToMeListApi(params: { keyword?: string; parentId: number; shareId?: number }) {
  return requestClient.get('/infra/drive/shares/list/to-my', { params });
}
export async function getCloudDiskRecycleListApi(params: { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/drive/trash/list', { params });
}
export async function createCloudDiskFolderApi(data: { fileName: string; parentId: number }) {
  return requestClient.post('/infra/drive/create-folder', data);
}
export async function shareCloudDiskFileApi(data: { ids: number[]; userIds: number[] }) {
  return requestClient.post('/infra/drive/shares/create', data);
}
export async function getCloudDiskFileInfoApi(params: { id: number }) {
  return requestClient.get('/infra/drive/detail', { params });
}
export async function renameCloudDiskFileApi(params: { fileName: string; id: number }) {
  return requestClient.post('/infra/drive/rename', {}, { params });
}
export async function delCloudDiskFileApi(data: { ids: number[] }) {
  return requestClient.post('/infra/drive/delete-batch', data);
}
export async function getCloudDiskShareDetail(params: { id: number }) {
  return requestClient.get('/infra/drive/shares/detail', { params });
}
export async function editShareCloudDiskFileApi(data: { ids: number[]; userIds: number[] }) {
  return requestClient.post('/infra/drive/shares/adjust', { id: data.ids[0], userIds: data.userIds });
}
export async function cancelCloudDiskShareApi(data: { ids: number[] }) {
  return requestClient.post('/infra/drive/shares/cancel', data);
}
export async function uploadCloudDiskFileApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/infra/drive/upload', data, config);
}
export async function preCheckCloudDiskFileApi(params: { sliceHash: string }) {
  return requestClient.get('/infra/drive/pre-check/slice', { params });
}
export async function checkCloudDiskFileApi(params: { folderId: number; fullHash: string; newFileName: string }) {
  return requestClient.get('/infra/drive/pre-check/full', { params, responseReturn: 'body' });
}
export async function downloadCloudDiskFileApi(params: { id: number }) {
  return requestClient.get('/infra/drive/download-link', { params });
}
export async function downloadCloudDiskMultiFileApi(params: { ids: number[] }) {
  return requestClient.post('/infra/drive/download-zip-link', params);
}
export async function getCloudDiskFolderTreeApi() {
  return requestClient.get('/infra/drive/folder/tree');
}
export async function moveCloudDiskFileToApi(data: { ids: number[]; toId: number }) {
  return requestClient.post('/infra/drive/move', data);
}
export async function restoreCloudDiskFileApi(data: { ids: number[] }) {
  return requestClient.post('/infra/drive/trash/restore', data);
}
export async function confirmDelCloudDiskFileApi(data: { ids: number[] }) {
  return requestClient.post('/infra/drive/trash/delete', data);
}
export async function calculateCloudDiskFolderApi(params: { folderId: number }) {
  return requestClient.get('/infra/drive/folder/size', { params });
}
