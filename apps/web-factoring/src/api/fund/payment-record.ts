import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface PaymentRecordInfo {
  calculation?: PaymentConfirmCalculationBO;
  /**
   * 付款记录编号
   */
  confirmCode?: string;
  /**
   * 确认投放金额
   */
  confirmInvestAmount?: number;
  /**
   * 确认投放日期
   */
  confirmInvestDate?: Date;
  /**
   * 实际付款方式
   */
  confirmPaymentMethod?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 收款单位银行账号
   */
  payeeBankAccount?: string;
  /**
   * 收款单位开户行名称
   */
  payeeBankBranch?: string;
  /**
   * 收款单位银行ID
   */
  payeeBankId?: number;
  /**
   * 收款单位Code
   */
  payeeCompanyCode?: string;
  /**
   * 收款单位名称
   */
  payeeCompanyName?: string;
  /**
   * 付款单位银行账号
   */
  payerBankAccount?: string;
  /**
   * 付款单位开户行名称
   */
  payerBankBranch?: string;
  /**
   * 付款单位银行ID
   */
  payerBankId?: number;
  /**
   * 付款单位Code
   */
  payerCompanyCode?: string;
  /**
   * 付款单位名称
   */
  payerCompanyName?: string;
  /**
   * 付款申请ID
   */
  paymentApplyId?: number;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 上传付款凭证文件ID
   */
  voucherFileId?: number;
  /**
   * 上传付款凭证文件名称
   */
  voucherFileName?: string;
  [property: string]: any;
}

/**
 * PaymentConfirmCalculationBO，付款记录试算
 */
export interface PaymentConfirmCalculationBO {
  /**
   * 确认投放金额
   */
  confirmInvestAmount?: number;
  /**
   * 试算明细
   */
  detailList?: PaymentConfirmCalculationDetailBO[];
  /**
   * 最后还款日
   */
  expectedDueDate?: Date;
  /**
   * 预估计息天数（天）
   */
  expectedInterestDays?: number;
  /**
   * 起息日
   */
  expectedLaunchDate?: Date;
  /**
   * 预估还款期数
   */
  expectedRepayPeriods?: number;
  /**
   * 宽限期天数（天）
   */
  gracePeriodDays?: number;
  /**
   * 宽限期费率（%/年）
   */
  gracePeriodRate?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 分期还息频次
   */
  interestPeriod?: string;
  /**
   * 还息方式
   */
  interestRepaymentMethod?: string;
  /**
   * 合同利率（%/年）
   */
  nominalInterestRate?: number;
  /**
   * 付款记录ID
   */
  paymentConfirmId?: number;
  /**
   * 固定罚息利率（%）
   */
  penaltyInterestRate?: number;
  /**
   * 阶梯罚息json
   */
  penaltySteps?: string;
  /**
   * 罚息类型
   */
  penaltyType?: string;
  /**
   * 还本付息计划规划方式
   */
  planningMethod?: string;
  /**
   * 用信定价综合收益率（%）
   */
  pricingXirrRate?: number;
  /**
   * 分期还本频次
   */
  principalPeriod?: string;
  /**
   * 还本方式
   */
  principalRepaymentMethod?: string;
  /**
   * 默认当期还息日
   */
  repayInterestDay?: string;
  /**
   * 默认当期还本日
   */
  repayPrincipalDay?: string;
  /**
   * 服务费（元）
   */
  serviceFeeAmount?: number;
  /**
   * 测算综合收益率（%/年）
   */
  xirrRate?: number;
  [property: string]: any;
}

/**
 * PaymentConfirmCalculationDetailBO，付款记录试算明细
 */
export interface PaymentConfirmCalculationDetailBO {
  /**
   * 当期还本/付息日
   */
  currentOperationDate?: Date;
  /**
   * 应还宽限期利息(元)
   */
  graceInterestAmount?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应还利息(元)
   */
  interestAmount?: number;
  /**
   * 应还逾期罚息(元)
   */
  overdueInterestAmount?: number;
  /**
   * 试算ID
   */
  paymentConfirmCalculationId?: number;
  /**
   * 应还本金(元)
   */
  principalAmount?: number;
  /**
   * 还款编号
   */
  repayCode?: string;
  /**
   * 还款项
   */
  repaymentItem?: string;
  /**
   * 还款期数
   */
  repayPeriods?: number;
  /**
   * 应收服务费(元)
   */
  serviceAmount?: number;
  /**
   * 当期净现金流(元)
   */
  totalAmount?: number;
  [property: string]: any;
}

// 获取分页列表
export async function getPaymentRecordPageListApi(params: PageListParams) {
  return requestClient.get<PaymentRecordInfo[]>('/factoring/payment/confirm/page', { params });
}

// 添加
export async function addPaymentRecordApi(data: PaymentRecordInfo) {
  return requestClient.post<PaymentRecordInfo>('/factoring/payment/confirm/add', data);
}

// 获取详情
export async function getPaymentRecordInfoApi(id: number) {
  return requestClient.get(`/factoring/payment/confirm/detail/${id}`);
}

export async function getCompanyDetailByCodeApi(params: PageListParams) {
  return requestClient.get('/base/company/detail-by-code', { params });
}

// 试算
export async function calculationPaymentConfirm(data: PaymentRecordInfo) {
  return requestClient.post<PaymentRecordInfo>('/factoring/payment/confirm/start', data);
}

// 仅更新利息
export async function calculationPaymentConfirmInterest(data: PaymentRecordInfo) {
  return requestClient.post<PaymentRecordInfo>('/factoring/payment/confirm/start/interest', data);
}

// 试算明细导出
export async function calculationPaymentConfirmApi(id: number) {
  return requestClient.downloadAndSave(`/factoring/payment/confirm/export?id=${id}`);
}

// 获取列表
export async function getPaymentConfirmListApi(params: PageListParams) {
  return requestClient.get<PaymentRecordInfo[]>('/factoring/payment/confirm/list', { params });
}
