import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface SettlementInfo {
  /**
   * 已还宽限息（元）
   */
  actualGraceInterestAmount?: number;
  /**
   * 已还利息（元）
   */
  actualInterestAmount?: number;
  /**
   * 已还罚息（元）
   */
  actualOverdueInterestAmount?: number;
  /**
   * 已还本金（元）
   */
  actualPrincipalAmount?: number;
  /**
   * 已还服务费（元）
   */
  actualServiceAmount?: number;
  /**
   * 已还总额（元）
   */
  actualTotalAmount?: number;
  /**
   * 附件列表
   */
  attachmentList?: number[];
  /**
   * 用信申请ID
   */
  creditApplyId?: number;
  /**
   * 用信申请名称
   */
  creditApplyName?: string;
  /**
   * 最后付息日
   */
  dueDate?: Date;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应还利息（元）
   */
  interestAmount?: number;
  /**
   * 起息日
   */
  launchDate?: Date;
  /**
   * 是否逾期
   */
  overdueStatus?: string;
  /**
   * 付款申请编号
   */
  paymentApplyCode?: string;
  /**
   * 付款申请ID
   */
  paymentApplyId?: number;
  /**
   * 付款记录ID
   */
  paymentConfirmId?: number;
  /**
   * 应还本金（元）
   */
  principalAmount?: number;
  /**
   * 项目编号
   */
  projectCode?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 还款计划ID
   */
  SettlementId?: number;
  /**
   * 应还服务费（元）
   */
  serviceAmount?: number;
  /**
   * 结清状态
   */
  settlementStatus?: string;
  /**
   * 应还总额（元）
   */
  totalAmount?: number;
  [property: string]: any;
}

// 获取分页列表
export async function getSettlementListApi(params: PageListParams) {
  return requestClient.get<SettlementInfo[]>('/factoring/settlement/page', { params });
}

// 获取详情
export async function getSettlementInfoApi(id: number) {
  return requestClient.get(`/factoring/settlement/report/detail/${id}`);
}

// 上传结清证明
export async function settlementReportUpload(data: SettlementInfo) {
  return requestClient.post<SettlementInfo>('/factoring/settlement/report/upload', data);
}
// 生成结清证明
export async function getSettlementGenerateApi(id: number) {
  return requestClient.downloadAndSave(`/factoring/settlement/generate?id=${id}`);
}
// 导出
export async function exportSettlementApi(data: SettlementInfo) {
  return requestClient.downloadAndSave('/factoring/settlement/export', {
    config: { params: data },
  });
}
