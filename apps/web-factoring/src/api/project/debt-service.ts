import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface DebtServiceInfo {
  /**
   * 试算名称
   */
  calculationName?: string;
  /**
   * 测算综合收益率（%/年）
   */
  compositeYieldRate?: number;
  /**
   * 综合收益率（%）
   */
  comprehensiveRate?: number;
  /**
   * 试算明细
   */
  detailList?: ProjectCalculationDetailBO[];
  /**
   * 预估最后还款日
   */
  expectedDueDate?: Date;
  /**
   * 预估计息天数（天）
   */
  expectedInterestDays?: number;
  /**
   * 预计业务投放日
   */
  expectedLaunchDate?: Date;
  /**
   * 预估还款期数
   */
  expectedRepayPeriods?: number;
  /**
   * 计划融资金额（元）
   */
  financingAmount?: number;
  /**
   * 融资比例（%）
   */
  financingRatio?: number;
  /**
   * 宽限期天数（天）
   */
  gracePeriodDays?: number;
  /**
   * 宽限期费率（%/年）
   */
  gracePeriodRate?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 分期还息频次
   */
  interestPeriod?: string;
  /**
   * 还息方式
   */
  interestRepaymentMethod?: string;
  /**
   * 是否审批
   */
  isReview?: number;
  /**
   * 提交操作
   */
  isSubmit?: boolean;
  /**
   * 固定罚息利率（%）
   */
  penaltyInterestRate?: number;
  /**
   * 阶梯罚息JSON：startDays , endDays , rate
   */
  penaltySteps?: string;
  /**
   * 罚息类型
   */
  penaltyType?: string;
  /**
   * 还本付息计划规划方式
   */
  planningMethod?: string;
  /**
   * 基准定价（%/年）
   */
  pricingBasicRatio?: number;
  /**
   * 浮动定价（%/年）
   */
  pricingFloatingRatio?: number;
  /**
   * 分期还本频次
   */
  principalPeriod?: string;
  /**
   * 还本方式
   */
  principalRepaymentMethod?: string;
  /**
   * 项目编码
   */
  projectCode?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 应收账款金额（元）
   */
  receivableAmount?: number;
  /**
   * 备注说明
   */
  remarks?: string;
  /**
   * 默认当期还息日
   */
  repayInterestDay?: string;
  /**
   * 默认当期还本日
   */
  repayPrincipalDay?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 服务费（元）
   */
  serviceFeeAmount?: number;
  /**
   * 操作状态
   */
  status?: string;
  /**
   * 合作企业Code
   */
  targetCompanyCode?: number;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  [property: string]: any;
}

/**
 * ProjectCalculationDetailBO，项目还本付息试算明细
 */
export interface ProjectCalculationDetailBO {
  /**
   * 业务类型
   */
  businessType?: string;
  /**
   * 当期还本/付息日
   */
  currentDate?: Date;
  /**
   * 应还宽限期利息(元)
   */
  graceInterestAmount?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应还利息(元)
   */
  interestAmount?: number;
  /**
   * 应还逾期罚息(元)
   */
  overdueInterestAmount?: number;
  /**
   * 应还本金(元)
   */
  principalAmount?: number;
  /**
   * 试算ID
   */
  projectCalculationId?: number;
  /**
   * 还款期数
   */
  repayPeriods?: number;
  /**
   * 应收服务费(元)
   */
  serviceAmount?: number;
  /**
   * 当期净现金流(元)
   */
  totalAmount?: number;
  [property: string]: any;
}

// 获取还本付息计划分页列表
export async function getDebtServicePageListApi(params: PageListParams) {
  return requestClient.get<DebtServiceInfo[]>('/factoring/project/calculation/page', { params });
}

// 添加还本付息计划
export async function addDebtServiceApi(data: DebtServiceInfo) {
  return requestClient.post<DebtServiceInfo>('/factoring/project/calculation/add', data);
}

// 编辑还本付息计划
export async function editDebtServiceApi(data: DebtServiceInfo) {
  return requestClient.post<DebtServiceInfo>('/factoring/project/calculation/edit', data);
}

// 获取还本付息计划详情
export async function getDebtServiceInfoApi(id: number) {
  return requestClient.get(`/factoring/project/calculation/detail/${id}`);
}

// 删除还本付息计划
export async function delDebtServiceApi(id: number) {
  return requestClient.post(`/factoring/project/calculation/delete/${id}`);
}

// 试算
export async function calculationProject(data: DebtServiceInfo) {
  return requestClient.post<DebtServiceInfo>('/factoring/project/calculation/start', data);
}

// 测算综合收益率
export async function calculationRateProject(data: ProjectCalculationDetailBO) {
  return requestClient.post('/factoring/project/xirr/rate', data);
}
