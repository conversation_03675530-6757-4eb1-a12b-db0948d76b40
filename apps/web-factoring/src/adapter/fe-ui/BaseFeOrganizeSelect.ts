import { defineComponent } from 'vue';

import { FeOrganizeSelect } from '@vben/fe-ui';

import { getOrgListApi, getOrgListPyIdsApi } from '#/api';

export const BaseFeOrganizeSelect = defineComponent({
  ...FeOrganizeSelect,
  props: {
    ...(FeOrganizeSelect as any).props,
    api: {
      type: Object,
      ...(FeOrganizeSelect as any).props.api,
      required: false,
      default() {
        return {
          getOrgListApi,
          getOrgListPyIdsApi,
        };
      },
    },
  },
});
