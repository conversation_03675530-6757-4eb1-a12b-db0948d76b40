import { defineComponent } from 'vue';

import { FilePickerList } from '@vben/base-ui';

import { getDownloadFileLinkApi, getFileInfoListApi, getPreviewFileExternalLink, uploadFileApi } from '#/api';
import * as cloudDiskApi from '#/api/core/cloud-disk';

export const BaseFilePickList = defineComponent({
  ...FilePickerList,
  props: {
    ...(FilePickerList as any).props,
    fileInfoApi: {
      type: Function,
      ...(FilePickerList as any).props.fileInfoApi,
      default: getFileInfoListApi,
    },
    previewExternalApi: {
      type: Function,
      ...(FilePickerList as any).props.previewExternalApi,
      default: getPreviewFileExternalLink,
    },
    cloudDiskApiGroup: {
      type: Object,
      ...(FilePickerList as any).props.cloudDiskApiGroup,
      default: cloudDiskApi,
    },
    downloadApi: {
      type: Function,
      ...(FilePickerList as any).props.downloadApi,
      default: getDownloadFileLinkApi,
    },
    uploadApi: {
      type: Function,
      ...(FilePickerList as any).props.uploadApi,
      default: uploadFileApi,
    },
  },
});
