<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { isEmpty } from 'lodash-es';

import { getStatisticsAssetClassificationApi } from '#/api';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const getStatistics = async () => {
  const res = await getStatisticsAssetClassificationApi();
  if (!isEmpty(res)) {
    res.forEach((item) => {
      item.value = item.quantity;
      item.name = item.assetClassification;
    });
  }
  return res;
};

onMounted(async () => {
  const infoDetail = await getStatistics();
  renderEcharts({
    radar: {
      indicator: infoDetail,
      radius: '70%',
      splitNumber: 8,
      // 调整网格线颜色适应白色背景
      splitLine: {
        lineStyle: {
          color: 'rgba(220, 231, 243, 0.8)', // 浅蓝灰色网格线
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(152, 193, 217, 0.6)', // 浅蓝色轴线
        },
      },
      name: {
        textStyle: {
          color: '#4A6FA5', // 指标名称颜色（深蓝灰色）
        },
      },
    },
    series: [
      {
        areaStyle: {
          opacity: 0.3, // 透明度设置（0.3-0.5之间效果最佳）
          shadowBlur: 5,
          shadowColor: 'rgba(100, 181, 246, 0.4)', // 浅蓝色阴影
          shadowOffsetX: 2,
          shadowOffsetY: 2,
        },
        data: [
          {
            itemStyle: {
              color: '#64B5F6', // 基础浅蓝色
              borderColor: '#64B5F6', // 边框同色
              borderWidth: 2,
            },
            name: '风险分类',
            value: infoDetail.map((item) => item.value),
          },
        ],
        lineStyle: {
          color: '#64B5F6', // 线条颜色与填充色一致
          width: 2,
        },
        symbolSize: 6, // 适当显示数据点（原0隐藏）
        symbol: 'circle',
        type: 'radar',
      },
    ],
    tooltip: {
      confine: true,
    },
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" />
</template>
