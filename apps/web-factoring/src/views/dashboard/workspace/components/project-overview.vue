<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import dayjs from 'dayjs';
import { isEmpty } from 'lodash-es';

import { getStatisticsProjectInfoApi } from '#/api';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const info = ref<Object>({
  months: [],
  addProjectQuantity: [],
  settleProjectQuantity: [],
  addInvestedAmount: [],
  settleInvestedAmount: [],
});
const year = ref(dayjs().format('YYYY'));
const detail = ref<Object>({});
const getStatistics = async () => {
  const res = await getStatisticsProjectInfoApi({ year: year.value });
  detail.value = res;
  if (!isEmpty(res.monthList)) {
    res.monthList.forEach((item) => {
      info.value.months.push(`${item.month}月`);
      info.value.addProjectQuantity.push(item.addProjectQuantity);
      info.value.settleProjectQuantity.push(item.settleProjectQuantity);
      info.value.addInvestedAmount.push(Number(item.addInvestedAmount));
      info.value.settleInvestedAmount.push(Number(item.settleInvestedAmount));
    });
  }
  renderEcharts({
    grid: {
      top: '100',
      bottom: '3%',
      containLabel: true,
    },
    tooltip: {
      confine: true,
      trigger: 'axis',
    },
    legend: {
      top: '10',
      data: ['本月新增项目数量', '本月新增项目投放金额', '本月结清项目数量', '结清项目累计收回金额'],
    },
    xAxis: [
      {
        type: 'category',
        data: info.value.months,
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
      },
      {
        type: 'value',
        name: '金额',
      },
    ],
    series: [
      {
        name: '本月新增项目数量',
        type: 'bar',
        itemStyle: { color: '#66B3FF' }, // 浅蓝色（匹配需求色）
        tooltip: {
          formatter: '{seriesName}：{value} 个',
        },
        data: info.value.addProjectQuantity,
      },
      {
        name: '本月新增项目投放金额',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#FF4D4F' }, // 红色（匹配需求色）
        tooltip: {
          formatter: '{seriesName}：{value} 万元',
        },
        data: info.value.settleProjectQuantity,
      },
      {
        name: '本月结清项目数量',
        type: 'bar',
        itemStyle: { color: '#36CFC9' },
        tooltip: {
          formatter: '{seriesName}：{value} 个',
        },
        data: info.value.addInvestedAmount,
      },
      {
        name: '结清项目累计收回金额',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#FAAD14' },
        tooltip: {
          formatter: '{seriesName}：{value} 万元',
        },
        data: info.value.settleInvestedAmount,
      },
    ],
  });
};

onMounted(() => {
  getStatistics();
});
</script>

<template>
  <div class="box-title">项目数据</div>
  <div class="box-date">
    <a-date-picker
      v-model:value="year"
      value-format="YYYY"
      picker="year"
      :allow-clear="false"
      @change="getStatistics"
    />
  </div>
  <div class="box-content-project">
    <EchartsUI ref="chartRef" />
  </div>
  <a-row class="box-project-overview">
    <a-col :span="12" class="project-overview-data"> 新增项目数量: {{ detail.yearProjectQuantity }} </a-col>
    <a-col :span="12" class="project-overview-data"> 结清项目数量: {{ detail.yearSettleProjectQuantity }} </a-col>
    <a-col :span="12" class="project-overview-data"> 新增投放金额: {{ detail.yearInvestedAmount }} </a-col>
    <a-col :span="12" class="project-overview-data"> 结清项目金额: {{ detail.yearSettleInvestedAmount }} </a-col>
  </a-row>
</template>
