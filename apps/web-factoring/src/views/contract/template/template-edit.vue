<script setup lang="ts">
import type { ContractTemplateInfo } from '#/api';

import { ref } from 'vue';

import { OnlyOffice } from '@vben/base-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';

import { getOnlyOfficeFileInfoApi, saveOnlyOfficeFileApi } from '#/api';

const emit = defineEmits(['ok', 'register']);
const OnlyOfficeRef = ref();
const init = async (data: ContractTemplateInfo) => {
  if (data.fileId) {
    await OnlyOfficeRef.value.init(data.fileId, 'edit');
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const save = async () => {
  changeOkLoading(true);
  try {
    await OnlyOfficeRef.value.save();
    emit('ok');
    message.success('保存成功');
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="模板信息" height="100%" @register="registerPopup" @ok="save">
    <div class="h-full px-2">
      <OnlyOffice ref="OnlyOfficeRef" :get-config-api="getOnlyOfficeFileInfoApi" :save-api="saveOnlyOfficeFileApi" />
    </div>
  </BasicPopup>
</template>

<style></style>
