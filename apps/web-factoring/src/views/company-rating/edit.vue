<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CompanyRatingVO } from '#/api';

import { reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { Button, Col, Form, FormItem, Input, Row, Select } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { spanMethod } from '../limit-admin/components/tableSpanUtils';

const emit = defineEmits(['register', 'ok']);
const dictStore = useDictStore();
const baseFormInfo = reactive<CompanyRatingVO>({
  companyCode: '',
  companyLogList: [],
  companyName: '',
  companyOrgType: '',
  id: undefined,
  lastLogId: undefined,
  oldCompanyRating: '',
  companyScoreList: [],
});

const ratingTable = ref([
  {
    firstCategory: '客户属性',
    secondCategory: '股东属性',
    dictType: 'COMPANY_RATING_SHAREHOLDER',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户属性',
    secondCategory: '行业属性',
    dictType: 'COMPANY_RATING_INDUSTRY',
    value: '',
    remark: '',
  },
  {
    firstCategory: '价值贡献',
    secondCategory: '业务覆盖',
    dictType: 'COMPANY_RATING_BUSINESS_COVERAGE',
    value: '',
    remark: '不同类型产品或服务，如供应链与保理业务。',
  },
  {
    firstCategory: '价值贡献',
    secondCategory: '利润贡献',
    dictType: 'COMPANY_RATING_PROFIT_CONTRIBUTION',
    value: '',
    remark: '以财务部会计毛利润为主。',
  },
  {
    firstCategory: '价值贡献',
    secondCategory: '资源推荐',
    dictType: 'COMPANY_RATING_RESOURCE',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户经营',
    secondCategory: '收入增长',
    dictType: 'COMPANY_RATING_REVENUE_GROWTH',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户经营',
    secondCategory: '盈利能力',
    dictType: 'COMPANY_RATING_PROFIT_ORIENTED',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户经营',
    secondCategory: '信用情况',
    dictType: 'COMPANY_RATING_CREDIT_SITUATION',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户经营',
    secondCategory: '纳税情况',
    dictType: 'COMPANY_RATING_TAX_SITUATION',
    value: '',
    remark: '',
  },
  {
    firstCategory: '合作基础',
    secondCategory: '合作年限',
    dictType: 'COMPANY_RATING_DURATION',
    value: '',
    remark: '客户自上而下主动配合。',
  },
  {
    firstCategory: '合作基础',
    secondCategory: '合作配合度',
    dictType: 'COMPANY_RATING_COOPERATION',
    value: '',
    remark: '',
  },
]);

// const processedData = computed(() => {
//   const result = [];
//   let currentFirstCategory = '';
//   let currentRowIndex = -1;

//   ratingTable.value.forEach((item, index) => {
//     if (item.firstCategory === currentFirstCategory) {
//       // 增加前一个相同一级维度的行跨度
//       result[currentRowIndex]._rowspan++;
//       // 当前行不显示一级维度
//       result.push({
//         ...item,
//         firstCategory: '',
//         _rowspan: 0,
//       });
//     } else {
//       currentFirstCategory = item.firstCategory;
//       currentRowIndex = index;
//       result.push({
//         ...item,
//         _rowspan: 1,
//       });
//     }
//   });

//   return result;
// });

const colSpan = { md: 12, sm: 24 };

const formRef = ref();

/**
 * 表格合并后数据源会改变，
 * 移除合并的一级维度，
 * 此方法处理补全被移除的数据
 */
// const dataHandling = (data: CompanyRatingScoreVO[]) => {
//   let currentFirstCategory = data[0]?.firstCategory;

//   return data.map((item) => {
//     if (!item.firstCategory || item.firstCategory === '') {
//       return { ...item, firstCategory: currentFirstCategory };
//     } else {
//       currentFirstCategory = item.firstCategory;
//       return item;
//     }
//   });
// };

const handleSave = async () => {
  try {
    await formRef.value.validate();
    const params = cloneDeep(baseFormInfo);
    // 确保获取表格最新数据
    if (gridApi.grid) {
      const TableData = gridApi.grid.getTableData();
      params.companyScoreList = TableData.fullData;
    }
    emit('ok', params);
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    changeOkLoading(false);
  }
};

const submit = async () => {
  handleSave();
};

// 添加初始化方法
const init = (data: any) => {
  if (data) {
    if (data.companyScoreList.length > 0) {
      ratingTable.value = ratingTable.value.map((value, index) => {
        return {
          ...value,
          value: data.companyScoreList[index].value,
          remark: data.companyScoreList[index].remark,
        };
      });
      gridApi.grid?.reloadData(ratingTable.value);
    }
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));
  }
};

const gridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'firstCategory',
      title: '一级维度',
      width: 160,
    },
    {
      field: 'secondCategory',
      title: '二级维度',
      width: 160,
    },
    {
      field: 'contractType',
      title: '评级赋分',
      slots: { default: 'contractType-select' },
      width: '35%',
    },
    {
      field: 'remark',
      title: '备注',
      slots: { default: 'remark' },
    },
  ],
  data: ratingTable.value,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  spanMethod: (params: any) => spanMethod(params, 'firstCategory'),
};

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const labelCol = { style: { width: '150px' } };

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
</script>

<template>
  <BasicPopup v-bind="$attrs" title="重新评级" @register="registerPopup">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="baseFormInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="企业名称" name="companyName">
            <Input disabled v-model:value="baseFormInfo.companyName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="统一社会信用代码" name="companyCode">
            <Input disabled v-model:value="baseFormInfo.companyCode" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="客户分级赋分" />
      <Grid>
        <!-- 评级赋分下拉框 -->
        <template #contractType-select="{ row }">
          <Select
            style="width: 100%"
            v-model:value="row.value"
            :options="dictStore.getDictList(row.dictType)"
            :placeholder="`请选择${row.secondCategory}`"
          />
        </template>

        <!-- 备注 -->
        <template #remark="{ row }">
          <div v-if="row.secondCategory === '资源推荐'">
            <typography-text>1.项目落地，且推荐需留痕，需客户管理部认定。</typography-text><br />
            <typography-text>2. 具体如码头仓储合作、供应集采资源、数据金融模型、运营科技赋能等</typography-text>
          </div>
          <div v-if="row.secondCategory === '收入增长' || row.secondCategory === '盈利能力'">
            <typography-text>1. 项目关联集团最近连续2年收入增速，GDP增速参考省GDP增速。</typography-text><br />
            <typography-text>2. 项目关联集团最近连续2年收入增速，GDP增速参考省GDP增速。</typography-text>
          </div>
          <div>
            <typography-text>{{ row.remark }}</typography-text>
          </div>
        </template>
      </Grid>
    </Form>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line; /* 或 pre 或 pre-wrap */
}
</style>
