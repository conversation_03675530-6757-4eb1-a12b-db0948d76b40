<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { maintenanceVo, rulesVo, warningRulesPageVO } from '#/api';

import { reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';

import { Button, Form, FormItem, InputNumber, message, Select, Switch, TypographyText } from 'ant-design-vue';

import { BaseFeUserSelect } from '#/adapter/fe-ui';
import {
  editMaintenanceRulesApi,
  editWarningRulesApi,
  enableOrDisableWarningRulesApi,
  getMaintenanceRulesApi,
  getUserListApi,
  getWarningRulesDetailApi,
  getWarningRulesListApi,
} from '#/api';

import RuleCard from './components/RuleCard.vue';

const dictStore = useDictStore();
const regulatoryList = ref<warningRulesPageVO[]>([]);
const complianceList = ref<warningRulesPageVO[]>([]);
const rulesFormRef = ref();
const maintenanceFormRef = ref();
const labelCol = { style: { width: '150px' } };

const rulesForm = reactive<rulesVo>({
  id: undefined,
  isSms: undefined,
  notifyRole: undefined,
  smsUserIdList: [],
  smsUserNameList: [],
  value: undefined,
  warningLevel: '',
  status: undefined,
  conditionDesc: '',
});
const maintenanceForm = reactive<maintenanceVo>({
  id: undefined,
  riskAsset: undefined,
  netAsset: undefined,
});

const rules: Record<string, Rule[]> = {
  value: [{ required: true, message: '请输入预警规则' }],
  warningLevel: [{ required: true, message: '请选择预警等级', trigger: 'change' }],
};

const maintenanceRules: Record<string, Rule[]> = {
  riskAsset: [{ required: true, message: '请输入风险资产' }],
  netAsset: [{ required: true, message: '请输入净资产' }],
};

const init = async () => {
  try {
    const res = await getWarningRulesListApi();
    regulatoryList.value = res.slice(0, 3);
    complianceList.value = res.slice(3);
  } catch {}
};
init();
const userOptions = ref<{ userId: number; userName: string }[]>([]);

const loadUserOptions = async () => {
  userOptions.value = await getUserListApi();
};
// const filterOption = (input: string, option: any) => {
//   return option.realName.toLowerCase().includes(input.toLowerCase());
// };
loadUserOptions();

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await rulesFormRef.value.validate();
      rulesForm.notifyRole = rulesForm.notifyRole.join(',');
      await editWarningRulesApi(rulesForm);
      message.success('调整成功');
      await modalApi.close();
      await init();
    } catch {}
  },
});

const [MaintenanceModal, maintenanceModalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await maintenanceFormRef.value.validate();
      await editMaintenanceRulesApi(maintenanceForm);
      await maintenanceModalApi.close();
    } catch {}
  },
});

const handleChange = async (id: number) => {
  try {
    await enableOrDisableWarningRulesApi(id);
  } catch {}
};

const rulesChange = async (item) => {
  await getWarningRulesDetailApi(item.id).then((res) => {
    Object.assign(rulesForm, { ...res, conditionDesc: item.ruleDesc });
  });
  rulesForm.notifyRole = rulesForm.notifyRole?.split(',') || [];
  modalApi.open();
};

const maintenanceClick = async () => {
  try {
    const res = await getMaintenanceRulesApi();
    Object.assign(maintenanceForm, res);
  } catch {}
  maintenanceModalApi.open();
};
</script>

<template>
  <Page auto-content-height>
    <div class="bg-card rounded-md p-4">
      <BasicCaption content="监管指标预警">
        <template #action>
          <Button class="mr-2" type="primary" @click="maintenanceClick">指标值维护</Button>
        </template>
      </BasicCaption>
      <div style="padding: 20px">
        <a-row :gutter="16" style="display: flex; flex-wrap: wrap">
          <RuleCard v-for="item in regulatoryList" :key="item.id" :item="item" @edit="rulesChange(item)">
            <template #extra="{ item }">
              <Switch
                v-model:checked="item.status"
                checked-value="1"
                un-checked-value="0"
                @change="handleChange(item.id)"
              />
            </template>
          </RuleCard>
        </a-row>
      </div>
      <BasicCaption content="合规操作预警" />
      <div style="padding: 20px">
        <a-row :gutter="16" style="display: flex; flex-wrap: wrap">
          <RuleCard v-for="item in complianceList" :key="item.id" :item="item" mb @edit="rulesChange">
            <template #extra="{ item }">
              <Switch
                v-model:checked="item.status"
                checked-value="1"
                un-checked-value="0"
                @change="handleChange(item.id)"
              />
            </template>
          </RuleCard>
        </a-row>
      </div>
    </div>
    <Modal title="规则调整" class="w-[600px]">
      <Form ref="rulesFormRef" :model="rulesForm" :label-col="labelCol" :rules="rules" :wrapper-col="{ span: 20 }">
        <FormItem label="预警规则">
          <TypographyText>
            {{ rulesForm.conditionDesc }}
          </TypographyText>
        </FormItem>
        <FormItem v-if="!['项目额度预警', '项目逾期催收'].includes(rulesForm.ruleName)" label="触发条件值" name="value">
          <InputNumber class="w-full" v-model:value="rulesForm.value" :controls="false" />
        </FormItem>
        <FormItem label="预警等级" name="warningLevel">
          <Select v-model:value="rulesForm.warningLevel" :options="dictStore.getDictList('FCT_WARNING_RULE_LEVEL')" />
        </FormItem>
        <FormItem label="预警角色" name="notifyRole">
          <Select
            v-model:value="rulesForm.notifyRole"
            allow-clear
            mode="multiple"
            :options="dictStore.getDictList('INFORM_STAFF_TYPE')"
          />
        </FormItem>
        <FormItem label="预警接收人">
          <BaseFeUserSelect v-model:value="rulesForm.smsUserIdList" multiple />
        </FormItem>
        <FormItem label="短信提醒">
          <Switch v-model:checked="rulesForm.isSms" :checked-value="1" :un-checked-value="0" />
        </FormItem>
      </Form>
    </Modal>
    <MaintenanceModal title="指标值维护" class="w-[800px]">
      <Form
        ref="maintenanceFormRef"
        :model="maintenanceForm"
        :label-col="labelCol"
        :rules="maintenanceRules"
        :wrapper-col="{ span: 20 }"
      >
        <FormItem label="风险资产（元）" name="riskAsset">
          <InputNumber style="width: 100%" v-model:value="maintenanceForm.riskAsset" />
        </FormItem>
        <FormItem label="净资产（元）" name="netAsset">
          <InputNumber style="width: 100%" v-model:value="maintenanceForm.netAsset" />
        </FormItem>
      </Form>
    </MaintenanceModal>
  </Page>
</template>

<style scoped></style>
