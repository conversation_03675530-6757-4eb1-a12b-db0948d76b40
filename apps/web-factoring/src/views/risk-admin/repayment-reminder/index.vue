<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { RepaymentReminderInfo } from '#/api';

import { computed, ref } from 'vue';

import { FilePreviewDialog } from '@vben/base-ui';
import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions, formatMoney } from '@vben/utils';

import { message, Table } from 'ant-design-vue';
import dayjs from 'dayjs'; // 新增日期处理
import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getDownloadFileLinkApi,
  getPreviewFileExternalLink,
  getRepaymentReminderGenerateApi,
  getRepaymentReminderListApi,
} from '#/api';

// 表单配置
const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'projectName', label: '项目名称' },
    { component: 'Input', fieldName: 'repaymentPlanCode', label: '还款计划编号' },
    { component: 'Input', fieldName: 'companyName', label: '还款单位' },
    { component: 'Input', fieldName: 'contractName', label: '合同名称' },
    { component: 'Input', fieldName: 'contractCode', label: '合同号' },
    { component: 'RangePicker', fieldName: 'dueDate', label: '待支付日期' },
  ],
  fieldMappingTime: [['dueDate', ['beginDueDate', 'endDueDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
  commonConfig: { labelCol: { span: 8 }, wrapperCol: { span: 16 } },
});

// 表格配置
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    { field: 'repaymentPlanCode', title: '还款计划编号', minWidth: 180 },
    { field: 'dueDate', title: '待支付日期', minWidth: 120, formatter: 'formatDate' },
    { field: 'companyName', title: '还款单位', minWidth: 180 },
    { field: 'bankBranch', title: '还款单位开户行', minWidth: 200 },
    { field: 'bankAccount', title: '还款单位银行账号', minWidth: 240 },
    { field: 'contractName', title: '合同名称', minWidth: 200 },
    { field: 'contractCode', title: '合同号', minWidth: 160 },
    { field: 'periods', title: '第N期', minWidth: 80 },
    { field: 'principalAmount', title: '待还本金（元）', minWidth: 120, formatter: 'formatMoney' },
    { field: 'interestAmount', title: '待还利息（元）', minWidth: 120, formatter: 'formatMoney' },
    { field: 'totalAmount', title: '本息合计（元）', minWidth: 120, formatter: 'formatMoney' },
    { field: 'dueDays', title: '到期天数', minWidth: 80 },
    {
      field: 'notifyStatus',
      title: '发送状态',
      minWidth: 120,
      cellRender: { name: 'CellStatus', props: { code: 'FCT_REPAYMENT_NOTIFY_STATUS' } },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) =>
        await getRepaymentReminderListApi({ current: page.currentPage, size: page.pageSize, ...formValues }),
    },
  },
  toolbarConfig: { slots: { tools: 'toolbar-tools' } },
};

const [Grid, GridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 通知函弹窗配置
const [SettlementModal, settlementModalApi] = useVbenModal({
  title: '发送通知函', // 弹窗标题
  confirmText: '发送通知函', // 确认按钮文本
  onConfirm: async () => {
    await getRepaymentReminderGenerateApi(settlementReport.value.id as number);
    message.success($t('base.resSuccess'));
    await settlementModalApi.close();
    await GridApi.reload();
  },
  onClosed: () => {
    settlementReport.value = {};
  },
});

const settlementReport = ref<RepaymentReminderInfo>({});

// 通知函动态数据
const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'));

// 本息明细表格配置
const reportColumns = [
  { title: '期数', dataIndex: 'periods', width: 80 },
  { title: '本息支付日期', dataIndex: 'dueDate', width: 150 },
  { title: '本金（元）', dataIndex: 'principalAmount', width: 120 },
  { title: '利息（元）', dataIndex: 'interestAmount', width: 120 },
  { title: '本息合计（元）', dataIndex: 'totalAmount', width: 120 },
];

const detailDataSource = computed(() => {
  if (!settlementReport.value) return [];
  return [
    {
      periods: `第${settlementReport.value.periods}期`,
      dueDate: dayjs(settlementReport.value.dueDate).format('YYYY-MM-DD'),
      principalAmount: formatMoney(settlementReport.value.principalAmount),
      interestAmount: formatMoney(settlementReport.value.interestAmount),
      totalAmount: formatMoney(settlementReport.value.totalAmount),
    },
  ];
});

// 打开通知函弹窗
const generateSettlement = async (row: any) => {
  settlementReport.value = cloneDeep(row);
  await settlementModalApi.open();
};
const FilePreviewDialogRef = ref();
// 下载通知函
const settlementDownload = (row: any) => {
  if (!row.notifyFileId) return;
  FilePreviewDialogRef.value.init(row.notifyFileId);
};
</script>

<template>
  <Page auto-content-height>
    <!-- 主表格 -->
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['to_be_sent'].includes(row.notifyStatus)" @click="generateSettlement(row)">
            发送通知函
          </a-typography-link>
          <a-typography-link v-else @click="settlementDownload(row)"> 查看通知函 </a-typography-link>
        </a-space>
      </template>
    </Grid>

    <!-- 通知函弹窗 -->
    <SettlementModal class="w-[1100px]">
      <div class="lg:prose-xl mx-auto p-6 text-gray-800">
        <!-- 标题 -->
        <div class="mb-6 text-center text-2xl font-bold">还本付息通知函</div>

        <!-- 致语 -->
        <div class="mb-4">致：{{ settlementReport.companyName }}</div>

        <!-- 合同说明 -->
        <div class="mb-6">
          根据《{{ settlementReport.contractName }}》（合同编号：{{ settlementReport.contractCode }}）约定，第【{{
            settlementReport.periods
          }}】期保理融资本息即将到期应付，具体金额见下表：
        </div>

        <!-- 本息明细 -->
        <div class="mb-6">
          <div class="mb-2 text-center font-semibold">第【{{ settlementReport.periods }}】期本息支付明细表</div>
          <Table :columns="reportColumns" :data-source="detailDataSource" bordered :pagination="false" />
        </div>

        <!-- 收款账户 -->
        <div class="mb-6">
          烦请贵司按照上述金额按时支付至我司如下账户：
          <a-descriptions
            bordered
            class="mt-2"
            :label-style="{
              fontWeight: 'bold',
              width: '160px',
              textAlign: 'right',
            }"
            :content-style="{
              width: 'calc(100% - 160px)',
            }"
          >
            <a-descriptions-item label="开户行" :span="3">{{ settlementReport.ourBankBranch }}</a-descriptions-item>
            <a-descriptions-item label="户名" :span="3">江西省财投商业保理有限公司</a-descriptions-item>
            <a-descriptions-item label="银行账号" :span="3">{{ settlementReport.ourBankAccount }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 提醒说明 -->
        <div class="mb-6">
          提醒贵司注意：如贵司推延支付，则我司有权根据《{{ settlementReport.contractName }}》规定收取迟延违约金。
        </div>

        <!-- 盖章区域 -->
        <div class="text-right">
          <div>江西省财投商业保理有限公司</div>
          <div>{{ currentDate }}</div>
        </div>
      </div>
    </SettlementModal>
    <FilePreviewDialog
      ref="FilePreviewDialogRef"
      :preview-api="getPreviewFileExternalLink"
      :download-api="getDownloadFileLinkApi"
    />
  </Page>
</template>

<style scoped></style>
