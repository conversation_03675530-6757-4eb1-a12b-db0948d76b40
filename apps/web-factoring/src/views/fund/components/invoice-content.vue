<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { watch } from 'vue';

import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

const props = defineProps({ invoiceForm: { type: Object, required: true } });

const GridOptions = {
  columns: [
    {
      field: 'invoiceItem',
      title: '服务名称',
      minWidth: '180px',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_INVOICE_ITME' } },
    },
    { field: 'quantity', title: '数量' },
    { field: 'amount', title: '不含税金额（元）', formatter: 'formatMoney' },
    { field: 'tax', title: '税额（元）', formatter: 'formatMoney' },
    { field: 'amountTax', title: '价税合计（元）', formatter: 'formatMoney' },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;

const [Grid, GridApi] = useVbenVxeGrid({ gridOptions: GridOptions });

watch(
  () => props.invoiceForm,
  (val = {}) => {
    GridApi.grid.reloadData(val.detailList);
  },
  { deep: true },
);
</script>

<template>
  <div>
    <BasicCaption content="开票内容" />
    <Grid />
  </div>
</template>
