<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { SettlementInfo } from '#/api'; // 引入数据类型

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Dropdown, Menu, MenuItem, message, Space, Table, TypographyLink } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  exportSettlementApi,
  getSettlementGenerateApi,
  getSettlementInfoApi,
  getSettlementListApi,
  settlementReportUpload,
} from '#/api';

// 表单配置：修改搜索条件为项目名称、用信申请名称、付款申请名称
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'creditApplyName',
      label: '用信申请名称',
    },
    {
      component: 'Input',
      fieldName: 'paymentApplyCode',
      label: '付款申请编号',
    },
  ],
  commonConfig: {
    labelCol: { span: 7 },
    wrapperCol: { span: 17 },
  },
});

// 表格配置：按要求调整列顺序和字段映射
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: { code: 'FCT_PROJECT_TYPE' },
      },
    },
    { field: 'creditApplyName', title: '用信申请名称', minWidth: 200 },
    { field: 'paymentApplyCode', title: '付款申请编号', minWidth: 200 },
    { field: 'launchDate', title: '起息日', minWidth: 120, formatter: 'formatDate' },
    { field: 'dueDate', title: '最后付息日', minWidth: 120, formatter: 'formatDate' },
    { field: 'totalAmount', title: '应还总额（元）', formatter: 'formatMoney' },
    { field: 'principalAmount', title: '应还本金（元）', formatter: 'formatMoney' },
    { field: 'interestAmount', title: '应还利息（元）', formatter: 'formatMoney' },
    { field: 'serviceAmount', title: '应还服务费（元）', formatter: 'formatMoney' },
    { field: 'actualTotalAmount', title: '已还总额（元）', formatter: 'formatMoney' },
    { field: 'actualPrincipalAmount', title: '已还本金（元）', formatter: 'formatMoney' },
    { field: 'actualInterestAmount', title: '已还利息（元）', formatter: 'formatMoney' },
    { field: 'actualServiceAmount', title: '已还服务费（元）', formatter: 'formatMoney' },
    { field: 'actualGraceInterestAmount', title: '已还宽限息（元）', formatter: 'formatMoney' },
    { field: 'actualOverdueInterestAmount', title: '已还罚息（元）', formatter: 'formatMoney' },
    {
      field: 'overdueStatus',
      title: '是否逾期',
      cellRender: {
        name: 'CellStatus',
        props: { code: 'FCT_OVERDUE_STATUS' },
      },
    },
    {
      field: 'settlementStatus',
      title: '结清状态',
      cellRender: {
        name: 'CellStatus',
        props: { code: 'FCT_SETTLEMENT_STATUS' },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        searchForm.value = formValues;
        return await getSettlementListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const uploadInfo = (row: any, type: string) => {
  uploadForm.value.type = type;
  uploadForm.value.id = row.id;
  uploadForm.value.label = {
    upload: '上传结清证明 （双章版）',
    check: '结清证明 （双章版）',
  }[type];
  uploadForm.value.businessType = 'FCT_SETTLEMENT_UPLOAD';
  modalApi.open();
};
// 上传弹窗配置
const uploadForm = ref<any>({
  businessType: '',
  type: '',
  id: null,
  label: '',
  attachmentList: [],
});
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await settlementReportUpload(uploadForm.value);
    message.success($t('base.resSuccess'));
    await GridApi.reload();
    await modalApi.close();
  },
  onClosed: () => {
    uploadForm.value = { businessType: '' };
  },
});
const searchForm = ref<any>({});
// 单据导出
const documentExport = async () => {
  await exportSettlementApi(searchForm.value);
  message.success($t('base.resSuccess'));
};

// 解构表格 API（用于刷新表格）
const [Grid, GridApi] = useVbenVxeGrid({ formOptions, gridOptions });

const generateSettlement = async (row: any) => {
  try {
    const res = await getSettlementInfoApi(row.id); // 调用接口获取详情
    settlementReport.value = res;
    settlementReport.value.projectType = row.projectType;
    settlementReport.value.id = row.id;
    await settlementModalApi.open();
  } catch {
    message.error('获取结清证明失败，请稍后重试');
  }
};

// ********** 新增：结清证明弹窗功能 ********** //
// 1. 弹窗实例 & 数据存储
const [SettlementModal, settlementModalApi] = useVbenModal({
  title: '结清证明',
  confirmText: '导出结清证明',
  onConfirm: async () => {
    await getSettlementGenerateApi(settlementReport.value.id as number);
    message.success($t('base.resSuccess'));
    await settlementModalApi.close();
    await GridApi.reload();
  },
  onClosed: () => {
    settlementReport.value = {}; // 关闭后清空数据
  },
});
const settlementReport = ref<SettlementInfo>({}); // 存储接口返回的证明数据

// 2. 结清证明明细表格列配置
const reportColumns = [
  { title: '序号', dataIndex: 'serialNumber', width: 50 },
  { title: '应付款日期', dataIndex: 'dueDate' },
  { title: '服务费（元）', dataIndex: 'serviceAmount' },
  { title: '本金（元）', dataIndex: 'principalAmount' },
  { title: '利息（元）', dataIndex: 'interestAmount' },
  { title: '应回款金额（元）', dataIndex: 'amount' },
  { title: '实际回款日期', dataIndex: 'actualDueDate' },
  { title: '实际回款金额（元）', dataIndex: 'actualAmount' },
  { title: '备注', dataIndex: 'remarks', width: 100 },
];
</script>

<template>
  <Page auto-content-height>
    <!-- 主表格 -->
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="documentExport"> 导出 </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <!-- 生成结清证明（未结清时显示） -->
                <MenuItem
                  key="generate"
                  v-if="['unsettled'].includes(row.settlementStatus)"
                  @click="generateSettlement(row)"
                >
                  生成结清证明
                </MenuItem>

                <!-- 上传结清证明（未结清时显示） -->
                <MenuItem
                  key="upload"
                  v-if="['unsettled'].includes(row.settlementStatus)"
                  @click="uploadInfo(row, 'upload')"
                >
                  上传结清证明（双章版）
                </MenuItem>

                <!-- 下载结清证明（已结清时显示） -->
                <MenuItem
                  key="download"
                  v-if="['settled'].includes(row.settlementStatus)"
                  @click="uploadInfo(row, 'check')"
                >
                  结清证明（双章版）
                </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </a-space>
      </template>
    </Grid>

    <!-- 上传弹窗 -->
    <Modal :title="uploadForm.label" class="w-[800px]" :show-confirm-button="uploadForm.type === 'upload'">
      <BaseAttachmentList
        v-model="uploadForm.attachmentList"
        :business-id="uploadForm.id"
        :business-type="uploadForm.businessType"
        :edit-mode="uploadForm.type === 'upload'"
      />
    </Modal>

    <SettlementModal class="w-[1100px]">
      <div class="text-gray-800">
        <!-- 标题 -->
        <div class="mb-6 text-center text-2xl font-bold">
          江西省财投商业保理有限公司<br />
          结清证明
        </div>

        <!-- 致：融资企业 -->
        <div class="mb-4">致：{{ settlementReport.financingCompanyName }}</div>

        <div class="mb-6">
          我司与贵司于
          {{ settlementReport.contractSignDate }}
          已签署商业保理合同开展 {{ settlementReport.projectName }}，{{
            `${settlementReport.projectType === 'single' ? '融资保理期间为' : '授信期限为'}`
          }}
          {{ settlementReport.creditTerm }}个月 ，截至
          {{ settlementReport.dueDate }}
          涉及该笔项目的资金、发票、单据等均已结清，该项目正常终止，相关信息详见下表。
        </div>

        <!-- 明细表格 -->
        <Table
          :columns="reportColumns"
          :data-source="settlementReport.detailList || []"
          bordered
          size="small"
          class="mb-6"
          :pagination="false"
        />

        <!-- 回寄说明（固定内容） -->
        <div class="mb-6">
          若贵司无异议，烦请贵司用章后寄回。<br />
          回寄地址：江西省南昌市红谷滩区金控大厦12楼 运营管理部
        </div>

        <!-- 盖章 & 日期（动态 + 固定） -->
        <div class="flex justify-between">
          <!-- 融资企业侧 -->
          <div>
            {{ settlementReport.financingCompanyName }}<br />
            （公司盖章）<br /><br /><br />
            {{ settlementReport.currentDate }}
          </div>
          <!-- 保理公司侧（固定名称，动态日期） -->
          <div>
            江西省财投商业保理有限公司<br />
            （公司盖章）<br /><br /><br />
            {{ settlementReport.currentDate }}
          </div>
        </div>
      </div>
    </SettlementModal>
  </Page>
</template>

<style></style>
