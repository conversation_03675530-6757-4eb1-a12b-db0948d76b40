<script setup lang="ts">
import { BasicCaption } from '@vben/fe-ui';
import { formatMoney } from '@vben/utils';

defineProps({
  creditPricingForm: { type: Object, default: () => ({}) },
  descriptionsProp: { type: Object, default: () => ({}) },
});
</script>

<template>
  <div>
    <BasicCaption content="用信定价方案" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="用信主体">
        {{ creditPricingForm.creditUseCompanyName }}
      </a-descriptions-item>
      <a-descriptions-item label="基准利率（%/年）">
        {{ formatMoney(creditPricingForm.pricingBasicRatio) }}
      </a-descriptions-item>
      <a-descriptions-item label="用信定价综合收益率(%/年)">
        {{ formatMoney(creditPricingForm.pricingXirrRate) }}
      </a-descriptions-item>
      <a-descriptions-item label="浮动利率（%/年）">
        {{ formatMoney(creditPricingForm.pricingFloatingRatio) }}
      </a-descriptions-item>
      <a-descriptions-item label="定价方案说明" :span="2">
        {{ creditPricingForm.pricingDesc }}
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style scoped></style>
