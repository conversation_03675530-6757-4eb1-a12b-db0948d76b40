<script setup lang="ts">
import type { CreditPricingInfo } from '#/api';

import { reactive, ref } from 'vue';

import {
  BASE_PAGE_CLASS_NAME,
  COL_SPAN_PROP,
  DESCRIPTIONS_PROP,
  FORM_PROP,
  FULL_FORM_ITEM_PROP,
} from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { formatMoney } from '@vben/utils';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getCreditPricingInfoApi, recheckCreditPricingApi, rejectCreditPricingApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';
import { validateCreditRate } from '#/views/project/components/validationCreditRate.ts';
import BaseDetail from '#/views/project/credit-pricing/components/base-detail.vue';

const emit = defineEmits(['ok', 'register']);
const { startWorkflow, initWorkflow, WorkflowPreviewModal } = useWorkflowBase();
const init = async (data: CreditPricingInfo) => {
  await initWorkflow({ formKey: 'fct_project_credit_pricing', businessKey: data.id });
  let info = data.id ? await getCreditPricingInfoApi(data.id as number) : data;
  const { id: _, ...calculation } = info.calculation;
  info = {
    ...info,
    ...calculation,
  };
  // info.expectedLaunchDate = dayjs(info.expectedLaunchDate).valueOf().toString();
  // info.expectedDueDate = dayjs(info.expectedDueDate).valueOf().toString();
  creditPricingForm.value = info;
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const creditPricingForm = ref<CreditPricingInfo>({});
const loading = reactive({
  submit: false,
});
const save = async (type?: string) => {
  await FormRef.value.validate();

  // 授信费率>=定价底线（基准定价+浮动定价）
  const result = validateCreditRate(creditPricingForm.value);

  if (!result.passed) {
    message.error(result.message);
    return;
  }
  let api;
  if (creditPricingForm.value.id) {
    api = recheckCreditPricingApi;
  }
  const formData = cloneDeep(creditPricingForm.value);
  if (type === 'submit') {
    formData.isSubmit = true;
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    formData.processDefinitionKey = processDefinitionKey;
    formData.startUserSelectAssignees = startUserSelectAssignees;
  }
  loading.submit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const reject = async () => {
  loading.submit = true;
  try {
    const res = await rejectCreditPricingApi(creditPricingForm.value.id as number);
    message.success('驳回成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const rules = {
  pricingFloatingRatio: [{ required: true, message: '请输入浮动定价' }],
  pricingDesc: [{ required: true, message: '请输入定价方案说明' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
</script>

<template>
  <BasicPopup title="用信定价" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" danger @click="reject">驳回</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseDetail :credit-pricing-form="creditPricingForm" :descriptions-prop="descriptionsProp" />
      <a-form ref="FormRef" class="mt-5" :model="creditPricingForm" :rules="rules" v-bind="formProp">
        <BasicCaption content="用信定价方案" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="用信主体">
              {{ creditPricingForm.creditUseCompanyName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="基准利率（%/年）">
              {{ formatMoney(creditPricingForm.pricingBasicRatio) }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信定价综合收益率(%/年)">
              {{ formatMoney(creditPricingForm.pricingXirrRate) }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="浮动利率（%/年）" name="pricingFloatingRatio">
              <a-input-number
                v-model:value="creditPricingForm.pricingFloatingRatio"
                placeholder="请输入浮动利率（%/年）"
                :controls="false"
                class="w-full"
                :precision="2"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="定价方案说明" name="pricingDesc" v-bind="fullProp">
              <a-textarea
                v-model:value="creditPricingForm.pricingDesc"
                :rows="4"
                class="w-full"
                placeholder="请输入定价方案说明"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <DebtServiceDetail
        :debt-service-form="creditPricingForm"
        :descriptions-prop="descriptionsProp"
        debt-type="creditPricing"
      />
      <RepaymentCalculationDetail
        :calculation-form="creditPricingForm"
        :descriptions-prop="descriptionsProp"
        calculation-type="CreditPricing"
      />
      <BaseAttachmentList
        v-model="creditPricingForm.attachmentList"
        :business-id="creditPricingForm.id"
        business-type="FCT_PROJECT_CREDIT_PRICING"
        edit-mode
      />
    </div>
    <WorkflowPreviewModal />
  </BasicPopup>
</template>

<style></style>
