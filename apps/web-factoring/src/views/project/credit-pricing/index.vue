<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CreditPricingInfo, PricingInfo } from '#/api';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { changeCreditPricingStatusApi, delCreditPricingApi, getCreditPricingPageListApi } from '#/api';

import CreditPricingChange from './credit-pricing-change.vue';
import CreditPricingDetail from './credit-pricing-detail.vue';
import CreditPricingEdit from './credit-pricing-edit.vue';
import CreditPricingReview from './credit-pricing-review.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'pricingName',
      label: '用信定价名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_PRICING_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'pricingName', title: '用信定价名称', minWidth: 200 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    { field: 'targetCompanyName', title: '合作企业', minWidth: 200 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PRICING_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCreditPricingPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: CreditPricingInfo) => {
  if (row.isChange) {
    openChangePopup(true, row);
  } else {
    openFormPopup(true, row);
  }
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerReview, { openPopup: openReviewPopup }] = usePopup();
const [registerChange, { openPopup: openChangePopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const recheck = (row: PricingInfo) => {
  const data = { ...row };
  openReviewPopup(true, data);
};
const change = async (row: PricingInfo) => {
  await changeCreditPricingStatusApi(row.id as number);
  const data = { ...row };
  await gridApi.reload();
  openChangePopup(true, data);
};
const viewDetail = (row: AccessInfo) => {
  openDetailPopup(true, row);
};
const del = (row: CreditPricingInfo) => {
  const id = row.id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该用信定价，是否继续？',
    async onOk() {
      await delCreditPricingApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

// 使用组合式函数处理URL参数自动打开弹层
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: CreditPricingInfo = {
        id: params.id,
      };
      edit(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: CreditPricingInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: CreditPricingInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});
const audit = (row: CreditPricingInfo) => {
  const data = { ...row, pageType: 'audit' };
  openDetailPopup(true, data);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT', 'EDITING'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
          <a-typography-link v-if="['EFFECTIVE'].includes(row.status)" @click="recheck(row)"> 复核 </a-typography-link>
          <a-typography-link v-if="['COMPLETED'].includes(row.status)" @click="change(row)"> 变更 </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <CreditPricingEdit @register="registerForm" @ok="editSuccess" />
    <CreditPricingReview @register="registerReview" @ok="editSuccess" />
    <CreditPricingChange @register="registerChange" @ok="editSuccess" />
    <CreditPricingDetail @register="registerDetail" @ok="editSuccess" />
  </Page>
</template>

<style></style>
