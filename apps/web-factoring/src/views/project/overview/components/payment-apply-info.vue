<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPaymentApplyPageListApi } from '#/api';
import PaymentApplyDetail from '#/views/fund/payment-apply/payment-apply-detail.vue';

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'applyCode', title: '付款申请编号', minWidth: 180 },
    { field: 'projectCreditApplyName', title: '用信申请名称', minWidth: 200 },
    { field: 'investAmount', title: '投放金额（元）', formatter: 'formatMoney' },
    { field: 'payeeCompanyName', title: '收款单位' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPaymentApplyPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
          projectId: form.value.id,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const form = ref({});
const init = (data: any) => {
  form.value = data;
  gridApi.reload();
};
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: any) => {
  openDetailPopup(true, row);
};
defineExpose({ init });
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <PaymentApplyDetail @register="registerDetail" />
  </Page>
</template>

<style></style>
