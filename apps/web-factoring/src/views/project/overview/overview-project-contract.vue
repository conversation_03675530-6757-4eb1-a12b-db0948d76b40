<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { ContractInfo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty } from 'lodash-es';

import { addContract, editContract, getProjectContractDetail } from '#/api';
import { ContractList } from '#/components';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);
const { WorkflowPreviewModal, startWorkflow, initWorkflow } = useWorkflowBase();
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const contractForm = ref<ContractInfo>({
  contractAmount: undefined,
  contractCode: '',
  contractName: '',
  contractEndDate: undefined,
  contractList: [],
  contractStartDate: undefined,
  contractTerm: undefined,
  creditAmount: undefined,
  creditExpireDate: '',
  creditTerm: undefined,
  decisionDate: '',
  projectId: undefined,
  signDate: '',
});

const contractDateRange = computed({
  get() {
    return [contractForm.value.contractStartDate, contractForm.value.contractEndDate];
  },
  set(newValue) {
    if (Array.isArray(newValue) && newValue.length === 2) {
      contractForm.value.contractStartDate = newValue[0];
      contractForm.value.contractEndDate = newValue[1];
    }
  },
});
const projectType = ref<string | undefined>('');
// 添加初始化方法
const init = async (data: any) => {
  await initWorkflow({ formKey: 'fct_project_contract', businessKey: data.id });
  if (Object.keys(data).length > 0) {
    contractForm.value.projectId = data?.projectId as number;
    contractForm.value.projectName = data?.projectName;
    contractForm.value.projectApplyId = data?.projectApplyId;
    contractForm.value.projectApplyName = data?.projectApplyName;
    contractForm.value.projectType = data?.projectType;
    projectType.value = data?.projectType;
    if (data.id) {
      const res = await getProjectContractDetail(data.id as number);
      res.contractEndDate = dayjs(res.contractEndDate).valueOf();
      res.contractStartDate = dayjs(res.contractStartDate).valueOf();
      res.decisionDate = res.decisionDate && dayjs(res.decisionDate).valueOf().toString();
      res.creditExpireDate = res.creditExpireDate && dayjs(res.creditExpireDate).valueOf().toString();
      res.signDate = dayjs(res.signDate).valueOf().toString();
      contractForm.value = { ...contractForm.value, ...res };
    }
  } else {
    Object.assign(contractForm, {
      contractAmount: undefined,
      contractCode: '',
      contractName: '',
      contractEndDate: undefined,
      contractList: [],
      sealContractList: [],
      contractStartDate: undefined,
      contractTerm: undefined,
      creditAmount: undefined,
      creditExpireDate: '',
      creditTerm: undefined,
      decisionDate: '',
      projectId: undefined,
      signDate: '',
    });
  }
};
const rules: Record<string, Rule[]> = {
  decisionDate: [{ required: true, message: '请输入决策日期', trigger: 'change' }],
  creditTerm: [{ required: true, message: '请输入授信期限' }],
  creditExpireDate: [{ required: true, message: '请输入授信到期日', trigger: 'change' }],
  creditAmount: [{ required: true, message: '请输入授信金额' }],
  contractCode: [{ required: true, message: '请输入主合同编号' }],
  contractName: [{ required: true, message: '请输入主合同名称' }],
  signDate: [{ required: true, message: '请选择合同签订日期', trigger: 'change' }],
  contractStartDate: [{ required: true, message: '请选择合同开始日期', trigger: 'change' }],
  contractTerm: [{ required: true, message: '请输入合同期限' }],
  contractAmount: [{ required: true, message: '请输入主合同金额' }],
};
const [registerPopup, { closePopup }] = usePopupInner((data) => init(data));
const formRef = ref();
const loading = reactive({
  submit: false,
});
const save = async (type: string) => {
  await formRef.value.validate();
  const params = cloneDeep(contractForm.value);
  params.contractStartDate = contractDateRange.value[0];
  params.contractEndDate = contractDateRange.value[1];
  if (type === 'submit') {
    params.isSubmit = true;
    if (!isEmpty(contractForm.value.contractList)) {
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      params.processDefinitionKey = processDefinitionKey;
      params.startUserSelectAssignees = startUserSelectAssignees;
    }
  }
  loading.submit = true;
  const api = params.id ? editContract : addContract;
  try {
    const res = await api(params);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};

const handleClose = () => {
  contractForm.value = {};
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="合同信息" @register="registerPopup" @close="handleClose">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="formRef" :model="contractForm" :rules="rules" v-bind="formProp" class="">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联项目">
              {{ contractForm.projectName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="projectType === 'comprehensive'">
            <a-form-item label="关联用信申请">
              {{ contractForm.projectApplyName }}
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption v-if="projectType === 'single'" content="总经办决策" />
        <a-row v-if="projectType === 'single'" class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="决策日期" name="decisionDate">
              <a-date-picker v-model:value="contractForm.decisionDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信期限（个月）" name="creditTerm">
              <a-input-number v-model:value="contractForm.creditTerm" :controls="false" class="w-full" :precision="0" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信到期日" name="creditExpireDate">
              <a-date-picker v-model:value="contractForm.creditExpireDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信额度（元）" name="creditAmount">
              <a-input-number
                v-model:value="contractForm.creditAmount"
                :controls="false"
                class="w-full"
                :precision="0"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="主合同信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="保理主合同名称" name="contractName">
              <a-input v-model:value="contractForm.contractName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="保理主合同编号" name="contractCode">
              <a-input v-model:value="contractForm.contractCode" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="签署日期" name="signDate">
              <a-date-picker v-model:value="contractForm.signDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="合同日期范围" name="contractStartDate">
              <a-range-picker v-model:value="contractDateRange" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <!-- <a-col v-bind="colSpan">
            <a-form-item label="合同结束日期" name="contractEndDate">
              <a-date-picker v-model:value="contractForm.contractEndDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col> -->
          <a-col v-bind="colSpan">
            <a-form-item label="合同期限（个月）" name="contractTerm">
              <a-input-number
                v-model:value="contractForm.contractTerm"
                :controls="false"
                class="w-full"
                :precision="0"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="主合同金额（元）" name="contractAmount">
              <a-input-number
                v-model:value="contractForm.contractAmount"
                :controls="false"
                class="w-full"
                :precision="0"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <ContractList
        v-model="contractForm.contractList"
        :business-id="contractForm.id"
        :business-type="projectType === 'comprehensive' ? 'FCT_PROJECT_APPLY_CONTRACT' : 'FCT_PROJECT_CONTRACT'"
        edit-mode
      >
        <template #header>
          <BasicCaption content="未盖章需审批合同" class="mb-4" />
        </template>
      </ContractList>
      <ContractList
        v-model="contractForm.sealContractList"
        :business-id="contractForm.id"
        :business-type="
          projectType === 'comprehensive' ? 'FCT_PROJECT_APPLY_CONTRACT_SEAL' : 'FCT_PROJECT_CONTRACT_SEAL'
        "
        edit-mode
      >
        <template #header>
          <BasicCaption content="盖章合同" class="mb-4" />
        </template>
      </ContractList>
    </div>
    <WorkflowPreviewModal />
  </BasicPopup>
</template>

<style scoped>
:deep(.fileBtn) div {
  display: none !important;
}

.fileBtn {
  margin-right: 10px;

  .mb-1 {
    margin-bottom: 0;
  }
}
</style>
