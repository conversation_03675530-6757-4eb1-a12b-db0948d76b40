<script setup lang="ts">
import type { ManagerInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getManagerInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const {
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isWorkflow,
  isWorkflowLoading,
} = useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: ManagerInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_project_meeting_manager', businessKey: data.id });
  managerForm.value = data.id ? await getManagerInfoApi(data.id as number) : data;
};
const managerForm = ref<ManagerInfo>({});
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="总经办会议详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联项目评审会议">
          {{ managerForm.projectReviewName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ managerForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="总经办会议名称" :span="2">
          {{ managerForm.reviewNodeName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目背景" :span="2">
          {{ managerForm.backgroundDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="项目风险点及建议" :span="2">
          {{ managerForm.riskMitigationDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="项目结论" :span="2">
          {{ managerForm.resultDesc }}
        </a-descriptions-item>
      </a-descriptions>
      <BaseAttachmentList :business-id="managerForm.id" business-type="FCT_PROJECT_MEETING_MANAGER" />
      <BaseAttachmentList :business-id="managerForm.id" business-type="FCT_PROJECT_MEETING_MANAGER_MINUTES">
        <template #header>
          <BasicCaption content="总经办会议纪要" />
        </template>
      </BaseAttachmentList>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
