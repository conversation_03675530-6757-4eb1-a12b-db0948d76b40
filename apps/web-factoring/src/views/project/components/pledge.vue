<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InitiationInfo } from '#/api';

import { useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { formatDate, isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import PledgePopup from './pledge-popup.vue';

const baseForm = defineModel({ type: Object, required: true });
const init = (form: InitiationInfo) => {
  gridApi.grid.reloadData(form.pledgeList);
};
const GridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'pledgeName', title: '质押物名称', width: 140 },
    { field: 'pledgorCompanyName', title: '出质人' },
    { field: 'pledgeeCompanyName', title: '质权人' },
    { field: 'mainContractCode', title: '质押主合同号码' },
    { field: 'pledgeContractCode', title: '质押合同编号' },
    { field: 'mainContractAmount', title: '主合同金额（元）', formatter: 'formatMoney' },
    { field: 'mortgageAmount', title: '债务履行期限', slots: { default: 'mortgageAmount-span' } },
    { field: 'pledgeAmount', title: '质押财产价值 （元）', formatter: 'formatMoney' },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: GridOptions,
});
const [Modal, modalApi] = useVbenModal({
  connectedComponent: PledgePopup,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      const selected = modalApi.getData() || [];
      if (Array.isArray(selected)) {
        gridApi.grid.reloadData(selected);
      }
    }
  },
});
const del = () => {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.error('请选择数据');
    return false;
  }
  gridApi.grid.removeCheckboxRow(selectedRows);
};
const save = async () => {
  const { visibleData } = gridApi.grid.getTableData();
  if (!isEmpty(visibleData)) {
    visibleData.forEach((item: any) => {
      item.startDebtPeriodDate = dayjs(item.startDebtPeriodDate).valueOf();
      item.endDebtPeriodDate = dayjs(item.endDebtPeriodDate).valueOf();
    });
  }
  baseForm.value.pledgeList = visibleData;
  return baseForm.value;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <BasicCaption content="质押物" />
    <Grid>
      <template #toolbar-tools>
        <a-space>
          <a-button class="mr-2" type="primary" @click="modalApi.setData({}).open()"> 选择质押物 </a-button>
          <a-button type="primary" danger @click="del">
            {{ $t('base.del') }}
          </a-button>
        </a-space>
      </template>
      <template #mortgageAmount-span="{ row }">
        {{ formatDate(row.startDebtPeriodDate) }} - {{ formatDate(row.endDebtPeriodDate) }}
      </template>
    </Grid>
    <Modal />
  </div>
</template>
