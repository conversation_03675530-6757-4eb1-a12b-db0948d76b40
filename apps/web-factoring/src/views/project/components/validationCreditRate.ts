/**
 * 校验授信费率是否满足定价底线要求
 * @param params 包含三个参数的对象
 * @param params.pricingCreditRate 授信费率（%/年）
 * @param params.pricingBasicRatio 基准定价（%/年）
 * @param params.pricingFloatingRatio 浮动定价（%/年）
 * @returns 校验结果对象 { passed: boolean, message?: string }
 */
export const validateCreditRate = ({
  pricingCreditRate,
  pricingBasicRatio,
  pricingFloatingRatio,
}: {
  pricingBasicRatio?: number | string;
  pricingCreditRate?: number | string;
  pricingFloatingRatio?: number | string;
}): { message?: string; passed: boolean } => {
  // 转换为数字（处理字符串/空值，默认0避免计算错误）
  const creditRate = Number(pricingCreditRate) || 0;
  const basicRatio = Number(pricingBasicRatio) || 0;
  const floatingRatio = Number(pricingFloatingRatio) || 0;
  const pricingFloor = basicRatio + floatingRatio; // 定价底线 = 基准定价 + 浮动定价

  // 校验逻辑
  if (creditRate < pricingFloor) {
    return {
      passed: false,
      message: `授信费率不能低于定价底线（基准定价 + 浮动定价）`,
    };
  }

  return { passed: true };
};
