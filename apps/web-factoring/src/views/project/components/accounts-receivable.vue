<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InitiationInfo } from '#/api';

import { useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import AccountsPopup from './accounts-popup.vue';

const emit = defineEmits(['changeReceivable']);
const baseForm = defineModel({ type: Object, required: true });
const init = (form: InitiationInfo) => {
  gridApi.grid.reloadData(form.receivableRefList);
};
const GridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'receivableName', title: '应收账款名称', width: 180 },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'receivableAmount', title: '应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'receivableTerm', title: '应收账款期限（月）' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: GridOptions,
});
const [Modal, modalApi] = useVbenModal({
  connectedComponent: AccountsPopup,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      const selectedAccounts = modalApi.getData() || [];
      if (Array.isArray(selectedAccounts)) {
        gridApi.grid.reloadData(selectedAccounts);
        baseForm.value.receivableRefList = selectedAccounts;
        emit('changeReceivable');
      }
    }
  },
});
const del = () => {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.error('请选择数据');
    return false;
  }
  gridApi.grid.removeCheckboxRow(selectedRows);
  const { visibleData } = gridApi.grid.getTableData();
  baseForm.value.receivableRefList = visibleData;
  emit('changeReceivable');
};
const save = async () => {
  const { visibleData } = gridApi.grid.getTableData();
  if (!isEmpty(visibleData)) {
    visibleData.forEach((item: any) => {
      item.receivableDueDate = dayjs(item.receivableDueDate).valueOf();
    });
  }
  baseForm.value.receivableRefList = visibleData;
  return baseForm.value;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <BasicCaption content="应收账款信息" />
    <Grid>
      <template #toolbar-tools>
        <a-space>
          <a-button class="mr-2" type="primary" @click="modalApi.setData({}).open()"> 选择应收账款 </a-button>
          <a-button type="primary" danger @click="del">
            {{ $t('base.del') }}
          </a-button>
        </a-space>
      </template>
    </Grid>
    <Modal />
  </div>
</template>
