<script setup lang="ts">
import { BasicCaption } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { formatMoney } from '@vben/utils';

defineProps({
  pricingForm: { type: Object, default: () => ({}) },
  descriptionsProp: { type: Object, default: () => ({}) },
});
const dictStore = useDictStore();
</script>

<template>
  <div>
    <BasicCaption :content="`一般定价方案（${dictStore.formatter(pricingForm.projectType, 'FCT_PROJECT_TYPE')}）`" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <template v-if="pricingForm.projectType === 'comprehensive'">
        <a-descriptions-item label="授信额度（元）">
          {{ formatMoney(pricingForm.pricingCreditAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="授信期限（个月）">
          {{ pricingForm.pricingCreditTerm }}
        </a-descriptions-item>
        <a-descriptions-item label="授信费率（%）">
          {{ formatMoney(pricingForm.pricingCreditRate) }}
        </a-descriptions-item>
        <a-descriptions-item label="授信额度类型">
          {{ dictStore.formatter(pricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
        </a-descriptions-item>
      </template>
      <a-descriptions-item label="基准定价（%/年）">
        {{ formatMoney(pricingForm.pricingBasicRatio) }}
      </a-descriptions-item>
      <a-descriptions-item label="浮动定价（%/年）">
        {{ formatMoney(pricingForm.pricingFloatingRatio) }}
      </a-descriptions-item>
      <template v-if="pricingForm.projectType === 'single'">
        <a-descriptions-item label="综合收益率（%/年）">
          {{ formatMoney(pricingForm.pricingXirrRate) }}
        </a-descriptions-item>
        <a-descriptions-item />
      </template>
      <a-descriptions-item label="定价方案说明" :span="2">
        {{ pricingForm.pricingDesc }}
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style scoped></style>
