<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getPricingLogDetailApi } from '#/api';
import BaseDetail from '#/views/project/pricing/components/base-detail.vue';
import PricingScheme from '#/views/project/pricing/components/pricing-scheme.vue';

const props = defineProps({
  pricingForm: { type: Object, default: () => ({}) },
  descriptionsProp: { type: Object, default: () => ({}) },
});
const gridOptions = {
  columns: [
    {
      field: 'changeDate',
      title: '项目定价变更时间',
      minWidth: '150px',
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

watch(
  () => props.pricingForm,
  (val = {}) => {
    gridApi.grid.reloadData(val.historyList ?? []);
  },
  { deep: true },
);
const projectForm = ref({});
const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  onClosed: () => {
    modalApi.close();
  },
});
const handleView = async (row: any = {}) => {
  projectForm.value = await getPricingLogDetailApi(row.id);
  modalApi.open();
};
</script>

<template>
  <div>
    <BasicCaption content="项目定价变更记录" />
    <grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="handleView(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </grid>
    <Modal title="历史版本信息" class="w-[1000px]">
      <BaseDetail :pricing-form="projectForm" :descriptions-prop="descriptionsProp" />
      <PricingScheme :pricing-form="projectForm" :descriptions-prop="descriptionsProp" />
      <BaseAttachmentList :business-id="pricingForm.id" business-type="FCT_PROJECT_PRICING" />
    </Modal>
  </div>
</template>
