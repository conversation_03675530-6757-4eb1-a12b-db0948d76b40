<script setup lang="ts">
import type { PricingInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getPricingInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';
import RepaymentCalculationHistory from '#/views/project/components/repayment-calculation-history.vue';
import BaseDetail from '#/views/project/pricing/components/base-detail.vue';
import ComprehensiveChangeRecord from '#/views/project/pricing/components/comprehensive-change-record.vue';
import PricingScheme from '#/views/project/pricing/components/pricing-scheme.vue';

const emit = defineEmits(['ok', 'register']);
const { WorkflowPreviewModal, useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading } =
  useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const init = async (data: PricingInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_project_pricing', businessKey: data.id });
  let info = data.id ? await getPricingInfoApi(data.id as number) : data;
  if (info.projectType === 'single') {
    const calculation = omit(
      info.calculation,
      'id',
      'targetCompanyName',
      'targetCompanyCode',
      'projectCode',
      'projectName',
    );
    info = {
      ...info,
      ...calculation,
    };
  }
  pricingForm.value = info;
  RepaymentCalculationHistoryRef.value.init(pricingForm.value);
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const pricingForm = ref<PricingInfo>({});
const RepaymentCalculationHistoryRef = ref();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="项目定价信息" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseDetail :pricing-form="pricingForm" :descriptions-prop="descriptionsProp" />
      <PricingScheme :pricing-form="pricingForm" :descriptions-prop="descriptionsProp" />
      <div v-show="pricingForm.projectType === 'single'">
        <DebtServiceDetail :debt-service-form="pricingForm" :descriptions-prop="descriptionsProp" />
        <RepaymentCalculationDetail
          :calculation-form="pricingForm"
          :descriptions-prop="descriptionsProp"
          calculation-type="Pricing"
        />
        <RepaymentCalculationHistory ref="RepaymentCalculationHistoryRef" calculation-type="Pricing" />
      </div>
      <ComprehensiveChangeRecord
        v-show="pricingForm.projectType === 'comprehensive'"
        :pricing-form="pricingForm"
        :descriptions-prop="descriptionsProp"
      />
      <BaseAttachmentList :business-id="pricingForm.id" business-type="FCT_PROJECT_PRICING" />
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
