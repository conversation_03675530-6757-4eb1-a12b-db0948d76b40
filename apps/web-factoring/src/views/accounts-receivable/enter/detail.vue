<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ReceivableContractVO, ReceivableInvoiceVO, ReceivableVO } from '#/api';

import { reactive } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { cloneDeep, formatDate, formatMoney } from '@vben/utils';

import { message } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPreviewFileExternalLink } from '#/api';

const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const baseFormInfo = reactive<ReceivableVO>({
  attachmentList: [],
  projectId: [],
  projectName: '',
  absBasisAssetId: undefined,
  bizType: undefined,
  contractList: [],
  creditorDebtorDel: '',
  creditorList: [],
  creditorName: '',
  debtorList: [],
  debtorName: '',
  id: undefined,
  invoiceList: [],
  receivableAmount: 0,
  receivableCode: '',
  receivableDueDate: undefined,
  receivableName: '',
  receivableTerm: 0,
  remarks: '',
  status: '',
  zdCode: '',
  zdStatus: '',
  isSubmit: false,
});

// 添加初始化方法
const init = (data: ReceivableVO) => {
  if (data) {
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));
    // 初始化表格数据
    if (gridApiContract.grid && data.contractList) {
      gridApiContract.grid.reloadData(data.contractList);
    }
    if (gridApiInvoice.grid && data.invoiceList) {
      gridApiInvoice.grid.reloadData(data.invoiceList);
    }
  }
};
const [registerPopup] = usePopupInner((data) => init(data));

const gridContractTable: VxeGridProps<ReceivableContractVO> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { field: 'contractName', title: '基础合同名称' },
    { field: 'contractCode', title: '基础合同编号' },
    {
      field: 'contractType',
      title: '基础合同类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'RECEIVABLE_CONTRACT_TYPE',
        },
      },
    },
    { field: 'totalAmount', title: '基础合同金额（元）', formatter: 'formatMoney' },
    { field: 'unpaidAmount', title: '未付款金额（元）', formatter: 'formatMoney' },
    { field: 'transferAmount', title: '拟转让应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'voucherName', title: '凭证名称' },
    { field: 'voucherNumber', title: '凭证号码' },
    { field: 'signedDate', title: '签署日期' },
    // { field: 'file', title: '上传附件', fixed: 'right', width: 160 },
  ],
  data: baseFormInfo.contractList || [],
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const viewFile = async (row: any) => {
  const res = await getPreviewFileExternalLink({ id: row.fileId });
  if (res) window.open(res);
  else message.error('获取文件链接失败');
};

const gridInvoiceTable: VxeGridProps<ReceivableInvoiceVO> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  data: baseFormInfo.invoiceList || [],
  columns: [
    {
      field: 'invoiceType',
      title: '发票类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'RECEIVABLE_INVOICE_TYPE',
        },
      },
    },
    { field: 'invoiceNumber', title: '发票号码' },
    { field: 'invoiceCode', title: '发票代码' },
    { field: 'totalAmount', title: '不含税金额（元）', formatter: 'formatMoney' },
    { field: 'totalAmountTax', title: '含税金额（元）', formatter: 'formatMoney' },
    { field: 'invoiceDate', title: '开票日期' },
    { field: 'buyerName', title: '购买方' },
    { field: 'sellerName', title: '销售方' },
    { field: 'checkCode', title: '校验码后6位' },
    {
      field: 'verifyResult',
      title: '验真结果',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'RECEIVABLE_INVOICE_VERIFY_RESULT',
        },
      },
    },
    {
      field: 'file',
      title: '发票附件',
      fixed: 'right',
      width: 160,
      slots: { default: 'viewFile' },
    },
  ],
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [ContractTable, gridApiContract] = useVbenVxeGrid({ gridOptions: gridContractTable });
const [InvoiceTable, gridApiInvoice] = useVbenVxeGrid({ gridOptions: gridInvoiceTable });
</script>

<template>
  <BasicPopup v-bind="$attrs" title="应收账款详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <!-- <a-descriptions-item label="关联项目">
          {{ baseFormInfo.projectName || '-' }}
        </a-descriptions-item> -->
        <a-descriptions-item label="应收款账编号">
          {{ baseFormInfo.receivableCode || '-' }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="业务类型">
          {{ dictStore.formatter(baseFormInfo.bizType, 'FCT_FACTORING_TYPE') }}
        </a-descriptions-item> -->
        <a-descriptions-item label="债权人">
          {{ baseFormInfo.creditorName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="债务人">
          {{ baseFormInfo.debtorName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="应收款账名称">
          {{ baseFormInfo.receivableName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="债权人、债务人关系">
          {{ baseFormInfo.creditorDebtorDel || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="应收账款金额（元）">
          {{ formatMoney(baseFormInfo.receivableAmount) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="应收账款到期日">
          {{ formatDate(baseFormInfo.receivableDueDate) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="应收账款期限（个月）">
          {{ baseFormInfo.receivableTerm || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="应收账款描述">
          {{ baseFormInfo.remarks || '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="基础合同" />
      <ContractTable />
      <BasicCaption content="发票" />
      <InvoiceTable>
        <template #viewFile="{ row }">
          <a-button v-if="row.fileId" type="link" @click="viewFile(row)">查看</a-button>
        </template>
      </InvoiceTable>
      <BaseAttachmentList
        v-model="baseFormInfo.attachmentList"
        :business-id="baseFormInfo.id"
        business-type="FCT_RECEIVABLE"
      >
        <template #header>
          <TypographyText>应收账款佐证材料</TypographyText>
        </template>
      </BaseAttachmentList>
    </div>
  </BasicPopup>
</template>

<style scoped></style>
