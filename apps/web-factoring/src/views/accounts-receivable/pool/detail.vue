<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ReceivablePoolLogsVO, ReceivablePoolVO, ReceivableVO } from '#/api';

import { nextTick, reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatMoney } from '@vben/utils';

import { message, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { infoReceivableApi, replaceDetail<PERSON><PERSON> } from '#/api';

import Detail from '../enter/detail.vue';

const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const inPoolList = ref([]);
const outPoolList = ref([]);
const baseFormInfo = reactive<ReceivablePoolVO>({
  bizType: undefined,
  companyList: [],
  discountRate: undefined,
  isSubmit: false,
  id: undefined,
  poolCapitalLimit: undefined,
  poolCode: '',
  poolDueDate: undefined,
  poolName: '',
  poolTotalAmount: undefined,
  projectId: [],
  receivableList: [],
  receivablePoolLogsList: [],
  status: undefined,
  thresholdAmount: undefined,
  orderType: '',
  orderName: '',
  projectName: '',
});
// const poolCount = ref(0);
const totalAmount = ref(0);
// 添加初始化方法
const init = (data: ReceivablePoolVO) => {
  if (data) {
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));
    // 初始化表格数据
    if (poolApi.grid && data.receivableList) {
      poolApi.grid.reloadData(data.receivableList);
      // poolCount.value = data.receivableList.length > 0 ? data.receivableList.length : 0
      totalAmount.value = data.receivableList.reduce((pre, cur) => pre + Number(cur.receivableAmount), 0);
    }
    if (replacementApi.grid && data.receivablePoolLogsList) {
      replacementApi.grid.reloadData(data.receivablePoolLogsList);
    }
  }
};

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await modalApi.close();
      await inPoolApi.reload();
      await outPoolApi.reload();
    } catch {}
  },
});
const [registerPopup] = usePopupInner((data) => init(data));

const gridPoolTable: VxeGridProps<ReceivableVO> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'receivableName', title: '应收账款名称' },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'receivableAmount', title: '应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
    { field: 'action', title: '操作', fixed: 'right', width: 160, slots: { default: 'action' } },
  ],
  data: baseFormInfo.receivableList || [],
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();
const gridReplacementTable: VxeGridProps<ReceivablePoolLogsVO> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  data: baseFormInfo.receivablePoolLogsList || [],
  columns: [
    { field: 'logsCode', title: '置换记录编号' },
    { field: 'inputAmount', title: '入池金额（元）', formatter: 'formatMoney' },
    { field: 'outputAmount', title: '出池金额（元）', formatter: 'formatMoney' },
    { field: 'poolTotalAmount', title: '置换后池资产总金额（元）', formatter: 'formatMoney' },
    { field: 'poolFundRatio', title: '置换后保理融资比例（%）', formatter: 'formatMoney' },
    { field: 'operateTime', title: '置换提交时间', formatter: 'formatDate' },
    { field: 'action', title: '操作', fixed: 'right', width: 160, slots: { default: 'action' } },
  ],
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const gridInPoolTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  columns: [
    { field: 'receivableName', title: '应收账款名称' },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    {
      field: 'bizType',
      title: '业务类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_FACTORING_TYPE',
        },
      },
    },
    { field: 'receivableAmount', title: '应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
  ],
  data: baseFormInfo.receivableList || [],
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const gridOutPoolTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  columns: [
    { field: 'receivableName', title: '应收账款名称' },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    {
      field: 'bizType',
      title: '业务类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_FACTORING_TYPE',
        },
      },
    },
    { field: 'receivableAmount', title: '应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
  ],
  data: baseFormInfo.receivableList || [],
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const detailReplacement = async (_row: any) => {
  try {
    const res = await replaceDetailApi(_row.id);
    inPoolList.value = res?.inputReceivableList || [];
    outPoolList.value = res?.outputReceivableList || [];
    modalApi.open();
    await nextTick();
    inPoolApi.grid.reloadData(inPoolList.value);
    outPoolApi.grid.reloadData(outPoolList.value);
  } catch (error) {
    message.error(`打开失败: ${error.message}`);
  }
};

const [PoolTable, poolApi] = useVbenVxeGrid({ gridOptions: gridPoolTable });
const [inPoolTable, inPoolApi] = useVbenVxeGrid({ gridOptions: gridInPoolTable });
const [outPoolTable, outPoolApi] = useVbenVxeGrid({ gridOptions: gridOutPoolTable });
const [ReplacementTable, replacementApi] = useVbenVxeGrid({ gridOptions: gridReplacementTable });

const detailReceivable = async (_row: ReceivableVO) => {
  try {
    const res = await infoReceivableApi(_row.id);
    const data = cloneDeep(res);
    openDetailFormPopup(true, data);
  } catch {
    // message.error('删除失败: ' + error.message);
  }
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="应收账款池详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="应收账款池名称">
          {{ baseFormInfo.poolName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="应收款账池编号">
          {{ baseFormInfo.poolCode || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="池保理融资企业">
          {{ baseFormInfo.companyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="关联业务类型">
          {{ dictStore.formatter(baseFormInfo.orderType, 'FCT_RECEIVABLE_POOL_ORDER_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="关联业务单据名称">
          {{ baseFormInfo.orderName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="业务所属项目">
          {{ baseFormInfo.projectName || '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="池规则" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="折扣率（%）">
          {{ formatMoney(baseFormInfo.discountRate) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="“水位线”金额（元）">
          {{ formatMoney(baseFormInfo.thresholdAmount) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="当前保理融资金额（元）">
          {{ formatMoney(baseFormInfo.currentPoolFund) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="当前保理融资比例（%）">
          {{ formatMoney(baseFormInfo.currentPoolFundRatio) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="池内资产金额范围（元）">
          {{ formatMoney(baseFormInfo.poolAssetsMin) ?? '-' }} ~ {{ formatMoney(baseFormInfo.poolAssetsMax) ?? '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="入池资产有效期范围（天）">
          {{ baseFormInfo.poolAssetsValidMin ?? '-' }} ~ {{ baseFormInfo.poolAssetsValidMax ?? '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="应收账款池" />
      <PoolTable>
        <template #action="{ row }">
          <TypographyLink @click="detailReceivable(row)"> 详情</TypographyLink>
        </template>
      </PoolTable>
      <BasicCaption content="入池出池置换记录" />
      <ReplacementTable>
        <template #action="{ row }">
          <TypographyLink @click="detailReplacement(row)"> 详情</TypographyLink>
        </template>
      </ReplacementTable>
    </div>
  </BasicPopup>
  <Detail @register="detailForm" />
  <Modal title="置换记录详情" class="w-[1200px]">
    <BasicCaption content="置换入池记录" />
    <inPoolTable />
    <BasicCaption content="置换出池记录" />
    <outPoolTable />
  </Modal>
</template>

<style scoped></style>
