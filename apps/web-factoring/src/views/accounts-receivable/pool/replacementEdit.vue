<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ReceivablePoolVO, ReceivableVO } from '#/api';

import { reactive, ref, watch } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicPopup, usePopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { cloneDeep } from '@vben/utils';

import {
  Button,
  Col,
  Form,
  FormItem,
  Input,
  InputGroup,
  InputNumber,
  Row,
  Select,
  TypographyLink,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getCompanyListApi, infoReceivableApi } from '#/api';

import Detail from '../enter/detail.vue';
import ReceivableModal from './receivableModal.vue';

const emit = defineEmits(['register', 'replacement']);

// 定义模态框显示状态
const modalVisible = ref(false);
const baseFormInfo = reactive<ReceivablePoolVO>({
  bizType: undefined,
  companyList: [],
  discountRate: undefined,
  isSubmit: false,
  id: undefined,
  poolCapitalLimit: undefined,
  poolCode: '',
  poolDueDate: undefined,
  poolName: '',
  inputAmount: 0,
  outputAmount: 0,
  poolTotalAmount: undefined,
  poolFundRatio: undefined,
  projectId: [],
  receivableList: [],
  receivablePoolLogsList: [],
  status: undefined,
  thresholdAmount: undefined,
});

const totalAmount = ref(0);

const formRef = ref();
const handleSave = async () => {
  try {
    await formRef.value.validate();
    // 确保获取表格最新数据
    if (poolApi.grid) {
      const poolTableData = poolApi.grid.getTableData().fullData;
      const outPoolData = outPoolApi.grid.getTableData().fullData;
      const inPoolData = inPoolApi.grid.getTableData().fullData;

      const filteredPoolData = poolTableData.filter((item) => !outPoolData.some((outItem) => outItem.id === item.id));
      const mergedData = [...filteredPoolData, ...inPoolData];
      const uniqueData = [...new Map(mergedData.map((item) => [item.id, item])).values()];

      baseFormInfo.receivableList = uniqueData;
    }
    const params = cloneDeep(baseFormInfo);
    params.receivableList = params?.receivableList?.map((v) => ({
      ...v,
      receivableDueDate: dayjs(v.receivableDueDate, 'YYYY-MM-DD').valueOf(),
    }));
    emit('replacement', params);
  } catch (error) {
    console.error(`提交失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const submit = async () => {
  handleSave();
};
const poolTotalAmountCatch = ref(0);
const isChange = ref(false);
// 添加初始化方法
const init = (data: ReceivablePoolVO) => {
  if (data && Object.keys(data).length > 0) {
    isChange.value = data.type === 'change';
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));

    companyInfo.value = {
      key: data?.companyList?.[0]?.companyCode,
      label: data?.companyList?.[0]?.companyName,
    };

    // 初始化表格数据
    if (poolApi.grid && data.receivableList) {
      poolApi.grid.reloadData(data.receivableList);
      //   poolCount.value = Math.max(data.receivableList.length, 0);
      totalAmount.value = data.receivableList.reduce((pre, cur) => pre + Number(cur.receivableAmount), 0);
      baseFormInfo.poolTotalAmount = totalAmount.value;
      poolTotalAmountCatch.value = totalAmount.value;
    }
  } else {
    // 否则重置为初始状态
    Object.assign(baseFormInfo, {
      projectId: [],
      receivableCode: '',
      bizType: undefined,
      companyList: [],
      companyCodeList: [],
      discountRate: undefined,
      isSubmit: false,
      id: undefined,
      poolCapitalLimit: undefined,
      poolCode: '',
      poolDueDate: undefined,
      poolName: '',
      poolTotalAmount: undefined,
      receivableList: [],
      receivablePoolLogsList: [],
      status: undefined,
      thresholdAmount: undefined,
    });
    companyInfo.value = {};
  }
};

const outPoolList = ref<ReceivableVO[]>([]);
const inPoolList = ref<ReceivableVO[]>([]);

// 打开模态框方法
const openReceivableModel = () => {
  modalVisible.value = true;
};

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));

const gridPoolTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'receivableName', title: '应收账款名称' },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'receivableAmount', title: '应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
    { field: 'action', title: '操作', fixed: 'right', width: 160, slots: { default: 'action' } },
  ],
  data: baseFormInfo.receivableList || [],
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const gridOutPoolTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { field: 'receivableName', title: '应收账款名称' },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'bizType', title: ' 业务类型' },
    { field: 'receivableAmount', title: '应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
    { field: 'action', title: '操作', fixed: 'right', width: 160, slots: { default: 'action' } },
  ],
  data: outPoolList.value,
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const gridInPoolTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { field: 'receivableName', title: '应收账款名称' },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'bizType', title: ' 业务类型' },
    { field: 'receivableAmount', title: '应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
    { field: 'action', title: '操作', fixed: 'right', width: 160, slots: { default: 'action' } },
  ],
  data: inPoolList.value,
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();
const [OutPoolTable, outPoolApi] = useVbenVxeGrid({ gridOptions: gridOutPoolTable });
const [InPoolTable, inPoolApi] = useVbenVxeGrid({ gridOptions: gridInPoolTable });
const [PoolTable, poolApi] = useVbenVxeGrid({
  gridOptions: gridPoolTable,
});

// 修改删除行方法
const removeReceivable = (row: any, index: number) => {
  const $grid = inPoolApi.grid;
  if ($grid) {
    const tableData = $grid.getTableData();
    $grid.remove(tableData.fullData[index]);
  }
  baseFormInfo.poolTotalAmount = Number(baseFormInfo.poolTotalAmount) - Number(row.receivableAmount);
  baseFormInfo.inputAmount = Number(baseFormInfo.inputAmount) - Number(row.receivableAmount);
};
const detailReceivable = async (_row: ReceivableVO) => {
  try {
    const res = await infoReceivableApi(_row.id);
    const data = cloneDeep(res);
    openDetailFormPopup(true, data);
  } catch {
    // message.error('删除失败: ' + error.message);
  }
};
const handleReceivableConfirm = (data: ReceivableVO[]) => {
  const $grid = inPoolApi.grid;
  if ($grid) {
    const existingData = $grid.getTableData().fullData;
    const mergedData = [...existingData, ...data];
    const uniqueData = [...new Map(mergedData.map((item) => [item.id, item])).values()];
    $grid.remove();
    $grid.insert(uniqueData);
    // poolCount.value = uniqueData.length > 0 ? uniqueData.length : 0;
    baseFormInfo.inputAmount =
      uniqueData.reduce((sum, item) => {
        return sum + Number(item.receivableAmount ?? 0);
      }, 0) ?? 0;
    baseFormInfo.poolTotalAmount = Number(baseFormInfo.poolTotalAmount) + Number(baseFormInfo.inputAmount);
  }
};

const companyInfo = ref();

const replaceThePool = () => {
  const selectedRows = poolApi.grid.getCheckboxRecords();
  outPoolList.value = [];
  outPoolList.value = selectedRows;
  outPoolApi.grid.reloadData(outPoolList.value);
  baseFormInfo.outputAmount =
    outPoolList.value.reduce((sum, item) => {
      return sum + Number(item.receivableAmount ?? 0);
    }, 0) ?? 0;
  baseFormInfo.poolTotalAmount = poolTotalAmountCatch.value - baseFormInfo.outputAmount;
};
const removeOutRow = (row: ReceivableVO) => {
  outPoolList.value = outPoolList.value.filter((item) => item.id !== row.id);
  outPoolApi.grid.reloadData(outPoolList.value);
  const $grid = poolApi.grid;
  if ($grid) {
    const tableData = $grid.getTableData().fullData;
    const targetRow = tableData.find((item) => item.id === row.id);
    if (targetRow) {
      $grid.setCheckboxRow(targetRow, false);
    }
  }
  baseFormInfo.outputAmount = baseFormInfo.outputAmount - row.receivableAmount;
  baseFormInfo.poolTotalAmount = Number(baseFormInfo.poolTotalAmount) + Number(row.receivableAmount);
};
const handleCompanyChange = (value: any) => {
  baseFormInfo.companyList = [];
  baseFormInfo.companyList?.push({
    companyName: value.label,
    companyCode: value.key,
  });
};
watch([() => baseFormInfo.poolTotalAmount], ([poolTotalAmount]) => {
  if (poolTotalAmount !== undefined) {
    baseFormInfo.poolFundRatio = (baseFormInfo.currentPoolFund / baseFormInfo.poolTotalAmount) * 100;
  }
});
const colSpan = COL_SPAN_PROP;
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="置换" @register="registerPopup">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <Form ref="formRef" :model="baseFormInfo" v-bind="formProp" class="px-8">
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="池保理融资企业" name="companyList">
            <ApiComponent
              disabled
              v-model="companyInfo"
              :component="Select"
              label-in-value
              :api="getCompanyListApi"
              label-field="companyName"
              @change="handleCompanyChange"
              value-field="companyCode"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款池名称" name="poolName">
            <Input disabled v-model:value="baseFormInfo.poolName" placeholder="请输入应收账款池名称" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="池规则" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="折扣率（%）" name="discountRate">
            <InputNumber
              style="width: 100%"
              disabled
              v-model:value="baseFormInfo.discountRate"
              placeholder="请输入折扣率"
              :min="0"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="“水位线”金额（元）" name="thresholdAmount">
            <InputNumber
              style="width: 100%"
              disabled
              v-model:value="baseFormInfo.thresholdAmount"
              placeholder="请输入“水位线”金额"
              :min="0"
              :precision="2"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="当前保理融资金额（元）" name="currentPoolFund">
            <InputNumber
              style="width: 100%"
              disabled
              v-model:value="baseFormInfo.currentPoolFund"
              :min="0"
              :precision="2"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="当前保理融资比例（%）" name="currentPoolFundRatio">
            <InputNumber style="width: 100%" disabled v-model:value="baseFormInfo.currentPoolFundRatio" :min="0" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="池内资产金额范围（元）">
            <InputGroup compact>
              <InputNumber
                disabled
                :precision="2"
                v-model:value="baseFormInfo.poolAssetsMin"
                style="width: 30%; text-align: center"
                placeholder="最小值"
              />
              <Input
                class="site-input-split"
                style="width: 30px; pointer-events: none; border-right: 0; border-left: 0"
                placeholder="~"
                disabled
              />
              <InputNumber
                disabled
                v-model:value="baseFormInfo.poolAssetsMax"
                class="site-input-right"
                :precision="2"
                style="width: 30%; text-align: center"
                placeholder="最大值"
              />
            </InputGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="入池资产有效期范围（天）">
            <InputGroup compact>
              <InputNumber
                disabled
                v-model:value="baseFormInfo.poolAssetsValidMin"
                style="width: 30%; text-align: center"
                placeholder="请输入"
                :precision="0"
              />
              <Input
                class="site-input-split"
                style="width: 30px; pointer-events: none; border-right: 0; border-left: 0"
                placeholder="~"
                disabled
              />
              <InputNumber
                disabled
                v-model:value="baseFormInfo.poolAssetsValidMax"
                class="site-input-right"
                style="width: 30%; text-align: center"
                placeholder="请输入"
                :precision="0"
              />
            </InputGroup>
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="置换信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="本次入池总金额（元）">
            <InputNumber
              style="width: 100%"
              disabled
              v-model:value="baseFormInfo.inputAmount"
              :min="0"
              :precision="2"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="本次出池总金额（元）">
            <InputNumber
              style="width: 100%"
              disabled
              v-model:value="baseFormInfo.outputAmount"
              :min="0"
              :precision="2"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="置换后池资产总金额（元）">
            <InputNumber
              style="width: 100%"
              disabled
              v-model:value="baseFormInfo.poolTotalAmount"
              :min="0"
              :precision="2"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="置换后保理融资比例（%）">
            <InputNumber
              style="width: 100%"
              disabled
              :precision="2"
              v-model:value="baseFormInfo.poolFundRatio"
              :min="0"
            />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="应收账款池" />
      <PoolTable>
        <template #toolbarActions>
          <!-- <TypographyText>
            应收账款【<b>{{ poolCount }}</b> 】笔，总金额 <b>{{ totalAmount }}</b>
            元
          </TypographyText> -->
        </template>
        <template #toolbar-tools>
          <Button type="primary" class="mr-2" @click="() => replaceThePool()">置换出池</Button>
        </template>
        <!-- 操作按钮 -->
        <template #action="{ row }">
          <TypographyLink @click="detailReceivable(row)"> 详情</TypographyLink>
          <!-- <TypographyLink type="danger" @click="removeReceivable(rowIndex)"> 删除</TypographyLink> -->
        </template>
      </PoolTable>
      <BasicCaption content="置换出池资产" />
      <OutPoolTable>
        <template #action="{ row }">
          <TypographyLink @click="detailReceivable(row)"> 详情</TypographyLink>
          <TypographyLink type="danger" @click="removeOutRow(row)"> 删除</TypographyLink>
        </template>
      </OutPoolTable>
      <BasicCaption content="置换入池资产" />
      <InPoolTable>
        <template #toolbar-tools>
          <Button type="primary" class="mr-2" @click="() => openReceivableModel()">选择应收账款</Button>
        </template>
        <template #action="{ rowIndex, row }">
          <TypographyLink @click="detailReceivable(row)"> 详情</TypographyLink>
          <TypographyLink type="danger" @click="removeReceivable(row, rowIndex)"> 删除</TypographyLink>
        </template>
      </InPoolTable>
    </Form>
    <ReceivableModal
      v-model:is-visible="modalVisible"
      :pool-assets-min="baseFormInfo.poolAssetsMin"
      :pool-assets-max="baseFormInfo.poolAssetsMax"
      :pool-assets-valid-min="baseFormInfo.poolAssetsValidMin"
      :pool-assets-valid-max="baseFormInfo.poolAssetsValidMax"
      @confirm="handleReceivableConfirm"
    />
  </BasicPopup>
  <Detail @register="detailForm" />
</template>

<style scoped>
.site-input-split {
  background-color: #fff;
}

.site-input-right {
  border-left-width: 0;
}

[data-theme='dark'] .site-input-split {
  background-color: transparent;
}
</style>
