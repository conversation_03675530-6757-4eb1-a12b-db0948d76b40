<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { watch } from 'vue';

import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

const props = defineProps({ receivableForm: { type: Object, required: true } });

const GridOptions = {
  columns: [
    { field: 'receivableName', title: '应收账款名称', width: 180 },
    {
      field: 'assetClassification',
      title: '资产五级分类',
      minWidth: 150,
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_ASSET_CLASSIFY_TYPE',
        },
      },
    },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'receivableAmount', title: '应收账款金额（元）', formatter: 'formatMoney' },
    { field: 'receivableTerm', title: '应收账款期限（月）' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;

const [Grid, GridApi] = useVbenVxeGrid({ gridOptions: GridOptions });

watch(
  () => props.receivableForm,
  (val = {}) => {
    GridApi.grid.reloadData(val.receivableList);
  },
  { deep: true },
);
</script>

<template>
  <div>
    <BasicCaption content="保理应收账款明细" />
    <Grid />
  </div>
</template>
