<script setup lang="ts">
import type { AssetPoolInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { formatDate, formatMoney } from '@vben/utils';

import { getAssetPoolInfoApi } from '#/api';
import AssetDetail from '#/views/asset-securities/asset-pool/components/basic-asset-detail.vue';

defineEmits(['register']);

// 详情页样式配置
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

// 初始化数据
const init = async (data: AssetPoolInfo) => {
  // 获取详情数据，如果有ID则从接口获取，否则使用传入的数据
  const info = data.id ? await getAssetPoolInfoApi({ id: data.id }) : data;
  poolForm.value = { ...poolForm.value, ...info };
};

const poolForm = ref<AssetPoolInfo>({});
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="ABS资产池信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="ABS资产池名称">
          {{ poolForm.assetPoolName }}
        </a-descriptions-item>

        <a-descriptions-item label="ABS资产池编号">
          {{ poolForm.assetPoolCode }}
        </a-descriptions-item>

        <a-descriptions-item label="关联ABS项目">
          {{ poolForm.absProjectName }}
        </a-descriptions-item>

        <a-descriptions-item label="基础资产池规模（元）">
          {{ formatMoney(poolForm.initPoolAmount) }}
        </a-descriptions-item>

        <a-descriptions-item label="资产池成立日期">
          {{ formatDate(poolForm.initPoolDate) }}
        </a-descriptions-item>

        <a-descriptions-item label="证券发行日期">
          {{ formatDate(poolForm.securityIssueDate) }}
        </a-descriptions-item>

        <a-descriptions-item label="预期到期日">
          {{ formatDate(poolForm.expectDueDate) }}
        </a-descriptions-item>

        <a-descriptions-item label="证券到期日期">
          {{ formatDate(poolForm.securityDueDate) }}
        </a-descriptions-item>
      </a-descriptions>

      <AssetDetail :info="poolForm" />
    </div>
  </BasicPopup>
</template>

<style></style>
