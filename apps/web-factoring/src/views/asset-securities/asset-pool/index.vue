<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AssetPoolInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';
import { cloneDeep, isEmpty } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  delAssetPoolApi,
  getAssetPoolInfoApi,
  getAssetPoolPageListApi,
  inputAssetPoolApi,
  poolPackagedApi,
} from '#/api';

import AssetPoolDetail from './asset-pool-detail.vue';
import AssetPoolEdit from './asset-pool-edit.vue';
import BasicAssetPopup from './components/basic-asset-popup.vue';

const dictStore = useDictStore();

// 搜索表单配置（按原型字段调整）
const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'assetPoolName', label: 'ABS资产池名称' },
    { component: 'Input', fieldName: 'absProjectName', label: 'ABS项目名称' },
    {
      component: 'Select',
      fieldName: 'statusList',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'packageStatusList',
      label: '封包状态',
      componentProps: {
        options: dictStore.getDictList('FCT_ABS_POOL_PACKAGE_STATUS'),
      },
    },
  ],
  commonConfig: { labelCol: { span: 6 }, wrapperCol: { span: 18 } },
});

// 表格列配置（按原型顺序 & 接口字段映射）
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'assetPoolName', title: 'ABS资产池名称', minWidth: 200 },
    { field: 'absProjectName', title: 'ABS项目名称', minWidth: 200 },
    { field: 'assetAmount', title: '包内资产总额（元）', minWidth: 150, formatter: 'formatMoney' },
    { field: 'assetCount', title: '包内资产笔数', minWidth: 150 },
    { field: 'packageDate', title: '封包日期', minWidth: 150, formatter: 'formatDate' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_STATUS' } },
      minWidth: 120,
    },
    {
      field: 'packageStatus',
      title: '封包状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_ABS_POOL_PACKAGE_STATUS' } },
      minWidth: 120,
    },
    { field: 'action', title: '操作', fixed: 'right', width: 180, slots: { default: 'action' } },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) =>
        await getAssetPoolPageListApi({ current: page.currentPage, size: page.pageSize, ...formValues }),
    },
  },
  checkboxConfig: { showHeader: false },
  toolbarConfig: { slots: { tools: 'toolbar-tools' } },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();

// 新增
const add = () => openFormPopup(true, {});
// 编辑
const edit = (row: AssetPoolInfo) => openFormPopup(true, row);
const editSuccess = () => gridApi.reload();
// 查看详情
const viewDetail = (row: AssetPoolInfo) => openDetailPopup(true, row);
// 删除
const del = (row: AssetPoolInfo) => {
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该ABS资产池，是否继续？',
    async onOk() {
      await delAssetPoolApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const inputForm = ref({});
const input = (row: AssetPoolInfo) => {
  modalApi.open();
  inputForm.value.poolId = row.id;
};
const [Modal, modalApi] = useVbenModal({
  connectedComponent: BasicAssetPopup,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      const selectedAccounts = modalApi.getData() || [];
      if (isEmpty(selectedAccounts)) {
        return;
      }
      inputForm.value.assetIds = selectedAccounts.map((item: object) => item.id);
      await inputAssetPoolApi(inputForm.value);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    }
  },
});
const outForm = ref({});
const outGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'absAssetName', title: '基础资产名称', minWidth: 200 },
    { field: 'factoringContractCode', title: '合同号', minWidth: 180 },
    { field: 'receivableAmount', title: '应收账款金额（元）', minWidth: 150, formatter: 'formatMoney' },
    { field: 'receivableAgeDays', title: '应收账款账龄（天）', minWidth: 150 },
    {
      field: 'assetClassification',
      title: '资产五级分类',
      minWidth: 150,
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_ASSET_CLASSIFY_TYPE',
        },
      },
    },
    { field: 'debtorName', title: '债务人', minWidth: 200 },
    { field: 'creditorName', title: '债权人', minWidth: 200 },
    { field: 'financingAmount', title: '融资金额（元）', minWidth: 150, formatter: 'formatMoney' },
    { field: 'financingRate', title: '融资利率（%）', minWidth: 120 },
    { field: 'remainingDays', title: '剩余期限（天）', minWidth: 120 },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [OutGrid, outGridApi] = useVbenVxeGrid({
  gridOptions: outGridOptions,
});
const out = async (row: AssetPoolInfo) => {
  outModalApi.open();
  outForm.value.poolId = row.id;
  const data = await getAssetPoolInfoApi({ id: row.id });
  outForm.value.allAbsAssetList = cloneDeep(data.absAssetList);
  outGridApi.grid.reloadData(data.absAssetList);
};
const [outModal, outModalApi] = useVbenModal({
  onConfirm: async () => {
    const selectedRows = outGridApi.grid.getCheckboxRecords();
    if (isEmpty(selectedRows)) {
      return message.error('请选择数据');
    }
    outForm.value.assetIds = selectedRows.map((item: object) => item.id);
    await inputAssetPoolApi(outForm.value);
    message.success($t('base.resSuccess'));
    await gridApi.reload();
    await outModalApi.close();
  },
  onClosed: () => {
    outForm.value = {};
  },
});
const packaged = async (row: AssetPoolInfo) => {
  await poolPackagedApi(row.id as number);
  message.success($t('base.resSuccess'));
  await gridApi.reload();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add">{{ $t('base.add') }}</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">{{ $t('base.detail') }}</a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
          <a-typography-link
            v-if="['EFFECTIVE'].includes(row.status) && ['un_packaged'].includes(row.packageStatus)"
            @click="input(row)"
          >
            入池
          </a-typography-link>
          <a-typography-link
            v-if="['EFFECTIVE'].includes(row.status) && ['un_packaged'].includes(row.packageStatus)"
            @click="out(row)"
          >
            出池
          </a-typography-link>
          <a-typography-link
            v-if="['EFFECTIVE'].includes(row.status) && ['un_packaged'].includes(row.packageStatus)"
            @click="packaged(row)"
          >
            封包
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <AssetPoolEdit @register="registerForm" @ok="editSuccess" />
    <AssetPoolDetail @register="registerDetail" />
    <Modal title="基础资产入池" />
    <outModal title="基础资产出池" class="w-[800px]"> <OutGrid /></outModal>
  </Page>
</template>
