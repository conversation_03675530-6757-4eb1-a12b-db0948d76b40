<script setup lang="ts">
import type { AbsProjectInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { formatDate, formatMoney } from '@vben/utils';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { infoAbsProjectApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const { WorkflowPreviewModal, useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading } =
  useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');

const dictStore = useDictStore();
// 描述组件配置（调整标签宽度适配布局）
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: { width: '180px', justifyContent: 'flex-end' },
};

// 详情数据存储
const projectForm = ref<AbsProjectInfo>({} as AbsProjectInfo);

// 初始化详情数据（调用接口 + 处理附件）
const init = async (data: AbsProjectInfo) => {
  if (data.id) {
    pageType.value = data.pageType ?? 'detail';
    await initWorkflow({ formKey: 'fct_abs_project', businessKey: data.id });
    projectForm.value = await infoAbsProjectApi({ id: data.id as number });
  }
};

const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="ABS项目详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <!-- 基本信息区 -->
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="ABS项目名称">
          {{ projectForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="ABS项目编号">
          {{ projectForm.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="立项日期">
          {{ formatDate(projectForm.establishDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="项目负责人">
          {{ projectForm.projectManagerName }}
        </a-descriptions-item>
        <a-descriptions-item label="ABS项目简介" :span="3">
          {{ projectForm.absProjectSummary }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 参与主体区 -->
      <BasicCaption content="参与主体" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="原始受益人">
          {{ projectForm.originalBeneficiaryName }}
        </a-descriptions-item>
        <a-descriptions-item label="担保人">
          {{ projectForm.guaranteeName }}
        </a-descriptions-item>
        <a-descriptions-item label="计划管理人">
          {{ projectForm.planManager }}
        </a-descriptions-item>
        <a-descriptions-item label="托管银行">
          {{ projectForm.custodianBank }}
        </a-descriptions-item>
        <a-descriptions-item label="评级机构">
          {{ projectForm.ratingAgency }}
        </a-descriptions-item>
        <a-descriptions-item label="律师事务所">
          {{ projectForm.lawFirm }}
        </a-descriptions-item>
        <a-descriptions-item label="会计师事务所">
          {{ projectForm.accountingFirm }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 产品要素区 -->
      <BasicCaption content="应收账款ABS产品要素" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="基础资产">
          {{ projectForm.baseAsset }}
        </a-descriptions-item>
        <a-descriptions-item label="融资期限（年）">
          {{ projectForm.financeTermYear }}
        </a-descriptions-item>
        <a-descriptions-item label="申报融资规模（元）">
          {{ formatMoney(projectForm.declareFinancingScale) }}
        </a-descriptions-item>
        <a-descriptions-item label="产品分层">
          {{ dictStore.formatter(projectForm.productLayer, 'FCT_ABS_PROJECT_PRODUCT_LAYER') }}
        </a-descriptions-item>
        <a-descriptions-item label="还本方式">
          {{ dictStore.formatter(projectForm.returnMethod, 'FCT_ABS_PROJECT_RETURN_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="还息方式">
          {{ dictStore.formatter(projectForm.returnInterestMethod, 'FCT_ABS_PROJECT_INTEREST_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="申报场所">
          {{ projectForm.declarationPlace }}
        </a-descriptions-item>
        <a-descriptions-item label="增信措施">
          {{ projectForm.creditEnhancementMethod }}
        </a-descriptions-item>
        <a-descriptions-item label="募集资金用途" :span="3">
          {{ projectForm.declarationPurpose }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 合规性审查区 -->
      <BasicCaption content="项目合规性审查" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="法律法规依据" :span="3">
          {{ projectForm.legalBasis }}
        </a-descriptions-item>
        <a-descriptions-item label="审批备案情况" :span="3">
          {{ projectForm.approvalFilingSummary }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 风险评审区 -->
      <BasicCaption content="项目风险评审" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="信用风险评估" :span="3">
          {{ projectForm.creditRiskSummary }}
        </a-descriptions-item>
        <a-descriptions-item label="市场风险评估" :span="3">
          {{ projectForm.marketRiskSummary }}
        </a-descriptions-item>
        <a-descriptions-item label="操作风险评估" :span="3">
          {{ projectForm.operationalRiskSummary }}
        </a-descriptions-item>
      </a-descriptions>

      <BaseAttachmentList :business-id="projectForm.id" business-type="FCT_ABS_PROJECT" />
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
