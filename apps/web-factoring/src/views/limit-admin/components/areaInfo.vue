<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeGridProps } from '@vben/plugins/vxe-tablele';

import type { areaComputeVo, limitRuleVo } from '#/api';

import { ref } from 'vue';

import { COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';

import { Col, Form, FormItem, Input, InputNumber, Row, Select } from 'ant-design-vue';

import { BaseRegionPicker } from '#/adapter/base-ui';

import { spanMethod } from './tableSpanUtils';

const { isAgainCompute } = defineProps({
  isAgainCompute: {
    type: Boolean,
    default: false,
  },
});
const dictStore = useDictStore();

const colSpan = COL_SPAN_PROP;
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const formRef = ref();

const baseFormInfo = defineModel<areaComputeVo>({ type: Object, required: true });

const gridOptions: VxeGridProps<limitRuleVo> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'quotaCategory',
      title: '指标分类',
      width: 140,
    },
    {
      field: 'quotaName',
      title: '指标名称',
      width: '43%',
    },
    {
      field: 'contractType',
      title: '数值',
      slots: { default: 'contractType-select' },
      width: '25%',
    },
    {
      field: 'remark',
      title: '备注',
      slots: { default: 'remark' },
    },
  ],
  data: baseFormInfo.value.limitRuleList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  spanMethod: (params: any) => spanMethod(params, 'quotaCategory'),
};
const validateForm = async () => {
  try {
    await formRef.value.validate();
    return true;
  } catch {
    return false;
  }
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const rules: Record<string, Rule[]> = {
  districtCode: [{ required: true, message: '请输入地市区县', trigger: 'change' }],
  companyRating: [{ required: true, message: '请输入区域内最高企业评级' }],
  budgetAmount: [{ required: true, message: '请输入地方一般公共预算收入', trigger: 'change' }],
};
defineExpose({
  gridApi,
  validateForm,
});
</script>

<template>
  <Form ref="formRef" :colon="false" :model="baseFormInfo" :rules="rules" v-bind="formProp" class="px-8">
    <Row class="mt-5">
      <Col v-bind="colSpan">
        <FormItem label="地市区县" name="districtCode">
          <BaseRegionPicker
            :disabled="isAgainCompute"
            v-model:province="baseFormInfo.province"
            v-model:city="baseFormInfo.cityName"
            v-model:district="baseFormInfo.districtName"
            v-model:district-code="baseFormInfo.districtCode"
            placeholder="请选择地区"
          />
        </FormItem>
      </Col>
      <Col v-bind="colSpan">
        <FormItem label="地方一般公共预算收入（元）" name="budgetAmount">
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-model:value="baseFormInfo.budgetAmount"
            placeholder="请输入地方一般公共预算收入"
          />
        </FormItem>
      </Col>
      <Col v-bind="colSpan">
        <FormItem label="区域内最高企业评级" name="companyRating">
          <Select
            style="width: 100%"
            v-model:value="baseFormInfo.companyRating"
            :options="dictStore.getDictList('COMPANY_RATING')"
            placeholder="请选择区域内最高企业评级"
          />
        </FormItem>
      </Col>
    </Row>
    <BasicCaption content="区域保理额度测算" />
    <Grid>
      <!-- 评级赋分下拉框 -->
      <template #contractType-select="{ row }">
        <Input v-if="row.dictType === 'input'" v-model:value="row.numericalValue" placeholder="请输入" />
        <InputNumber
          class="w-full"
          :controls="false"
          :precision="2"
          v-if="row.dictType === 'number'"
          v-model:value="row.numericalValue"
          placeholder="请输入数值"
        />
        <InputNumber
          class="w-full"
          :controls="false"
          v-if="row.dictType === 'number-10'"
          max="10"
          min="0"
          v-model:value="row.numericalValue"
          placeholder="请输入0～10的数值"
        />
        <Select
          v-if="row.dictType !== 'input' && row.dictType !== 'number'"
          style="width: 100%"
          v-model:value="row.numericalValue"
          :options="dictStore.getDictList(row.dictType)"
          :placeholder="`请选择${row.quotaName}`"
        />
      </template>
      <!-- 备注 -->
      <template #remark="{ row }">
        <div>
          <Input v-model:value="row.remark" placeholder="请输入备注" />
        </div>
      </template>
    </Grid>
  </Form>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
