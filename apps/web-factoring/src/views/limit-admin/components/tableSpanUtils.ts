export const spanMethod = ({ row, column, data, rowIndex }: any, mergeColName: string) => {
  if (column.field === mergeColName) {
    const prevRow = data[rowIndex - 1];
    if (prevRow && prevRow[mergeColName] === row[mergeColName]) {
      return { rowspan: 0, colspan: 0 };
    } else {
      let rowspan = 1;
      while (rowIndex + rowspan < data.length && data[rowIndex + rowspan][mergeColName] === row[mergeColName]) {
        rowspan++;
      }
      return { rowspan, colspan: 1 };
    }
  }
};
