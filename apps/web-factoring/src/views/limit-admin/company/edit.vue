<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { companyComputeVo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatMoney } from '@vben/utils';

import { Button, Col, Form, FormItem, Input, InputNumber, Row, Select } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import { spanMethod } from '../components/tableSpanUtils';

const emit = defineEmits(['register', 'ok']);
const { WorkflowPreviewModal, startWorkflow, initWorkflow } = useWorkflowBase();
const dictStore = useDictStore();

const baseFormInfo = reactive<companyComputeVo>({
  cityCode: '',
  cityName: '',
  companyCode: '',
  companyName: '',
  companyRating: '',
  companyType: '',
  districtCode: '',
  districtName: '',
  isSubmit: false,
  marketAdjustFactor: 0,
  marketBasicAmount: 0,
  marketCreditAmount: 0,
  marketCreditScore: 0,
  marketDepreciation: 0,
  marketNetProfit: 0,
  operatingAdjustFactor: 0,
  operatingBasicAmount: 0,
  operatingCreditAmount: 0,
  operatingCreditScore: 0,
  operatingLimitRuleList: [
    {
      quotaCategory: '区域情况',
      quotaName: '人口（万人）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政平衡率(%)',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: 'GDP增速（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政实力(%)',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '区域利差',
      dictType: 'FCT_LIMIT_INTEREST_RATE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '所有者权益规模（集团）（元）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '资产负债率（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '短期债务/总债务（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: 'EBIT/债务利息',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '财务报表可靠性',
      dictType: 'FCT_LIMIT_FINANCIAL_STATEMENT',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '主体评级',
      dictType: 'FCT_LIMIT_RATING',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '公司治理：按提供相关制度计算',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '业务经营质量（净资产收益率）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '业务多样性：包括主营业务、第二增长曲线、财务投资、特许经营权等',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '经营稳定性',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '配合度：配合积极及时，材料提供完整',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '信用记录',
      dictType: 'FCT_LIMIT_CREDIT_HISTORY',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '及时偿还行为',
      dictType: 'FCT_LIMIT_REPAYMENT',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '综合成本接受度',
      quotaName: '成本接受',
      dictType: 'FCT_LIMIT_COST',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '舆情',
      quotaName: '负面舆情：授信对象出现负面舆情,如债务违约，或者高管违法违纪等',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
  regionBudgetAmount: 0,
  regionCreditAmount: 0,
  regionCreditRatio: 0,
  regionCreditScore: 0,
  regionLimitRuleList: [],
  marketLimitRuleList: [],
  adjustSupportingFileId: 0,
  budgetAmount: 0,
  creditAmount: 0,
  creditRatio: 0,
  creditScore: 0,
  limitRuleList: [
    {
      quotaCategory: '区域情况',
      quotaName: '人口（万人）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政平衡率（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: 'GDP增速（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政实力（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '区域利差',
      dictType: 'FCT_LIMIT_INTEREST_RATE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '过往合作履约情况',
      quotaName: '过往合作履约情况',
      dictType: 'FCT_LIMIT_PREVIOUS',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '负面舆情',
      quotaName: '负面舆情：区域内出现国有发债主体的债券违约事件或者频繁的非标融资、银行借款等债务逾期风险事件',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
  limitLogList: [],
});

const title = computed(() => {
  return baseFormInfo.id ? '编辑' : '新增';
});
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const handleSave = async (isSubmit = false) => {
  try {
    const TableData = gridApi.grid?.getTableData();
    baseFormInfo.operatingLimitRuleList = TableData.fullData;
    // await formRef.value.validate();
    const params = cloneDeep(baseFormInfo);
    params.regionLimitRuleList = baseFormInfo.limitRuleList;
    params.regionBudgetAmount = baseFormInfo.budgetAmount;
    params.regionCreditAmount = baseFormInfo.creditAmount;
    params.regionCreditRatio = baseFormInfo.creditRatio;
    params.regionCreditScore = baseFormInfo.creditScore;

    if (isSubmit) {
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      params.processDefinitionKey = processDefinitionKey;
      params.startUserSelectAssignees = startUserSelectAssignees;
      params.isSubmit = true;
    }
    emit('ok', params);
  } catch (error) {
    console.error(`${isSubmit ? '提交' : '保存'}失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const save = async () => {
  handleSave(false);
};

const submit = async () => {
  handleSave(true);
};

const isAgainCompute = ref(false);
// 添加初始化方法
const init = async (data: companyComputeVo) => {
  await initWorkflow({ formKey: 'fct_operation_company_limit', businessKey: data.id });
  if (data && Object.keys(data).length > 0) {
    if (data.status === 'EFFECTIVE') {
      isAgainCompute.value = true;
    }
    if (data.limitRuleList?.length > 0) {
      data.limitRuleList = data.limitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.limitRuleList[index].dictType,
        };
      });
      gridAreaApi.grid?.reloadData(data.limitRuleList);
    }
    if (data.operatingLimitRuleList?.length > 0) {
      data.operatingLimitRuleList = data.operatingLimitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.operatingLimitRuleList[index].dictType,
        };
      });
      gridApi.grid?.reloadData(data.operatingLimitRuleList);
    }
    Object.assign(baseFormInfo, cloneDeep(data));
  } else {
    Object.assign(baseFormInfo, {
      cityCode: '',
      cityName: '',
      companyCode: '',
      companyName: '',
      companyRating: '',
      companyType: '',
      districtCode: '',
      districtName: '',
      isSubmit: false,
      marketAdjustFactor: 0,
      marketBasicAmount: 0,
      marketCreditAmount: 0,
      marketCreditScore: 0,
      marketDepreciation: 0,
      marketNetProfit: 0,
      operatingAdjustFactor: 0,
      operatingBasicAmount: 0,
      operatingCreditAmount: 0,
      operatingCreditScore: 0,
      operatingLimitRuleList: [
        {
          quotaCategory: '区域情况',
          quotaName: '人口（万人）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: '财政平衡率(%)',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: 'GDP增速（%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: '财政实力(%)',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: '区域利差',
          dictType: 'FCT_LIMIT_INTEREST_RATE',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: '所有者权益规模（集团）（元）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: '资产负债率（%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: '短期债务/总债务（%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: 'EBIT/债务利息',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: '财务报表可靠性',
          dictType: 'FCT_LIMIT_FINANCIAL_STATEMENT',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '主体评级',
          dictType: 'FCT_LIMIT_RATING',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '公司治理：按提供相关制度计算',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '业务经营质量（净资产收益率）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '业务多样性：包括主营业务、第二增长曲线、财务投资、特许经营权等',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '经营稳定性',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '履约情况',
          quotaName: '配合度：配合积极及时，材料提供完整',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '履约情况',
          quotaName: '信用记录',
          dictType: 'FCT_LIMIT_CREDIT_HISTORY',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '履约情况',
          quotaName: '及时偿还行为',
          dictType: 'FCT_LIMIT_REPAYMENT',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '综合成本接受度',
          quotaName: '成本接受',
          dictType: 'FCT_LIMIT_COST',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '舆情',
          quotaName: '负面舆情：授信对象出现负面舆情,如债务违约，或者高管违法违纪等',
          dictType: 'FCT_LIMIT_SENTIMENT',
          numericalValue: '',
          remark: '',
        },
      ],
      regionBudgetAmount: 0,
      regionCreditAmount: 0,
      regionCreditRatio: 0,
      regionCreditScore: 0,
      regionLimitRuleList: [],
      marketLimitRuleList: [],
    });
  }
};
const gridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'quotaCategory',
      title: '指标分类',
      width: 160,
    },
    {
      field: 'quotaName',
      title: '指标名称',
      width: '30%',
    },
    {
      field: 'numericalValue',
      title: '数值',
      slots: { default: 'numericalValue' },
      width: '35%',
    },
    {
      field: 'remark',
      title: '备注',
      slots: { default: 'remark' },
    },
  ],
  data: baseFormInfo.operatingLimitRuleList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  spanMethod: (params: any) => spanMethod(params, 'quotaCategory'),
};
const colSpan = { md: 12, sm: 24 };
const labelCol = { style: { width: '150px' } };
const AreaGridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'quotaCategory',
      title: '指标分类',
      width: 140,
    },
    {
      field: 'quotaName',
      title: '指标名称',
      width: '43%',
    },
    {
      field: 'contractType',
      title: '数值',
      slots: { default: 'contractType-select' },
      width: '25%',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: baseFormInfo.limitRuleList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  spanMethod: (params: any) => spanMethod(params, 'quotaCategory'),
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const [AreaGrid, gridAreaApi] = useVbenVxeGrid({ gridOptions: AreaGridOptions });

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const rules: Record<string, Rule[]> = {
  operatingBasicAmount: [{ required: true, message: '请输入额度计算基数' }],
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :show-ok-btn="!isAgainCompute" :title="title" @register="registerPopup" @ok="save">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <Form :model="baseFormInfo" :label-col="labelCol" :wrapper-col="{ span: 20 }" class="px-8" :rules="rules">
      <div :class="BASE_PAGE_CLASS_NAME">
        <a-descriptions class="mt-4" v-bind="descriptionsProp">
          <a-descriptions-item label="企业名称">
            {{ baseFormInfo.companyName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="统一社会信用代码">
            {{ baseFormInfo.companyCode || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="客户类别">
            {{ dictStore.formatter(baseFormInfo.companyType, 'FCT_COMPANY_LIMIT_COMPANY_TYPE') }}
          </a-descriptions-item>
          <a-descriptions-item label="企业评级">
            {{ dictStore.formatter(baseFormInfo.companyRating, 'COMPANY_RATING') }}
          </a-descriptions-item>
          <a-descriptions-item label="地市区县">
            {{ baseFormInfo.cityName || '-' }}/{{ baseFormInfo.districtName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="地方一般公共预算收入（元）">
            {{ formatMoney(baseFormInfo.budgetAmount) || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <Row class="mt-5">
          <Col v-bind="colSpan">
            <FormItem label="额度计算基数（元）" name="operatingBasicAmount">
              <InputNumber
                class="w-full"
                :controls="false"
                v-model:value="baseFormInfo.operatingBasicAmount"
                placeholder="请输入"
                :precision="2"
              />
            </FormItem>
          </Col>
        </Row>
        <BasicCaption content="区域保理额度测算" />
        <AreaGrid>
          <!-- 评级赋分下拉框 -->
          <template #contractType-select="{ row }">
            <TypographyText v-if="row.dictType !== 'input' && row.dictType !== 'number'">
              {{ dictStore.formatter(row.numericalValue, row.dictType) }}
            </TypographyText>
            <TypographyText v-else v-model:value="row.numericalValue">
              {{ row.numericalValue }}
            </TypographyText>
          </template>
        </AreaGrid>
        <BasicCaption content="地区额度测算结果" />
        <a-descriptions class="mt-4" v-bind="descriptionsProp">
          <a-descriptions-item label="地区保理额度测算得分">
            {{ formatMoney(baseFormInfo.creditScore) || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="区域授信比例（%）">
            {{ formatMoney(baseFormInfo.creditRatio) || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="地区保理授信额度（元）">
            {{ formatMoney(baseFormInfo.creditAmount) || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <BasicCaption content="区域经营性客户额度测算" />
        <Grid>
          <!-- 评级赋分下拉框 -->
          <template #numericalValue="{ row }">
            <Input v-if="row.dictType === 'input'" v-model:value="row.numericalValue" placeholder="请输入" />
            <InputNumber
              class="w-full"
              :controls="false"
              v-if="row.dictType === 'number-10'"
              max="10"
              min="0"
              v-model:value="row.numericalValue"
              placeholder="请输入0～10的数值"
            />
            <InputNumber
              class="w-full"
              :controls="false"
              :precision="2"
              v-if="row.dictType === 'number'"
              v-model:value="row.numericalValue"
              placeholder="请输入数值"
            />
            <Select
              v-if="row.dictType !== 'input' && row.dictType !== 'number'"
              style="width: 100%"
              v-model:value="row.numericalValue"
              :options="dictStore.getDictList(row.dictType)"
              :placeholder="`请选择${row.quotaName}`"
            />
          </template>
          <!-- 备注 -->
          <template #remark="{ row }">
            <div>
              <Input v-model:value="row.remark" placeholder="请输入备注" />
            </div>
          </template>
        </Grid>
      </div>
    </Form>
    <template>
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;
}
</style>
