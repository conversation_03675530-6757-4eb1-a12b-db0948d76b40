<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { companyComputeVo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { Button, Col, Form, FormItem, Input, InputNumber, Row, Select } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import { spanMethod } from '../components/tableSpanUtils';

const emit = defineEmits(['register', 'ok']);
const dictStore = useDictStore();
const { WorkflowPreviewModal, startWorkflow, initWorkflow } = useWorkflowBase();

const baseFormInfo = reactive<companyComputeVo>({
  cityCode: '',
  cityName: '',
  companyCode: '',
  companyName: '',
  companyRating: '',
  companyType: '',
  districtCode: '',
  districtName: '',
  isSubmit: false,
  marketAdjustFactor: 0,
  marketBasicAmount: 0,
  marketCreditAmount: 0,
  marketCreditScore: 0,
  marketDepreciation: undefined,
  marketNetProfit: undefined,
  operatingAdjustFactor: 0,
  operatingBasicAmount: 0,
  operatingCreditAmount: 0,
  operatingCreditScore: 0,
  operatingLimitRuleList: [],
  regionBudgetAmount: 0,
  regionCreditAmount: 0,
  regionCreditRatio: 0,
  regionCreditScore: 0,
  regionLimitRuleList: [],
  marketLimitRuleList: [
    {
      quotaCategory: '主体类型',
      quotaName: '公司类型',
      dictType: 'FCT_LIMIT_COMPANY_TYPE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '主体类型',
      quotaName: '所在区域',
      dictType: 'FCT_LIMIT_REGION',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '所有者权益规模（集团）（元）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '资产负债率（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '短期债务/总债务（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: 'EBIT/债务利息',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '财务报表可靠性',
      dictType: 'FCT_LIMIT_FINANCIAL_STATEMENT',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '主体评级',
      dictType: 'FCT_LIMIT_RATING',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '公司治理：按提供相关制度计算',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '业务经营质量（净资产收益率%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '业务多样性：包括主营业务、第二增长曲线、财务投资、特许经营权等',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '经营稳定性',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '配合度：配合积极及时，材料提供完整',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '信用记录',
      dictType: 'FCT_LIMIT_CREDIT_HISTORY',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '及时偿还行为',
      dictType: 'FCT_LIMIT_REPAYMENT',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '综合成本接受度',
      quotaName: '成本接受',
      dictType: 'FCT_LIMIT_COST',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '舆情',
      quotaName: '负面舆情：授信对象出现负面舆情,如债务违约，或者高管违法违纪等',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
  adjustSupportingFileId: 0,
  budgetAmount: 0,
  creditAmount: 0,
  creditRatio: 0,
  creditScore: 0,
  limitRuleList: [],
  limitLogList: [],
});
const colSpan = { md: 12, sm: 24 };
const labelCol = { style: { width: '150px' } };

const title = computed(() => {
  return baseFormInfo.id ? '编辑' : '新增';
});
const gridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'quotaCategory',
      title: '指标分类',
      width: 160,
    },
    {
      field: 'quotaName',
      title: '指标名称',
      width: '30%',
    },
    {
      field: 'numericalValue',
      title: '数值',
      slots: { default: 'numericalValue' },
      width: '35%',
    },
    {
      field: 'remark',
      title: '备注',
      slots: { default: 'remark' },
    },
  ],
  data: baseFormInfo.marketLimitRuleList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  spanMethod: (params: any) => spanMethod(params, 'quotaCategory'),
};
const formRef = ref();
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const handleSave = async (isSubmit = false) => {
  try {
    await formRef.value.validate();
    const TableData = gridApi.grid?.getTableData();
    baseFormInfo.marketLimitRuleList = TableData.fullData;

    const params = cloneDeep(baseFormInfo);
    if (isSubmit) {
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      params.processDefinitionKey = processDefinitionKey;
      params.startUserSelectAssignees = startUserSelectAssignees;
      params.isSubmit = true;
    }
    emit('ok', params);
  } catch (error) {
    console.error(`${isSubmit ? '提交' : '保存'}失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const save = async () => {
  handleSave(false);
};

const submit = async () => {
  handleSave(true);
};

const isAgainCompute = ref(false);

// 添加初始化方法
const init = async (data: any) => {
  await initWorkflow({ formKey: 'fct_market_company_limit', businessKey: data.id });
  if (data && Object.keys(data).length > 0) {
    if (data.status === 'EFFECTIVE') {
      isAgainCompute.value = true;
    }
    if (data.marketLimitRuleList?.length > 0) {
      data.marketLimitRuleList = data.marketLimitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.marketLimitRuleList[index].dictType,
        };
      });
      gridApi.grid?.reloadData(data.marketLimitRuleList);
    }
    Object.assign(baseFormInfo, cloneDeep(data));
  } else {
    Object.assign(baseFormInfo, {
      cityCode: '',
      cityName: '',
      companyCode: '',
      companyName: '',
      companyRating: '',
      companyType: '',
      districtCode: '',
      districtName: '',
      isSubmit: false,
      marketAdjustFactor: 0,
      marketBasicAmount: 0,
      marketCreditAmount: 0,
      marketCreditScore: 0,
      marketDepreciation: undefined,
      marketNetProfit: undefined,
      operatingAdjustFactor: 0,
      operatingBasicAmount: 0,
      operatingCreditAmount: 0,
      operatingCreditScore: 0,
      operatingLimitRuleList: [],
      regionBudgetAmount: 0,
      regionCreditAmount: 0,
      regionCreditRatio: 0,
      regionCreditScore: 0,
      regionLimitRuleList: [],
      marketLimitRuleList: [
        {
          quotaCategory: '主体类型',
          quotaName: '公司类型',
          dictType: 'FCT_LIMIT_COMPANY_TYPE',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '主体类型',
          quotaName: '所在区域',
          dictType: 'FCT_LIMIT_REGION',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: '所有者权益规模（集团）（元）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: '资产负债率（%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: '短期债务/总债务（%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: 'EBIT/债务利息',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象财务指标',
          quotaName: '财务报表可靠性',
          dictType: 'FCT_LIMIT_FINANCIAL_STATEMENT',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '主体评级',
          dictType: 'FCT_LIMIT_RATING',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '公司治理：按提供相关制度计算',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '业务经营质量（净资产收益率%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '业务多样性：包括主营业务、第二增长曲线、财务投资、特许经营权等',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '授信对象非财务指标',
          quotaName: '经营稳定性',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '履约情况',
          quotaName: '配合度：配合积极及时，材料提供完整',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '履约情况',
          quotaName: '信用记录',
          dictType: 'FCT_LIMIT_CREDIT_HISTORY',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '履约情况',
          quotaName: '及时偿还行为',
          dictType: 'FCT_LIMIT_REPAYMENT',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '综合成本接受度',
          quotaName: '成本接受',
          dictType: 'FCT_LIMIT_COST',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '舆情',
          quotaName: '负面舆情：授信对象出现负面舆情,如债务违约，或者高管违法违纪等',
          dictType: 'FCT_LIMIT_SENTIMENT',
          numericalValue: '',
          remark: '',
        },
      ],
    });
  }
};
const formatDict = (val: any) => {
  // 修正字典类型为 FCT_LIMIT_COMPANY_TYPE
  return dictStore.formatter(val, 'FCT_COMPANY_LIMIT_COMPANY_TYPE');
};

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const rules: Record<string, Rule[]> = {
  marketNetProfit: [{ required: true, message: '请输入税后经营净利润（元）' }],
  marketDepreciation: [{ required: true, message: '请输入折旧与摊销' }],
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :show-ok-btn="!isAgainCompute" :title="title" @register="registerPopup" @ok="save">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="baseFormInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="企业名称">
            <Input disabled v-model:value="baseFormInfo.companyName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="统一社会信用代码">
            <Input disabled v-model:value="baseFormInfo.companyCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="客户类别">
            <Input disabled :value="formatDict(baseFormInfo.companyType)" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="税后经营净利润（元）" name="marketNetProfit">
            <InputNumber :precision="2" :controls="false" v-model:value="baseFormInfo.marketNetProfit" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="折旧与摊销（元）" name="marketDepreciation">
            <InputNumber
              :precision="2"
              :controls="false"
              v-model:value="baseFormInfo.marketDepreciation"
              class="w-full"
            />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="市场化经营客户额度测算" />
      <Grid>
        <!-- 评级赋分下拉框 -->
        <template #numericalValue="{ row }">
          <Input v-if="row.dictType === 'input'" v-model:value="row.numericalValue" placeholder="请输入" />
          <InputNumber
            class="w-full"
            :controls="false"
            v-if="row.dictType === 'number-10'"
            max="10"
            min="0"
            v-model:value="row.numericalValue"
            placeholder="请输入0～10的数值"
          />
          <InputNumber
            class="w-full"
            :controls="false"
            :precision="2"
            v-if="row.dictType === 'number'"
            v-model:value="row.numericalValue"
            placeholder="请输入数值"
          />
          <Select
            v-if="row.dictType !== 'input' && row.dictType !== 'number'"
            style="width: 100%"
            v-model:value="row.numericalValue"
            :options="dictStore.getDictList(row.dictType)"
            :placeholder="`请选择${row.quotaName}`"
          />
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <div>
            <Input v-model:value="row.remark" placeholder="请输入备注" />
          </div>
        </template>
      </Grid>
    </Form>
    <template>
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
