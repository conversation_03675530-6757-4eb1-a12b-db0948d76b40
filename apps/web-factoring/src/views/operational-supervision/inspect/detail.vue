<script setup lang="ts">
import type { CheckItem } from './components/inspect.ts';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { InspectVO } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { formatDate, formatMoney } from '@vben/utils';

import { Empty } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInspectDetailApi } from '#/api';

import FinancialReportTable from './components/FinancialReportTable.vue';
import ScoreTableDetail from './components/ScoreTableDetail.vue';

/* ------------------ 样式常量 ------------------ */
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: { width: '220px', justifyContent: 'flex-end' },
};
const quarterOptions = [
  { value: 'Q1', label: '第一季度' },
  { value: 'Q2', label: '第二季度' },
  { value: 'Q3', label: '第三季度' },
  { value: 'Q4', label: '第四季度' },
];

/* ------------------ 表单 & map 对象 ------------------ */
const reportForm = reactive<InspectVO>({} as InspectVO);

const creditorActiveKey = ref('');
const debtorActiveKey = ref('');
const guarantorActiveKey = ref('');

const creditorScoreTableDataMap = reactive<Record<string, CheckItem[]>>({});
const creditorFinanceTableDataMap = reactive<Record<string, any[]>>({});
const debtorScoreTableDataMap = reactive<Record<string, CheckItem[]>>({});
const guarantorScoreTableDataMap = reactive<Record<string, CheckItem[]>>({});
const guarantorFinanceTableDataMap = reactive<Record<string, any[]>>({});

const creditorFinancialAnalysisDesc = reactive<Record<string, string>>({});
const guarantorFinancialAnalysisDesc = reactive<Record<string, string>>({});

/* ------------------ 工具：清空+赋值 ------------------ */
function resetAssign<T extends Record<string, any>>(target: T, source: Record<string, any> = {}): void {
  Object.keys(target).forEach((k) => delete target[k]);
  Object.assign(target, source);
}

/* ------------------ 批量映射关系 ------------------ */
type MapKey =
  | 'creditorFinanceMap'
  | 'creditorFinancialAnalysisDesc'
  | 'creditorInvestmentMap'
  | 'debtorInvestmentMap'
  | 'guarantorFinanceMap'
  | 'guarantorFinancialAnalysisDesc'
  | 'guarantorInvestmentMap';

const MAP_TARGET: Record<MapKey, Record<string, any>> = {
  creditorFinanceMap: creditorFinanceTableDataMap,
  creditorInvestmentMap: creditorScoreTableDataMap,
  creditorFinancialAnalysisDesc,
  guarantorFinanceMap: guarantorFinanceTableDataMap,
  guarantorInvestmentMap: guarantorScoreTableDataMap,
  guarantorFinancialAnalysisDesc,
  debtorInvestmentMap: debtorScoreTableDataMap,
};

/* ------------------ 业绩表格 ------------------ */
const gridPerformanceTable: VxeTableGridOptions = {
  pagerConfig: { enabled: false },
  data: [],
  columns: [
    { field: 'repaymentDate', title: '还本付息日', formatter: 'formatDate' },
    { field: 'repaymentAmount', title: '应还款金额（元）', formatter: 'formatMoney' },
    { field: 'actualRepaymentDate', title: '实际回款日', formatter: 'formatDate' },
    { field: 'actualRepaymentAmount', title: '实际回款金额（元）', formatter: 'formatMoney' },
    {
      field: 'overdueStatus',
      title: '是否逾期',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_INSPECTION_OVERDUE_STATUS' } },
      width: 150,
    },
  ],
  toolbarConfig: { custom: false, refresh: false, resizable: false, zoom: false },
};
const [PerformanceTable, gridPerformanceApi] = useVbenVxeGrid({ gridOptions: gridPerformanceTable });

/* ------------------ 字典 ------------------ */
const dictStore = useDictStore();

/* ------------------ 核心：init ------------------ */
const init = async (data: any) => {
  const empty: Partial<InspectVO> = {
    creditorList: [],
    debtorList: [],
    guarantorList: [],
    paymentList: [],
    creditorFinanceMap: {},
    creditorInvestmentMap: {},
    creditorFinancialAnalysisDesc: {},
    guarantorFinanceMap: {},
    guarantorInvestmentMap: {},
    guarantorFinancialAnalysisDesc: {},
    debtorInvestmentMap: {},
  };

  const res = Object.keys(data).length > 0 ? await getInspectDetailApi(data.id) : {};
  const src = { ...empty, ...res } as InspectVO;

  Object.assign(reportForm, src);
  (Object.keys(MAP_TARGET) as MapKey[]).forEach((key) => resetAssign(MAP_TARGET[key], src[key]));
  gridPerformanceApi.grid.reloadData(src.paymentList);

  creditorActiveKey.value = src.creditorList?.[0]?.companyCode ?? '';
  debtorActiveKey.value = src.debtorList?.[0]?.companyCode ?? '';
  guarantorActiveKey.value = src.guarantorList?.[0]?.companyCode ?? '';
};

const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <!-- 头部信息 -->
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="项目名称">
          {{ reportForm.projectName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item v-show="reportForm.projectType === 'comprehensive'" label="项目用信">
          {{ reportForm.creditApplyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="运营分析报告名称">
          {{ reportForm.reportName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="运营分析报告编号">
          {{ reportForm.reportCode || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="分析年份">
          {{ reportForm.analysisYear || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="分析季度">
          {{
            quarterOptions.find((q) => q.value === reportForm.analysisQuarter)?.label ||
            reportForm.analysisQuarter ||
            '-'
          }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 一、基本情况 -->
      <BasicCaption content="一、基本情况" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="检查人员">
          {{ reportForm.inspectionByName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="检查方式">
          {{ dictStore.formatter(reportForm.inspectionMethod, 'FCT_INSPECTION_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="检查日期">
          {{ formatDate(reportForm.inspectionDate) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="项目类型">
          {{ dictStore.formatter(reportForm.projectType, 'FCT_PROJECT_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="增信措施">
          {{ reportForm.creditEnhancementDesc || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="投放日期">
          {{ formatDate(reportForm.investmentDate) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="投放金额（元）">
          {{ formatMoney(reportForm.investmentAmount) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="业务余额（元）">
          {{ formatMoney(reportForm.businessAmount) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="内含报酬率（XIRR）">
          {{ formatMoney(reportForm.xirrRate) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="保理类型">
          {{ dictStore.formatter(reportForm.factoringType, 'FCT_FACTORING_TYPE') || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="合同开始日期">
          {{ formatDate(reportForm.contractStartDate) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="合同结束日期">
          {{ formatDate(reportForm.contractEndDate) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="债权人">
          {{ reportForm.creditorName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="债务人">
          {{ reportForm.debtorName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="担保人">
          {{ reportForm.guarantorName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="应收账款概况">
          {{ reportForm.receivableDesc || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="风险管理要求落实情况">
          {{ reportForm.riskManagementDesc || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="资金流向">
          {{ reportForm.fundFlowDesc || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="底层项目情况">
          {{ reportForm.underlyingProjectDesc || '-' }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 二、本季度回款履约情况 -->
      <BasicCaption content="二、本季度回款履约情况" />
      <PerformanceTable />

      <!-- 三、债权人投后管理情况 -->
      <BasicCaption content="三、债权人投后管理情况" />
      <a-tabs v-model:active-key="creditorActiveKey" v-if="reportForm.creditorList?.length > 0">
        <a-tab-pane :key="v.companyCode" :tab="v.companyName" v-for="v in reportForm.creditorList">
          <div>
            <ScoreTableDetail v-model="creditorScoreTableDataMap[v.companyCode]" />
            <FinancialReportTable style="padding: 5px" :table-data="creditorFinanceTableDataMap[v.companyCode]" />
          </div>
          <a-descriptions class="mt-4" v-bind="descriptionsProp">
            <a-descriptions-item label="财务分析">
              {{ creditorFinancialAnalysisDesc[v.companyCode] || '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
      </a-tabs>
      <Empty style="margin: 10px" v-else />

      <!-- 四、债务人投后管理情况 -->
      <BasicCaption content="四、债务人投后管理情况" />
      <a-tabs v-model:active-key="debtorActiveKey" v-if="reportForm.debtorList?.length > 0">
        <a-tab-pane :key="v.companyCode" :tab="v.companyName" v-for="v in reportForm.debtorList">
          <div>
            <ScoreTableDetail v-model="debtorScoreTableDataMap[v.companyCode]" />
          </div>
        </a-tab-pane>
      </a-tabs>
      <Empty style="margin: 10px" v-else />

      <!-- 五、担保人投后管理情况 -->
      <BasicCaption content="五、担保人投后管理情况" />
      <a-tabs v-model:active-key="guarantorActiveKey" v-if="reportForm.guarantorList?.length > 0">
        <a-tab-pane :key="v.companyCode" :tab="v.companyName" v-for="v in reportForm.guarantorList">
          <div>
            <ScoreTableDetail v-model="guarantorScoreTableDataMap[v.companyCode]" />
            <FinancialReportTable style="padding: 10px" :table-data="guarantorFinanceTableDataMap[v.companyCode]" />
          </div>
          <a-descriptions class="mt-4" v-bind="descriptionsProp">
            <a-descriptions-item label="财务分析">
              {{ guarantorFinancialAnalysisDesc[v.companyCode] || '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
      </a-tabs>
      <Empty style="margin: 10px" v-else />

      <!-- 六、抵质押物检查情况 -->
      <BasicCaption content="六、抵质押物检查情况" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="存续情况">
          {{ reportForm.continuationDesc || '-' }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 七、检查结果 -->
      <BasicCaption content="七、检查结果" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="其他情况">
          {{ reportForm.otherDesc || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="检查结论">
          {{ reportForm.inspectionConclusionDesc || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="资产分类">
          {{ dictStore.formatter(reportForm.assetClassification, 'FCT_ASSET_CLASSIFY_TYPE') || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="运营人员">
          {{ reportForm.operationByName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="复核人员">
          {{ reportForm.reviewerByName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="报告日期">
          {{ formatDate(reportForm.reportDate) || '-' }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 附件 -->
      <BaseAttachmentList
        v-model="reportForm.attachmentList"
        :business-id="reportForm.id"
        business-type="FCT_OPE_OPERATION_INSPECTION"
      />
    </div>
  </BasicPopup>
</template>

<style scoped></style>
