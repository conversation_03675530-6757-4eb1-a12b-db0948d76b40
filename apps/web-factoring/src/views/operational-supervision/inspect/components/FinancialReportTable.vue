<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import { computed } from 'vue';

import { Table } from 'ant-design-vue';

interface Row extends Record<string, string> {
  key: string;
}

const props = defineProps<{
  tableData: Record<string, string>[];
}>();

/* 计算列配置 */
const antColumns = computed<TableColumnType<Row>[]>(() => {
  if (!props.tableData || props.tableData.length === 0) return [];

  const headerRow = props.tableData[0];
  const keys = Object.keys(headerRow);

  return keys.map((key) => {
    const col: TableColumnType<Row> = {
      title: headerRow[key],
      dataIndex: key,
      key,
      align: 'center', // 单元格内容居中
    };

    // if (idx === 0) {
    //   col.width = 200;
    //   col.fixed = 'left';
    // }

    return col;
  });
});

/* 计算行数据（去掉表头行，并加入唯一 key） */
const antData = computed<Row[]>(() => {
  if (!props.tableData || props.tableData.length <= 1) return [];
  return props.tableData.slice(1).map((row, index) => ({
    ...row,
    key: index.toString(), // ant-design-vue 需要唯一 key
  }));
});
</script>

<template>
  <Table
    v-if="tableData"
    class="center-table w-full"
    row-key="key"
    size="small"
    bordered
    :columns="antColumns"
    :data-source="antData"
    :pagination="false"
    :scroll="{ x: 'max-content' }"
  />
</template>
<style scoped>
/* 单元格内容居中 */
.center-table :deep(.ant-table-tbody > tr > td) {
  text-align: center;
}

/* 表头文字居中 */
.center-table :deep(.ant-table-thead > tr > th) {
  text-align: center;
}
</style>
