<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CheckItem } from './inspect';

import { watch } from 'vue';

import { useVModel } from '@vueuse/core';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { spanMethod } from '../../../limit-admin/components/tableSpanUtils';
import { generateTemplate } from './inspect';

const props = withDefaults(defineProps<{ modelValue?: CheckItem[] }>(), {
  modelValue: () => generateTemplate(),
});

const emit = defineEmits<{ 'update:modelValue': [CheckItem[]] }>();

const dataSource = useVModel(props, 'modelValue', emit);

if (dataSource.value.length === 0) {
  dataSource.value = generateTemplate();
}

watch(dataSource, (newVal) => emit('update:modelValue', newVal), { deep: true });

const gridOptions: VxeTableGridOptions = {
  pagerConfig: { enabled: false },
  data: dataSource.value,
  columns: [
    { field: 'firstContent', title: '检查项目', width: 150 },
    { field: 'secondContent', title: '检查内容', width: 250 },
    { field: 'value', title: '状态', width: 250, formatter: ({ cellValue }) => (cellValue === 1 ? '有' : '无') },
    { field: 'remark', title: '情况描述' },
  ],
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  spanMethod: (params: any) => spanMethod(params, 'firstContent'),
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

defineExpose({
  getTableData: () => {
    return gridApi.grid?.getTableData().fullData || [];
  },
});
</script>

<template>
  <Grid />
</template>

<style scoped>
/* 确保表格有最小高度 */
.vxe-table {
  min-height: 200px;
}
</style>
