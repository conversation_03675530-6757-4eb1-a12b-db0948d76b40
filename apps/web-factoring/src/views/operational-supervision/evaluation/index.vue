<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { EvaluationPageVO, EvaluationVo } from '#/api';

import { nextTick, reactive, ref } from 'vue';

import { useModalUrl } from '@vben/base-ui';
import { ApiComponent, Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Select, Space, TypographyLink } from 'ant-design-vue';
import dayjs from 'dayjs';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addEvaluationApi, addEvaluationFileApi, getEvaluationPageListApi, getProjectListApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import Detail from './detail.vue';

const { WorkflowPreviewModal, startWorkflow, initWorkflow } = useWorkflowBase();

const labelCol = { style: { width: '150px' } };
// 使用组合式函数处理URL参数自动打开弹层
useModalUrl({
  handlers: {
    // 审核弹层
    audit: (params) => {
      const data: EvaluationPageVO = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '项目后评价名称',
    },
    // {
    //   component: 'DateRangePicker',
    //   fieldName: 'businessDate', // 修复字段名：productName -> businessDate
    //   label: '结清日期',
    // },
  ],
  fieldMappingTime: [['businessDate', ['startDate', 'endDate'], 'YYYY-MM-DD hh:mm:ss']],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
  showCollapseButton: false,
  submitOnEnter: true,
});

// 修复表格列显示和数据绑定的bug
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'reportName', title: '项目后评价名称' },
    { field: 'projectName', title: '项目名称' },
    { field: 'settleDate', title: '结清日期' },
    { field: 'projectName', title: '附件名称', slots: { default: 'file' } },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 200,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getEvaluationPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const edit = async (_row: EvaluationPageVO) => {
  const { projectId, reportName, evaluationFileId, settleDate, id } = _row;
  const date = dayjs(settleDate).valueOf().toString();
  Object.assign(addForm, {
    id,
    projectId,
    reportName,
    evaluationFileId,
    settleDate: date,
  });
  modalApi.open();
  await initWorkflow({ formKey: 'fct_project_evaluation', businessKey: addForm.id });
};
const addFormRef = ref();

const addForm = reactive<EvaluationVo>({
  evaluationFileId: undefined,
  projectId: undefined,
  reportName: '',
  settleDate: '',
  id: undefined,
});

const add = async () => {
  Object.assign(addForm, {
    reportName: '',
    evaluationFileId: undefined,
    projectId: undefined,
    settleDate: '',
    id: undefined,
  });
  modalApi.open();
  await initWorkflow({ formKey: 'fct_project_evaluation', businessKey: addForm.id });
};

const audit = (data: any) => {
  openDetailFormPopup(true, data);
};

// 定义验证规则
const rules: Record<string, Rule[]> = {
  settleDate: [{ required: true, message: '请选择结清日期', trigger: 'change' }],
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  evaluationFileId: [{ required: true, message: '请上传项目后评价报告', trigger: 'change' }],
  reportName: [{ required: true, message: '请输入项目后评价名称' }],
};

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await addFormRef.value.validate();
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      addForm.processDefinitionKey = processDefinitionKey;
      addForm.startUserSelectAssignees = startUserSelectAssignees;
      addForm.id
        ? await addEvaluationFileApi(addForm as EvaluationVo)
        : await addEvaluationApi(addForm as EvaluationVo);

      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } catch {}
  },
});
const handlePick = ({ file }) => {
  nextTick(() => {
    addFormRef.value?.clearValidate(['evaluationFileId']);
  });
  addForm.reportName = addForm.reportName === '' ? file.mainName : addForm.reportName;
};
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #file="{ row }">
        <div>
          <BaseFilePickList v-model="row.evaluationFileId" :edit-mode="false" />
        </div>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink v-if="!['REVIEWING'].includes(row.reviewStatus)" @click="edit(row)">
            重新上传
          </TypographyLink>
          <!-- <TypographyLink @click="upload(row)"> 下载 </TypographyLink> -->
          <!-- <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink> -->
        </Space>
      </template>
    </Grid>
    <Detail @register="detailForm" />
    <Modal title="上传项目后评价" class="w-[800px]">
      <Form
        ref="addFormRef"
        :label-col="labelCol"
        :wrapper-col="{ span: 30 }"
        :model="addForm"
        class="mt-5 px-5"
        :rules="rules"
      >
        <Row class="mt-5">
          <Col :span="24">
            <FormItem label="上传项目后评价报告" name="evaluationFileId">
              <BaseFilePickList v-model="addForm.evaluationFileId" @pick="handlePick" />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="关联项目" name="projectId">
              <ApiComponent
                :disabled="addForm.id"
                v-model="addForm.projectId as unknown as string"
                :component="Select"
                :api="getProjectListApi"
                :params="{ status: 'EFFECTIVE', isMeetingCompleted: 1 }"
                label-field="projectName"
                value-field="id"
                model-prop-name="value"
                show-search
                :filter-option="(input: string, option: any) => option.label.includes(input)"
              />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="结清日期" name="settleDate">
              <DatePicker
                :disabled="addForm.id"
                style="width: 100%"
                v-model:value="addForm.settleDate"
                value-format="x"
              />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="项目后评价名称" name="reportName">
              <Input :disabled="addForm.id" v-model:value="addForm.reportName" placeholder="请输入项目后评价名称" />
            </FormItem>
          </Col>
        </Row>
      </Form>
      <template>
        <WorkflowPreviewModal />
      </template>
    </Modal>
  </Page>
</template>

<style></style>
