<script setup lang="ts">
import type { EvaluationVo } from '#/api';

import { reactive, ref } from 'vue';

import { prompt } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { cloneDeep, formatDate } from '@vben/utils';

import { message, Textarea } from 'ant-design-vue';

import { BaseFilePickList } from '#/adapter/base-ui';
import { addEvaluationApi, getEvaluationDetailApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const {
  startWorkflow,
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isProcessInstance,
  isWorkflow,
  isWorkflowLoading,
} = useWorkflowBase();

const pageType = ref('detail');
const ViewButton = useViewButton();
const baseFormInfo = reactive<EvaluationVo>({
  isSubmit: false,
  projectId: undefined,
  id: undefined,
  evaluationFileId: undefined,
  settleDate: undefined,
});
const OperationButton = useOperationButton();
// 添加初始化方法
const init = async (data: EvaluationVo) => {
  pageType.value = data.pageType ?? 'detail';
  if (data && Object.keys(data).length > 0) {
    await initWorkflow({ formKey: 'fct_project_evaluation', businessKey: data.id });
    const res = await getEvaluationDetailApi(data.id as number);
    if (res) {
      Object.assign(baseFormInfo, cloneDeep(res));
    }
  }
};

const state = reactive({
  loading: false,
});

const audit = async (status: number) => {
  if (status === 1) {
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    if (processDefinitionKey) {
      state.loading = true;
      try {
        await addEvaluationApi({
          ...baseFormInfo,
          isPass: status,
          processDefinitionKey,
          startUserSelectAssignees,
        });
        message.success($t('base.resSuccess'));
        emit('ok');
        closePopup();
      } finally {
        state.loading = false;
      }
      return false;
    }
  }
  prompt({
    content: '请输入审核意见：',
    title: '审核意见',
    component: Textarea,
    modelPropName: 'value',
    async beforeClose(scope) {
      if (!scope.isConfirm) return;
      if (!scope.value) {
        message.warning('请输入审核意见');
        return false;
      }
      state.loading = true;
      try {
        await addEvaluationApi({
          ...baseFormInfo,
          isPass: status,
          remark: scope.value,
        });
      } finally {
        state.loading = false;
      }
      return true;
    },
  }).then(() => {
    message.success($t('base.resSuccess'));
    emit('ok');
    closePopup();
  });
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
        <a-space v-else>
          <a-button :loading="state.loading" type="primary" @click="audit(1)">通过</a-button>
          <a-button :loading="state.loading" type="primary" danger @click="audit(0)">驳回</a-button>
        </a-space>
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="上传项目后评价报告">
          <BaseFilePickList :edit-mode="false" v-model="baseFormInfo.evaluationFileId" />
        </a-descriptions-item>
        <a-descriptions-item label="关联项目">
          {{ baseFormInfo.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="结清日期">
          {{ formatDate(baseFormInfo.settleDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="项目后评价名称">
          {{ baseFormInfo.reportName }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
