<script setup lang="ts">
import type { AssetClassificationInfo } from '#/api';

import { computed, ref } from 'vue';

import { ApiComponent, useVbenModal } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';
import { formatDate } from '@vben/utils';

import { message, Select, TypographyText } from 'ant-design-vue';
import dayjs from 'dayjs';
import { defaultsDeep } from 'lodash-es';

import { addAssetApi, getProjectListApi, updateAssetApi, getCreditApplyListApi } from '#/api';

const dictStore = useDictStore();
const modalFormRef = ref();
const modalForm = ref<AssetClassificationInfo>({});

const modalTitle = ref('新增划分/调整分类');
const projectDisabled = ref(false);
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {
    const data = modalApi.getData() ?? {};
    modalTitle.value = data.id ? '编辑划分/调整分类' : '新增划分/调整分类';
    modalForm.value = defaultsDeep(data, {
      latestDate: formatDate(Date.now()),
      latestClassification: 'FCT_CLASS_NORMAL',
    });
    if (data.id) {
      projectDisabled.value = false;
      modalForm.value.latestBasis = data.latestBasis.split(' ');
      modalForm.value.latestAdjustBasis = data.latestAdjustBasis && data.latestAdjustBasis.split(' ');
    } else {
      projectDisabled.value = true;
      modalForm.value.latestDate = dayjs().format('YYYY-MM-DD HH:mm:ss');
    }
  },
  onConfirm: async () => {
    await modalFormRef.value.validate();
    let api = addAssetApi;
    if (modalForm.value.id) {
      api = updateAssetApi;
    }
    const formValue = { ...modalForm.value };
    if (Array.isArray(formValue.latestBasis)) {
      formValue.latestBasis = formValue.latestBasis.join(' ');
    }
    if (Array.isArray(formValue.latestAdjustBasis)) {
      formValue.latestAdjustBasis = formValue.latestAdjustBasis.join(' ');
    }
    try {
      modalApi.lock();
      await api(formValue);
      message.success('保存成功');
      await modalApi.close();
    } finally {
      modalApi.unlock();
    }
  },
  onBeforeClose: () => {
    modalForm.value = {};
    modalFormRef.value.resetFields();
    return true;
  },
});
const modalRules = {
  latestBasis: [{ required: true, message: '请选择划分依据' }],
};
const basisOptions = computed(() => {
  const list = dictStore.getDictList(modalForm.value.latestClassification || 'FCT_CLASS_NORMAL');
  return list.map((item) => item.dictValue);
});
const adjustOptions = computed(() => {
  const list = dictStore.getDictList('ASSET_B_CLASSIFY');
  return list.map((item) => item.dictValue);
});
const selectCompany = (_value: number, data: any) => {
  modalForm.value.projectId = data.id;
  modalForm.value.projectName = data.label;
  modalForm.value.projectType = data.projectType;
  modalForm.value.executorCompanyName = data.executorCompanyName;
  modalForm.value.executorCompanyCode = data.executorCompanyCode;

};
const handleApplyChange = (value: string, data: any) => {
  modalForm.value.projectApplyName = data.label;
};
</script>

<template>
  <Modal :title="modalTitle" class="w-[80vw]" destroy-on-close>
    <a-form ref="modalFormRef" :model="modalForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :colon="false"
      :rules="modalRules">
      <a-form-item label="项目名称" name="projectCode">
        <ApiComponent v-if="projectDisabled" v-model="modalForm.projectCode" :component="Select" :params="{ isClassFlag: '1'  }"
          :api="getProjectListApi" label-field="projectName" value-field="projectCode" model-prop-name="value"
          @change="selectCompany" show-search
          :filter-option="(input: string, option: any) => option.label.includes(input)" />
        <TypographyText v-else>{{ modalForm.projectName }}</TypographyText>
      </a-form-item>
      <a-form-item label="用信名称" v-show="modalForm.projectType === 'comprehensive' || modalForm.projectApplyId">
        <ApiComponent v-if="projectDisabled" v-model="modalForm.projectApplyId as unknown as string" :component="Select"
          :api="getCreditApplyListApi" :params="{isClassFlag: '1', projectId: modalForm.projectId }" show-search
          :filter-option="(input: string, option: any) => option.label.includes(input)"
          label-field="projectCreditApplyName" @change="handleApplyChange" value-field="id" model-prop-name="value" />
        <TypographyText v-else>{{ modalForm.projectApplyName }}</TypographyText>
      </a-form-item>
      <a-form-item label="资产五级分类" name="latestClassification">
        <a-select v-model:value="modalForm.latestClassification"
          :options="dictStore.getDictList('FCT_ASSET_CLASSIFY_TYPE')" />
      </a-form-item>
      <a-form-item label="划分依据" name="latestBasis">
        <a-checkbox-group v-model:value="modalForm.latestBasis" :options="basisOptions" class="custom-checkbox-group" />
      </a-form-item>
      <a-form-item label="调整依据" name="latestAdjustBasis">
        <a-checkbox-group v-model:value="modalForm.latestAdjustBasis" :options="adjustOptions"
          class="custom-checkbox-group" />
      </a-form-item>
      <a-form-item label="调整说明" name="remarks">
        <a-textarea v-model:value="modalForm.remarks" :rows="3" />
      </a-form-item>
      <a-form-item label="划分/调整时间" name="latestDate">
        <a-date-picker v-model:value="modalForm.latestDate" value-format="YYYY-MM-DD HH:mm:ss" class="w-full" />
      </a-form-item>
    </a-form>
  </Modal>
</template>

<style lang="less" scoped>
.custom-checkbox-group {
  display: flex;
  flex-direction: column;
}

.custom-checkbox-group :deep(.ant-checkbox-wrapper) {
  margin-bottom: 13px;
  position: relative;
  margin-left: 20px;
  margin-top: 5px;
}

.custom-checkbox-group :deep(.ant-checkbox + span) {
  white-space: normal;
}

.custom-checkbox-group :deep(.ant-checkbox) {
  position: absolute;
  top: 2.5px;
  left: -15px;
}

.instruction-con {
  color: #aaa;
}
</style>
