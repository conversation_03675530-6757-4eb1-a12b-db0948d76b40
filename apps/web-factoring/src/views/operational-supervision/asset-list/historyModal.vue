<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { formatDate } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getAssetHistoryApi } from '#/api';

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'latestDate', title: '分类/调整日期', formatter: ({ cellValue }) => formatDate(cellValue) },
    { field: 'latestClassification', title: '资产五级分类', formatter: ['formatStatus', 'FCT_ASSET_CLASSIFY_TYPE'] },
    {
      field: 'latestBasis',
      title: '划分依据',
      minWidth: 160,
      showOverflow: false,
    },
    {
      field: 'latestAdjustBasis',
      title: '调整依据',
      minWidth: 160,
      showOverflow: false,
    },
  ],
  ...DETAIL_GRID_OPTIONS,
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  footer: false,
  onOpened: async () => {
    const data = (await modalApi.getData()) ?? {};
    const list = await getAssetHistoryApi({
      id: data.id,
    });
    gridApi.grid.reloadData(list);
  },
});
</script>

<template>
  <Modal title="分类历史记录" class="w-[80vw]">
    <Grid />
  </Modal>
</template>
