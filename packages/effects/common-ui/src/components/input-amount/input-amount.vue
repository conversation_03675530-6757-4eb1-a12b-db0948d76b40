<script setup lang="ts">
import type { Ref } from 'vue';

import { computed, ref, watch } from 'vue';
import { CurrencyDisplay, useCurrencyInput } from 'vue-currency-input';

import { Input, Tooltip } from 'ant-design-vue';
import Nzh from 'nzh';

/**
 * @typedef {object} Props
 * @property {string} [placeholder=''] - 输入框的占位符文本，默认为空字符串。
 * @property {boolean} [showTooltip=true] - 控制是否显示提示框，默认为 `true`。
 * @property {Function} [tooltipFormatter] - 可选的弹出格式化函数，用于格式化提示框中的内容。默认使用 `Nzh.cn.toMoney`将输入值格式化为中文大写货币格式。
 * @property {object} [currencyInputOption={}] - 可选的配置对象，用于传递`vue-currency-input`相关的设置。
 */
/**
 * 组件接收的 props
 *
 * @type {Props}
 */
const props: {
  readonly currencyInputOption: Record<string, any>;
  readonly inputProps: Record<string, any>;
  readonly placeholder: string;
  readonly showTooltip: boolean;
  readonly tooltipFormatter: Function;
} = defineProps({
  placeholder: {
    type: String,
    default: '',
  },
  showTooltip: {
    type: Boolean,
    default: true,
  },
  tooltipFormatter: {
    type: Function,
    default: (value: number) => {
      if (value !== undefined && value !== null) {
        const nzhCn = Nzh.cn;
        return nzhCn.toMoney(value, { outSymbol: false });
      }
    },
  },
  currencyInputOption: {
    type: Object,
    default() {
      return {};
    },
  },
  inputProps: {
    type: Object,
    default() {
      return {};
    },
  },
});
const emit = defineEmits(['focus', 'blur', 'input', 'change']);
const model = defineModel({
  type: [Number, null],
});
const config = Object.assign(
  {
    currency: 'CNY',
    currencyDisplay: CurrencyDisplay.hidden,
    hideCurrencySymbolOnFocus: true,
    hideGroupingSeparatorOnFocus: true,
    hideNegligibleDecimalDigitsOnFocus: true,
    autoDecimalDigits: false,
    useGrouping: true,
    accountingSign: false,
  },
  props.currencyInputOption,
);
const { inputRef, formattedValue, setValue } = useCurrencyInput(config) as {
  formattedValue: Ref<string>;
  inputRef: Ref<HTMLInputElement>;
  setValue: (value: null | number) => void;
};

const tooltipVisible = ref(false);
const formatAmount = computed(() => {
  let amount = null;
  amount = props.tooltipFormatter
    ? props.tooltipFormatter(model.value)
    : model.value;
  return amount;
});

const { stop, pause, resume } = watch(formatAmount, (newValue) => {
  tooltipVisible.value = !!newValue;
});
watch(model, () => {
  setValue(model.value ?? null);
});
if (props.showTooltip) {
  pause();
} else {
  stop();
}
const inputFocus = () => {
  if (props.showTooltip) {
    resume();
    if (formatAmount.value) {
      tooltipVisible.value = true;
    }
  }
  emit('focus');
};
const inputBlur = () => {
  if (props.showTooltip) {
    pause();
    tooltipVisible.value = false;
  }
  emit('blur');
};
</script>

<template>
  <span class="amount-input">
    <Tooltip :open="tooltipVisible" :arrow="false" placement="bottomLeft">
      <template #title>
        <p>{{ formatAmount }}</p>
      </template>
      <Input
        ref="inputRef"
        v-model:value="formattedValue"
        :placeholder="placeholder"
        v-bind="props.inputProps"
        @focus="inputFocus"
        @blur="inputBlur"
      />
    </Tooltip>
  </span>
</template>

<style scoped lang="scss">
.amount-input {
  width: 100%;
}
</style>
