<script setup lang="ts">
import type { NoticeInfo } from '@vben/types';

import { ref } from 'vue';

import { useVbenModal } from '@vben-core/popup-ui';

import { ClockCircleOutlined, UserOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  getNoticeList: { type: Function, required: true },
  getNoticeDetail: { type: Function, required: true },
  noticeTypeList: { type: Array, required: true },
});

const noticeIds = ref<number[]>([]);
const activeIndex = ref(0);
const noticeDetail = ref<NoticeInfo>({});

const init = async () => {
  noticeIds.value = await props.getNoticeList();
  if (noticeIds.value.length > 0) {
    activeIndex.value = 0;
    noticeDetail.value = await props.getNoticeDetail({
      id: noticeIds.value[activeIndex.value],
    });
    const noticeTypeInfo = props.noticeTypeList?.find((item) => item.value === noticeDetail.value.noticeType) ?? {};
    modalApi.setState({ title: noticeTypeInfo.label });
    modalApi.open();
  }
};
init();

const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  showCancelButton: false,
  centered: true,
});

const prev = () => {
  activeIndex.value--;
  loadNoticeDetail();
};

const next = () => {
  activeIndex.value++;
  loadNoticeDetail();
};

const loadNoticeDetail = async () => {
  noticeDetail.value = await props.getNoticeDetail({
    id: noticeIds.value[activeIndex.value],
  });
  const noticeTypeInfo = props.noticeTypeList?.find((item) => item.value === noticeDetail.value.noticeType) ?? {};
  modalApi.setState({ title: noticeTypeInfo.label });
};
</script>

<template>
  <Modal class="w-[1000px]">
    <div>
      <h3 class="text-center text-lg font-bold">{{ noticeDetail.title }}</h3>
      <div class="mt-3 flex flex-wrap items-center justify-center gap-x-6 gap-y-2 text-sm text-gray-400">
        <div class="flex items-center">
          <UserOutlined class="mr-1.5" />
          <span>发布人：{{ noticeDetail.createByName }}</span>
        </div>
        <div class="flex items-center">
          <ClockCircleOutlined class="mr-1.5" />
          <span>发布时间：{{ noticeDetail.createTime }}</span>
        </div>
      </div>
      <div class="mt-4 min-h-[300px] p-4">
        <div v-html="noticeDetail.content"></div>
      </div>
    </div>
    <template #footer>
      <div class="flex w-full items-center px-4 py-2">
        <div>{{ activeIndex + 1 }}/{{ noticeIds.length }}</div>
        <div class="ml-auto">
          <a-button v-if="activeIndex > 0" class="mr-2" @click="prev">上一篇</a-button>
          <a-button v-if="activeIndex < noticeIds.length - 1" @click="next">下一篇</a-button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<style></style>
