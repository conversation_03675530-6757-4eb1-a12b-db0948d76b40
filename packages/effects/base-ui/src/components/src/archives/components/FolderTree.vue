<script lang="ts" setup>
import type { TreeActionType } from '@vben/fe-ui';
import type { Nullable } from '@vben/types';

import { nextTick, reactive, ref, toRefs, unref } from 'vue';

import { DocumentIcons } from '@vben/base-ui';
import { BasicModal, BasicTree, useModalInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';

const props = defineProps({
  api: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['register', 'reload']);

const { getArchiveFolderTreeApi, moveArchiveFileToApi } = props.api;

const { folderImg } = DocumentIcons;

interface State {
  treeData: any[];
  loading: boolean;
  ids: number[];
  toId: number;
  projectId: number;
}

const [registerModal, { changeOkLoading, closeModal }] = useModalInner(init);
const treeRef = ref<Nullable<TreeActionType>>(null);
const state = reactive<State>({
  treeData: [],
  loading: false,
  ids: [],
  toId: 0,
  projectId: 0,
});
const { treeData, loading } = toRefs(state);

function init(data: { ids: number[]; parentId: number; projectId: number }) {
  state.ids = data.ids;
  state.toId = data.parentId;
  state.projectId = data.projectId;
  state.loading = true;
  getArchiveFolderTreeApi({ projectId: state.projectId }).then((res) => {
    state.treeData = res;
    state.loading = false;
    nextTick(() => {
      getTree().setSelectedKeys([state.toId]);
    });
  });
}
function getTree() {
  const tree = unref(treeRef);
  if (!tree) throw new Error('tree is null!');
  return tree;
}
function handleSelect(keys: number[]) {
  if (keys && keys[0]) {
    state.toId = keys[0];
  }
}
function handleSubmit() {
  const toId = state.toId;
  if (!toId) return message.warning('请选择移动目录');
  changeOkLoading(true);
  moveArchiveFileToApi({ toId, ids: state.ids })
    .then(() => {
      message.success('移动成功');
      changeOkLoading(false);
      closeModal();
      emit('reload');
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="移动"
    :width="450"
    @ok="handleSubmit"
    class="fe-tree-modal"
    ok-text="移动到此"
  >
    <div class="h-[350px] overflow-y-auto">
      <BasicTree class="tree-main" :tree-data="treeData" @select="handleSelect" ref="treeRef" :loading="loading">
        <template #title="{ fileName }"> <img :src="folderImg" class="mr-1 h-4 w-4" />{{ fileName }} </template>
      </BasicTree>
    </div>
  </BasicModal>
</template>
<style lang="less">
.fe-tree-modal {
  .ant-modal-body {
    & > .scrollbar {
      padding: 20px !important;
    }
  }
}
</style>
