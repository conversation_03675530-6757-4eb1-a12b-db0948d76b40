import type { BpmCandidateStrategyEnum } from '#/components/src/workflow/constants';

import { BpmNodeTypeEnum } from '#/components/src/workflow/constants';

export namespace BpmModelApi {
  /** 用户信息 TODO 这个是不是可以抽取出来定义在公共模块 */
  // TODO @芋艿：一起看看。
  export interface UserInfo {
    id: number;
    nickname: string;
    avatar?: string;
    deptId?: number;
    deptName?: string;
  }

  /** 流程定义 */
  export interface ProcessDefinition {
    id: string;
    key?: string;
    version: number;
    deploymentTime: number;
    suspensionState: number;
    formType?: number;
    formCustomViewPath?: string;
  }

  /** 流程模型 */
  export interface Model {
    id: number;
    key: string;
    name: string;
    icon?: string;
    description: string;
    category: string;
    formName: string;
    formType: number;
    formId: number;
    formCustomCreatePath: string;
    formCustomViewPath: string;
    processDefinition: ProcessDefinition;
    status: number;
    remark: string;
    createTime: string;
    bpmnXml: string;
    startUsers?: UserInfo[];
  }
}
export namespace BpmTaskApi {
  /** BPM 流程监听器 */
  export interface Task {
    id: number; // 编号
    name: string; // 监听器名字
    type: string; // 监听器类型
    status: number; // 监听器状态
    event: string; // 监听事件
    valueType: string; // 监听器值类型
  }

  // 流程任务
  export interface TaskManager {
    id: string; // 编号
    name: string; // 任务名称
    createTime: number; // 创建时间
    endTime: number; // 结束时间
    durationInMillis: number; // 持续时间
    status: number; // 状态
    reason: string; // 原因
    ownerUser: any; // 负责人
    assigneeUser: any; // 处理人
    taskDefinitionKey: string; // 任务定义key
    processInstanceId: string; // 流程实例id
    // eslint-disable-next-line no-use-before-define
    processInstance: BpmProcessInstanceApi.ProcessInstance; // 流程实例
    parentTaskId: any; // 父任务id
    children: any; // 子任务
    formId: any; // 表单id
    formName: any; // 表单名称
    formConf: any; // 表单配置
    formFields: any; // 表单字段
    formVariables: any; // 表单变量
    buttonsSetting: any; // 按钮设置
    signEnable: any; // 签名设置
    reasonRequire: any; // 原因设置
    nodeType: any; // 节点类型
  }
}
export namespace BpmProcessInstanceApi {
  // TODO @芋艿：一些注释缺少或者不对；
  export interface Task {
    id: number;
    name: string;
  }

  export interface User {
    avatar: string;
    id: number;
    nickname: string;
  }

  // 审批任务信息
  export interface ApprovalTaskInfo {
    assigneeUser: User;
    id: number;
    ownerUser: User;
    reason: string;
    signPicUrl: string;
    status: number;
  }

  // 审批节点信息
  export interface ApprovalNodeInfo {
    candidateStrategy?: BpmCandidateStrategyEnum;
    candidateUsers?: User[];
    endTime?: Date;
    id: number;
    name: string;
    nodeType: BpmNodeTypeEnum;
    startTime?: Date;
    status: number;
    tasks: ApprovalTaskInfo[];
  }

  /** 流程实例 */
  export interface ProcessInstance {
    businessKey: string;
    category: string;
    createTime: string;
    endTime: string;
    fields: string[];
    formVariables: Record<string, any>;
    id: number;
    name: string;
    processDefinition?: BpmModelApi.ProcessDefinition;
    processDefinitionId: string;
    remark: string;
    result: number;
    startTime?: Date;
    startUser?: User;
    status: number;
    tasks?: BpmProcessInstanceApi.Task[];
  }

  // 审批详情
  export interface ApprovalDetail {
    activityNodes: BpmProcessInstanceApi.ApprovalNodeInfo[];
    formFieldsPermission: any;
    processDefinition: BpmModelApi.ProcessDefinition;
    processInstance: BpmProcessInstanceApi.ProcessInstance;
    status: number;
    todoTask: BpmTaskApi.Task;
  }

  // 抄送流程实例
  export interface Copy {
    activityId: string;
    activityName: string;
    createTime: number;
    createUser: User;
    id: number;
    processInstanceId: string;
    processInstanceName: string;
    processInstanceStartTime: number;
    reason: string;
    startUser: User;
    summary: {
      key: string;
      value: string;
    }[];
    taskId: string;
  }
}

export namespace BpmProcessDefinitionApi {
  /** 流程定义 */
  export interface ProcessDefinition {
    id: string;
    version: number;
    name: string;
    description: string;
    deploymentTime: number;
    suspensionState: number;
    modelType: number;
    modelId: string;
    formType?: number;
    bpmnXml?: string;
    simpleModel?: string;
    formFields?: string[];
    icon?: string;
  }
}
