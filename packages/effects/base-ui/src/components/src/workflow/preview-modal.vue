<script setup lang="ts">
import type { BpmProcessInstanceApi } from '#/components/src/workflow/type';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { TabPane, Tabs } from 'ant-design-vue';

import { WorkflowSimpleViewer, WorkflowTimeline } from '#/components';

const activeTab = ref('timeline');
const activityNodes = ref<BpmProcessInstanceApi.ApprovalNodeInfo[]>([]);
const simpleJson = ref<string>('');
let api = {};
const [Modal, modalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<Record<string, any>>();
      activityNodes.value = data.activityNodes;
      simpleJson.value = data.simpleJson;
      api = data.api;
    }
  },
});
const list: { activityId: number; userList: any[] }[] = [];
const handleUserSelectConfirm = (activityId: number, userList: any[]) => {
  list.push({ activityId, userList });
  modalApi.setData(list);
};
defineExpose({ modalApi });
</script>

<template>
  <Modal title="确认流程节点" class="min-h-[400px] w-[1000px]">
    <Tabs v-model:active-key="activeTab">
      <TabPane key="timeline" tab="流程节点">
        <WorkflowTimeline :activity-nodes="activityNodes" :api="api" @select-user-confirm="handleUserSelectConfirm" />
      </TabPane>
      <TabPane key="flow" tab="流程图">
        <div class="h-full w-full">
          <WorkflowSimpleViewer :simple-json="simpleJson" />
        </div>
      </TabPane>
    </Tabs>
  </Modal>
</template>

<style></style>
