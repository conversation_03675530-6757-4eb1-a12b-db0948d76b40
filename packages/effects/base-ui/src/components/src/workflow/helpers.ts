import type { Ref } from 'vue';

import type { BpmNodeTypeEnum } from '#/components/src/workflow/constants';
import type { SimpleFlowNode } from '#/components/src/workflow/consts';

import { inject, nextTick, ref, toRaw, unref, watch } from 'vue';

import { BpmTaskStatusEnum } from '#/components/src/workflow/constants';
import { FieldPermissionType, NODE_DEFAULT_NAME } from '#/components/src/workflow/consts';

/**
 * 解析表单组件的  field, title 等字段（递归，如果组件包含子组件）
 *
 * @param rule  组件的生成规则 https://www.form-create.com/v3/guide/rule
 * @param fields 解析后表单组件字段
 * @param parentTitle  如果是子表单，子表单的标题，默认为空
 */
export const parseFormFields = (
  rule: Record<string, any>,
  fields: Array<Record<string, any>> = [],
  parentTitle: string = '',
) => {
  const { type, field, $required, title: tempTitle, children } = rule;
  if (field && tempTitle) {
    let title = tempTitle;
    if (parentTitle) {
      title = `${parentTitle}.${tempTitle}`;
    }
    let required = false;
    if ($required) {
      required = true;
    }
    fields.push({
      field,
      title,
      type,
      required,
    });
    // TODO 子表单 需要处理子表单字段
    // if (type === 'group' && rule.props?.rule && Array.isArray(rule.props.rule)) {
    //   // 解析子表单的字段
    //   rule.props.rule.forEach((item) => {
    //     parseFields(item, fieldsPermission, title)
    //   })
    // }
  }
  if (children && Array.isArray(children)) {
    children.forEach((rule) => {
      parseFormFields(rule, fields);
    });
  }
};

// 解析 formCreate 所有表单字段, 并返回
function parseFormCreateFields(formFields?: string[]) {
  const result: Array<Record<string, any>> = [];
  if (formFields) {
    formFields.forEach((fieldStr: string) => {
      parseFormFields(JSON.parse(fieldStr), result);
    });
  }
  return result;
}

export function useWatchNode(props: { flowNode: SimpleFlowNode }): Ref<SimpleFlowNode> {
  const node = ref<SimpleFlowNode>(props.flowNode);
  watch(
    () => props.flowNode,
    (newValue) => {
      node.value = newValue;
    },
  );
  return node;
}
export function useNodeName2(node: Ref<SimpleFlowNode>, nodeType: BpmNodeTypeEnum) {
  // 显示节点名称输入框
  const showInput = ref(false);
  // 输入框的引用
  const inputRef = ref<HTMLInputElement | null>(null);

  // 监听 showInput 的变化，当变为 true 时自动聚焦
  watch(showInput, (value) => {
    if (value) {
      nextTick(() => {
        inputRef.value?.focus();
      });
    }
  });

  // 修改节点名称
  function changeNodeName() {
    showInput.value = false;
    node.value.name = node.value.name || (NODE_DEFAULT_NAME.get(nodeType) as string);
    console.warn('node.value.name===>', node.value.name);
  }
  // 点击节点标题进行输入
  function clickTitle() {
    showInput.value = true;
  }
  return {
    showInput,
    inputRef,
    clickTitle,
    changeNodeName,
  };
}
/**
 * @description 根据节点任务状态，获取节点任务状态样式
 */
export function useTaskStatusClass(taskStatus: BpmTaskStatusEnum | undefined): string {
  if (!taskStatus) {
    return '';
  }
  if (taskStatus === BpmTaskStatusEnum.APPROVE) {
    return 'status-pass';
  }
  if (taskStatus === BpmTaskStatusEnum.RUNNING) {
    return 'status-running';
  }
  if (taskStatus === BpmTaskStatusEnum.REJECT) {
    return 'status-reject';
  }
  if (taskStatus === BpmTaskStatusEnum.CANCEL) {
    return 'status-cancel';
  }
  return '';
}
/** 获取条件节点默认的名称 */
export function getDefaultConditionNodeName(index: number, defaultFlow: boolean | undefined): string {
  if (defaultFlow) {
    return '其它情况';
  }
  return `条件${index + 1}`;
}
/** 获取包容分支条件节点默认的名称 */
export function getDefaultInclusiveConditionNodeName(index: number, defaultFlow: boolean | undefined): string {
  if (defaultFlow) {
    return '其它情况';
  }
  return `包容条件${index + 1}`;
}

/**
 * @description 节点名称配置
 */
export function useNodeName(nodeType: BpmNodeTypeEnum) {
  // 节点名称
  const nodeName = ref<string>();
  // 节点名称输入框
  const showInput = ref(false);
  // 输入框的引用
  const inputRef = ref<HTMLInputElement | null>(null);
  // 点击节点名称编辑图标
  function clickIcon() {
    showInput.value = true;
  }
  // 修改节点名称
  function changeNodeName() {
    showInput.value = false;
    nodeName.value = nodeName.value || (NODE_DEFAULT_NAME.get(nodeType) as string);
  }
  // 监听 showInput 的变化，当变为 true 时自动聚焦
  watch(showInput, (value) => {
    if (value) {
      nextTick(() => {
        inputRef.value?.focus();
      });
    }
  });

  return {
    nodeName,
    showInput,
    inputRef,
    clickIcon,
    changeNodeName,
  };
}

/**
 * @description 表单数据权限配置，用于发起人节点 、审批节点、抄送节点
 */
export function useFormFieldsPermission(defaultPermission: FieldPermissionType) {
  // 字段权限配置. 需要有 field, title,  permissioin 属性
  const fieldsPermissionConfig = ref<Array<Record<string, any>>>([]);

  const formType = inject<Ref<number | undefined>>('formType', ref()); // 表单类型

  const formFields = inject<Ref<string[]>>('formFields', ref([])); // 流程表单字段

  function getNodeConfigFormFields(nodeFormFields?: Array<Record<string, string>>) {
    nodeFormFields = toRaw(nodeFormFields);
    fieldsPermissionConfig.value =
      !nodeFormFields || nodeFormFields.length === 0
        ? getDefaultFieldsPermission(unref(formFields))
        : mergeFieldsPermission(nodeFormFields, unref(formFields));
  }
  // 合并已经设置的表单字段权限，当前流程表单字段 (可能新增，或删除了字段)
  function mergeFieldsPermission(formFieldsPermisson: Array<Record<string, string>>, formFields?: string[]) {
    let mergedFieldsPermission: Array<Record<string, any>> = [];
    if (formFields) {
      mergedFieldsPermission = parseFormCreateFields(formFields).map((item) => {
        const found = formFieldsPermisson.find((fieldPermission) => fieldPermission.field === item.field);
        return {
          field: item.field,
          title: item.title,
          permission: found ? found.permission : defaultPermission,
        };
      });
    }
    return mergedFieldsPermission;
  }

  // 默认的表单权限： 获取表单的所有字段，设置字段默认权限为只读
  function getDefaultFieldsPermission(formFields?: string[]) {
    let defaultFieldsPermission: Array<Record<string, any>> = [];
    if (formFields) {
      defaultFieldsPermission = parseFormCreateFields(formFields).map((item) => {
        return {
          field: item.field,
          title: item.title,
          permission: defaultPermission,
        };
      });
    }
    return defaultFieldsPermission;
  }

  // 获取表单的所有字段，作为下拉框选项
  const formFieldOptions = parseFormCreateFields(unref(formFields));

  return {
    formType,
    fieldsPermissionConfig,
    formFieldOptions,
    getNodeConfigFormFields,
  };
}
