<script setup lang="ts">
import { computed } from 'vue';

import { useDictStore } from '@vben/stores';

import { Select } from 'ant-design-vue';

import StatusTag from './status-tag.vue';

const props = defineProps({
  code: {
    type: [String],
    required: true,
  },
});
const value = defineModel('value', { type: [String, null] });
const dictStore = useDictStore();
const options = computed(() => dictStore.getDictList(props.code));
</script>

<template>
  <Select v-model:value="value" option-label-prop="children" v-bind="$attrs">
    <Select.Option v-for="item in options" :key="item.value" :value="item.value">
      <StatusTag :value="item.value" :code="props.code" :enable-tag="false" />
    </Select.Option>
  </Select>
</template>

<style scoped></style>
