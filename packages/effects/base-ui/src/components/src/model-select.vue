<script setup lang="ts">
import type { Key } from 'ant-design-vue/es/_util/type';

import { reactive, ref, watch } from 'vue';

import { multiFieldSearch } from '@vben/utils';

import { FormItemRest, InputSearch, Modal, Select, Table } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

const prop = withDefaults(
  defineProps<{
    columns?: any[];
    disabled?: boolean;
    labelKey?: string;
    multiple?: boolean;
    rowKey?: string;
    searchKeys?: string[];
    searchPlaceholder?: string;
    source?: any[];
    sourceApi?: (params: any) => Promise<any>;
    sourceQuery?: Record<string, any>;
    title?: string;
  }>(),
  {
    multiple: false,
    title: '选择',
    columns: () => [],
    source: () => [],
    sourceApi: undefined,
    sourceQuery: () => ({}),
    rowKey: 'id',
    labelKey: 'name',
    searchKeys: () => [],
    searchPlaceholder: '请输入搜索内容',
    disabled: false,
  },
);
const emit = defineEmits(['change']);
const state = reactive({
  open: false,
  keyword: '',
});
const modelValue = defineModel<number | number[] | string | string[] | undefined>();
const options = ref<{ label: string; value: number }[]>([]);
const dataSource = ref([]);
const searchDataSource = ref([]);
const selectedRowKeys = ref<Key[]>([]);
const selectedRowList = ref<any[]>([]);
const onSelectChange = (keys: Key[], selectedRows: any[]) => {
  if (prop.multiple) {
    selectedRowKeys.value = keys as number[];
    selectedRowList.value = selectedRows;
  } else {
    selectedRowKeys.value = keys.length > 0 ? [keys[keys.length - 1] as number] : [];
    selectedRowList.value = selectedRows.length > 0 ? [selectedRows[selectedRows.length - 1]] : [];
  }
};

const onRowClick = (record: any) => {
  const key = record[prop.rowKey];
  if (prop.multiple) {
    const index = selectedRowKeys.value.indexOf(key);
    if (index === -1) {
      selectedRowKeys.value.push(key);
      selectedRowList.value.push(record);
    } else {
      selectedRowKeys.value.splice(index, 1);
      const recordIndex = selectedRowList.value.findIndex((item) => item[prop.rowKey] === key);
      if (recordIndex !== -1) {
        selectedRowList.value.splice(recordIndex, 1);
      }
    }
  } else {
    selectedRowKeys.value = [key];
    selectedRowList.value = [record];
  }
};
const open = async () => {
  if (prop.disabled) return;
  state.open = true;
  dataSource.value = prop.sourceApi ? await prop.sourceApi(prop.sourceQuery) : prop.source;
  searchDataSource.value = cloneDeep(dataSource.value);

  if (modelValue.value) {
    selectedRowKeys.value = Array.isArray(modelValue.value) ? modelValue.value : [modelValue.value];
    const selectedKeys = selectedRowKeys.value;
    selectedRowList.value = dataSource.value.filter((item: any) => selectedKeys.includes(item[prop.rowKey]));
  } else {
    selectedRowKeys.value = [];
    selectedRowList.value = [];
  }
};
const search = (searchValue: string) => {
  searchDataSource.value = multiFieldSearch(dataSource.value, searchValue, [prop.labelKey, ...prop.searchKeys]);
};
const handleOk = () => {
  const keysToEmit: any[] | Key | undefined = prop.multiple ? [...selectedRowKeys.value] : selectedRowKeys.value[0];
  modelValue.value = keysToEmit;
  const selectedObjects = cloneDeep(selectedRowList.value);
  const selectedObjectOrArray = prop.multiple ? selectedObjects : selectedObjects[0];
  emit('change', keysToEmit, selectedObjectOrArray);

  state.open = false;
};
const close = () => {
  selectedRowKeys.value = [];
  selectedRowList.value = [];
};
watch(
  () => modelValue.value,
  async (val: any) => {
    if (val) {
      let data = [];
      if (Array.isArray(prop.source) && prop.source.length > 0) {
        data = prop.source;
      } else if (Array.isArray(dataSource.value) && dataSource.value.length > 0) {
        data = dataSource.value;
      } else if (prop.sourceApi) {
        data = (await prop.sourceApi(prop.sourceQuery)) || [];
      }
      const selectDataList = Array.isArray(val)
        ? data.filter((item: any) => val.includes(item[prop.rowKey]))
        : data.filter((item: any) => item[prop.rowKey] === val);
      options.value = selectDataList.map((item: any) => ({ label: item[prop.labelKey], value: item[prop.rowKey] }));
    }
  },
  { immediate: true },
);
</script>

<template>
  <div>
    <Select
      v-model:value="modelValue"
      v-bind="$attrs"
      :options="options"
      :mode="multiple ? 'multiple' : undefined"
      :open="false"
      :disabled="disabled"
      @click="open"
    />
    <FormItemRest>
      <Modal v-model:open="state.open" :title="title" width="1000px" @ok="handleOk" @close="close">
        <InputSearch
          v-model:value="state.keyword"
          :placeholder="searchPlaceholder"
          class="mb-4 w-[300px]"
          allow-clear
          @search="search"
        />
        <Table
          :columns="columns"
          :data-source="searchDataSource"
          :row-key="rowKey"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          :custom-row="(record) => ({ onClick: () => onRowClick(record) })"
        />
      </Modal>
    </FormItemRest>
  </div>
</template>

<style scoped></style>
