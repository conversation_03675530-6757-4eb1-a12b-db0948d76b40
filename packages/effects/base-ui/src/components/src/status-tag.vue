<script setup lang="ts">
import type { DictInfo } from '@vben/stores';

import { computed } from 'vue';

import { useDictStore } from '@vben/stores';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Tag, Tooltip } from 'ant-design-vue';
import { isNil } from 'lodash-es';

const props = defineProps({
  value: { type: [String, Number, Array, null], default: '' },
  code: { type: [String], required: true },
  enableTag: { type: Boolean, default: true },
  option: { type: Object, default: null },
  tooltipProps: {
    type: Object,
    default() {
      return {};
    },
  },
  separator: {
    type: String,
    default: ',',
  },
  // 新增：显示模式，'all' 为全部显示, 'collapse' 为折叠模式
  displayMode: {
    type: String,
    default: 'all', // or 'collapse'
    validator: (value: string) => ['all', 'collapse'].includes(value),
  },
  // 新增：在折叠模式下，最多可见的标签数量
  maxVisible: {
    type: Number,
    default: 1,
  },
});

const dictStore = useDictStore();

// 基础计算属性，处理所有原始值
const processedItems = computed(() => {
  if (isNil(props.value) || props.value === '') {
    return [];
  }
  let rawValues: (number | string)[] = [];
  if (Array.isArray(props.value)) {
    rawValues = props.value;
  } else if (typeof props.value === 'string') {
    rawValues = props.value.split(props.separator);
  } else {
    rawValues = [props.value];
  }
  return rawValues
    .map((val) => {
      if (isNil(val) || val === '') {
        return null;
      }
      const dictInfo = dictStore.dictItemInfo(val, props.code) ?? ({} as DictInfo);
      const label = dictStore.formatter(val, props.code, props.option);
      return {
        label,
        value: val,
        color: dictInfo.dictColor || 'default',
        icon: dictInfo.dictIcon,
        isTag: props.enableTag && dictInfo.isTag === 1,
      };
    })
    .filter(Boolean);
});

// 计算可见的项
const visibleItems = computed(() => {
  if (props.displayMode !== 'collapse' || processedItems.value.length <= props.maxVisible) {
    return processedItems.value;
  }
  return processedItems.value.slice(0, props.maxVisible);
});

// 计算被隐藏的项
const hiddenItems = computed(() => {
  if (props.displayMode !== 'collapse' || processedItems.value.length <= props.maxVisible) {
    return [];
  }
  return processedItems.value.slice(props.maxVisible);
});

// 判断折叠模式是否激活
const isCollapsedModeActive = computed(() => {
  return props.displayMode === 'collapse' && hiddenItems.value.length > 0;
});
</script>

<template>
  <div v-if="processedItems.length > 0">
    <Tooltip v-if="!isCollapsedModeActive" v-bind="props.tooltipProps">
      <div class="flex flex-wrap items-start gap-1">
        <template v-for="item in processedItems" :key="item.value">
          <Tag v-if="item.isTag" :color="item.color">
            <div class="flex items-center">
              <VbenIcon v-if="item.icon" :icon="item.icon" class="mr-2 w-4" />
              <span>{{ item.label }}</span>
            </div>
          </Tag>
          <div v-else class="flex items-center">
            <VbenIcon v-if="item.icon" :icon="item.icon" class="mr-2 w-4" />
            <span>{{ item.label }}</span>
          </div>
        </template>
      </div>
    </Tooltip>

    <div v-else class="flex flex-wrap items-start gap-1">
      <template v-for="item in visibleItems" :key="item.value">
        <Tag v-if="item.isTag" :color="item.color">
          <div class="flex items-center">
            <VbenIcon v-if="item.icon" :icon="item.icon" class="mr-2 w-4" />
            <span>{{ item.label }}</span>
          </div>
        </Tag>
        <div v-else class="flex items-center">
          <VbenIcon v-if="item.icon" :icon="item.icon" class="mr-2 w-4" />
          <span>{{ item.label }}</span>
        </div>
      </template>

      <Tooltip overlay-class-name="status-tag-tooltip">
        <template #title>
          <div class="flex flex-wrap gap-1">
            <template v-for="hiddenItem in hiddenItems" :key="hiddenItem.value">
              <Tag v-if="hiddenItem.isTag" :color="hiddenItem.color">
                <div class="flex items-center">
                  <VbenIcon v-if="hiddenItem.icon" :icon="hiddenItem.icon" class="mr-2 w-4" />
                  <span>{{ hiddenItem.label }}</span>
                </div>
              </Tag>
              <div v-else class="flex items-center">
                <VbenIcon v-if="hiddenItem.icon" :icon="hiddenItem.icon" class="mr-2 w-4" />
                <span>{{ hiddenItem.label }}</span>
              </div>
            </template>
          </div>
        </template>
        <Tag color="blue">+{{ hiddenItems.length }}</Tag>
      </Tooltip>
    </div>
  </div>
</template>

<style>
.status-tag-tooltip .ant-tooltip-inner {
  background-color: hsl(var(--background));
}

.status-tag-tooltip .ant-tooltip-arrow::before {
  background-color: hsl(var(--background));
}
</style>
