@namespace: fe;
@text-color-base: hsl(var(--foreground));
@text-color-secondary: rgba(0, 0, 0, 0.45);
@primary-color: hsl(var(--primary));
@border-color-base: hsl(var(--border));
@component-background: hsl(var(--background));
@modal-mask-bg: #d9d9d9;
@error-color: hsl(#ff3860);
@selected-hover-bg: hsl(#f5f5f5);
@tree-node-selected-bg: hsl(#f5f5f5);
@item-hover-bg: hsl(#f5f5f5);
@iconify-bg-color: transparent;
@border-color-base1: hsl(var(--border));
@text-color-help-dark: hsl(var(--foreground));
@warning-color: hsl(var(--foreground));
@success-color: hsl(var(--foreground));
@app-content-background: hsl(var(--background));
@text-color-label: hsl(var(--foreground));
@disabled-color: hsl(var(--foreground));
@preview-comp-z-index: 201;
@white: #fff;
