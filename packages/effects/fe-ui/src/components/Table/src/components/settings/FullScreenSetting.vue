<script lang="ts">
import { defineComponent } from 'vue';

import { $t as t } from '@vben/locales';

import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons-vue';
import { useFullscreen } from '@vueuse/core';
import { Tooltip } from 'ant-design-vue';

import { useTableContext } from '../../hooks/useTableContext';

export default defineComponent({
  name: 'FullScreenSetting',
  components: {
    FullscreenExitOutlined,
    FullscreenOutlined,
    Tooltip,
  },

  setup() {
    const table = useTableContext();
    const { toggle, isFullscreen } = useFullscreen(table.wrapRef);

    return {
      toggle,
      isFullscreen,
      t,
    };
  },
});
</script>
<template>
  <Tooltip placement="top">
    <template #title>
      <span>{{ t('component.table.settingFullScreen') }}</span>
    </template>
    <FullscreenOutlined @click="toggle" v-if="!isFullscreen" />
    <FullscreenExitOutlined @click="toggle" v-else />
  </Tooltip>
</template>
