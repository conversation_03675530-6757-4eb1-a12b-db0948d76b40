<script lang="ts">
import { computed, defineComponent } from 'vue';

import { $t as t } from '@vben/locales';

import { DoubleLeftOutlined } from '@ant-design/icons-vue';
import { Tooltip } from 'ant-design-vue';

import { useTableContext } from '../../hooks/useTableContext';

export default defineComponent({
  name: 'ExpandSetting',
  components: {
    DoubleLeftOutlined,
    Tooltip,
  },
  setup() {
    const table = useTableContext();

    const isExpanded = computed(() => {
      return table.getIsExpanded();
    });

    function expandAll() {
      table.expandAll();
    }
    function collapseAll() {
      table.collapseAll();
    }

    return { expandAll, collapseAll, t, isExpanded };
  },
});
</script>
<template>
  <Tooltip placement="top">
    <template #title>
      <span>{{ isExpanded ? t('common.collapseAll') : t('common.expandAll') }}</span>
    </template>
    <DoubleLeftOutlined class="icon-collapse" @click="collapseAll" v-if="isExpanded" />
    <DoubleLeftOutlined class="icon-expand" @click="expandAll" v-else />
  </Tooltip>
</template>
<style lang="less" scoped>
.icon-collapse {
  transform: rotate(90deg);
}
.icon-expand {
  transform: rotate(270deg);
}
</style>
