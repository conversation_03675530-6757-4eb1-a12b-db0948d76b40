<script lang="ts">
import { defineComponent } from 'vue';

import { RedoOutlined } from '@ant-design/icons-vue';
import { $t as t } from '@vben/locales';
import { Tooltip } from 'ant-design-vue';

import { useTableContext } from '../../hooks/useTableContext';

export default defineComponent({
  name: 'RedoSetting',
  components: {
    RedoOutlined,
    Tooltip,
  },
  setup() {
    const table = useTableContext();

    function redo() {
      table.clearSelectedRowKeys();
      table.reload();
    }

    return { redo, t };
  },
});
</script>
<template>
  <Tooltip placement="top">
    <template #title>
      <span>{{ t('common.redo') }}</span>
    </template>
    <RedoOutlined @click="redo" />
  </Tooltip>
</template>
