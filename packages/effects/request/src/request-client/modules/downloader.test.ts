import type { AxiosRequestConfig } from 'axios';

import { beforeEach, describe, expect, it, vi } from 'vitest';

import { FileDownloader } from './downloader';

describe('fileDownloader', () => {
  let fileDownloader: FileDownloader;
  const mockAxiosInstance = {
    get: vi.fn(),
  } as any;

  beforeEach(() => {
    fileDownloader = new FileDownloader(mockAxiosInstance);
  });

  it('should create an instance of FileDownloader', () => {
    expect(fileDownloader).toBeInstanceOf(FileDownloader);
  });

  it('should download a file and return a Blob', async () => {
    const url = 'https://example.com/file';
    const mockBlob = new Blob(['file content'], { type: 'text/plain' });
    const mockResponse: Blob = mockBlob;

    mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

    const result = await fileDownloader.download(url);

    expect(result).toBeInstanceOf(Blob);
    expect(result).toEqual(mockBlob);
    expect(mockAxiosInstance.get).toHaveBeenCalledWith(url, {
      responseType: 'blob',
      responseReturn: 'body',
    });
  });

  it('should merge provided config with default config', async () => {
    const url = 'https://example.com/file';
    const mockBlob = new Blob(['file content'], { type: 'text/plain' });
    const mockResponse: Blob = mockBlob;

    mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

    const customConfig: AxiosRequestConfig = {
      headers: { 'Custom-Header': 'value' },
    };

    const result = await fileDownloader.download(url, customConfig);
    expect(result).toBeInstanceOf(Blob);
    expect(result).toEqual(mockBlob);
    expect(mockAxiosInstance.get).toHaveBeenCalledWith(url, {
      ...customConfig,
      responseType: 'blob',
      responseReturn: 'body',
    });
  });

  it('should handle errors gracefully', async () => {
    const url = 'https://example.com/file';
    mockAxiosInstance.get.mockRejectedValueOnce(new Error('Network Error'));
    await expect(fileDownloader.download(url)).rejects.toThrow('Network Error');
  });

  it('should handle empty URL gracefully', async () => {
    const url = '';
    mockAxiosInstance.get.mockRejectedValueOnce(new Error('Request failed with status code 404'));

    await expect(fileDownloader.download(url)).rejects.toThrow('Request failed with status code 404');
  });

  it('should handle null URL gracefully', async () => {
    const url = null as unknown as string;
    mockAxiosInstance.get.mockRejectedValueOnce(new Error('Request failed with status code 404'));

    await expect(fileDownloader.download(url)).rejects.toThrow('Request failed with status code 404');
  });

  describe('downloadWithFileName', () => {
    it('should download file and extract filename from Content-Disposition', async () => {
      const mockBlob = new Blob(['test content'], { type: 'text/plain' });
      const mockResponse = {
        data: mockBlob,
        headers: {
          'content-disposition': 'attachment; filename="test.txt"',
        },
        config: {
          url: 'https://example.com/download',
        },
      };

      mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

      const result = await fileDownloader.downloadWithFileName('https://example.com/download');

      expect(result.blob).toBe(mockBlob);
      expect(result.fileName).toBe('test.txt');
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        'https://example.com/download',
        expect.objectContaining({
          responseReturn: 'raw',
          responseType: 'blob',
        }),
      );
    });

    it('should use fallback filename when Content-Disposition is not available', async () => {
      const mockBlob = new Blob(['test content'], { type: 'text/plain' });
      const mockResponse = {
        data: mockBlob,
        headers: {},
        config: {
          url: 'https://example.com/download',
        },
      };

      mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

      const result = await fileDownloader.downloadWithFileName('https://example.com/download', {
        fallbackFileName: 'fallback.txt',
      });

      expect(result.blob).toBe(mockBlob);
      expect(result.fileName).toBe('fallback.txt');
    });

    it('should extract filename from URL when no other options available', async () => {
      const mockBlob = new Blob(['test content'], { type: 'text/plain' });
      const mockResponse = {
        data: mockBlob,
        headers: {},
        config: {
          url: 'https://example.com/files/document.pdf',
        },
      };

      mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

      const result = await fileDownloader.downloadWithFileName('https://example.com/files/document.pdf');

      expect(result.blob).toBe(mockBlob);
      expect(result.fileName).toBe('document.pdf');
    });

    it('should use default filename when all else fails', async () => {
      const mockBlob = new Blob(['test content'], { type: 'text/plain' });
      const mockResponse = {
        data: mockBlob,
        headers: {},
        config: {
          url: 'https://example.com/api/download',
        },
      };

      mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

      const result = await fileDownloader.downloadWithFileName('https://example.com/api/download');

      expect(result.blob).toBe(mockBlob);
      expect(result.fileName).toBe('downloaded_file');
    });

    it('should decode URL-encoded filename from Content-Disposition', async () => {
      const mockBlob = new Blob(['test content'], { type: 'text/plain' });
      const mockResponse = {
        data: mockBlob,
        headers: {
          'content-disposition': 'attachment;filename=%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
        },
        config: {
          url: 'https://example.com/download',
        },
      };

      mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

      const result = await fileDownloader.downloadWithFileName('https://example.com/download');

      expect(result.blob).toBe(mockBlob);
      expect(result.fileName).toBe('商品导入模板.xlsx');
    });

    it('should decode URL-encoded filename from quoted Content-Disposition', async () => {
      const mockBlob = new Blob(['test content'], { type: 'text/plain' });
      const mockResponse = {
        data: mockBlob,
        headers: {
          'content-disposition': 'attachment; filename="%E6%96%87%E4%BB%B6%E5%90%8D.pdf"',
        },
        config: {
          url: 'https://example.com/download',
        },
      };

      mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

      const result = await fileDownloader.downloadWithFileName('https://example.com/download');

      expect(result.blob).toBe(mockBlob);
      expect(result.fileName).toBe('文件名.pdf');
    });

    it('should handle RFC 5987 encoded filename', async () => {
      const mockBlob = new Blob(['test content'], { type: 'text/plain' });
      const mockResponse = {
        data: mockBlob,
        headers: {
          'content-disposition': "attachment; filename*=UTF-8''%E6%96%87%E4%BB%B6%E5%90%8D.pdf",
        },
        config: {
          url: 'https://example.com/download',
        },
      };

      mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

      const result = await fileDownloader.downloadWithFileName('https://example.com/download');

      expect(result.blob).toBe(mockBlob);
      expect(result.fileName).toBe('文件名.pdf');
    });

    it('should handle malformed URL encoding gracefully', async () => {
      const mockBlob = new Blob(['test content'], { type: 'text/plain' });
      const mockResponse = {
        data: mockBlob,
        headers: {
          'content-disposition': 'attachment; filename="malformed%E5%95%86%E5%93%81%"',
        },
        config: {
          url: 'https://example.com/download',
        },
      };

      mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

      const result = await fileDownloader.downloadWithFileName('https://example.com/download');

      expect(result.blob).toBe(mockBlob);
      // 如果解码失败，应该返回原始文件名
      expect(result.fileName).toBe('malformed%E5%95%86%E5%93%81%');
    });
  });
});
