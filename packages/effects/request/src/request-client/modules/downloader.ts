import type { RequestClient } from '../request-client';
import type { RequestClientConfig, RequestResponse } from '../types';

type DownloadRequestConfig = {
  /**
   * 定义期望获得的数据类型。
   * raw: 原始的AxiosResponse，包括headers、status等。
   * body: 只返回响应数据的BODY部分(Blob)
   */
  responseReturn?: 'body' | 'raw';
} & Omit<RequestClientConfig, 'responseReturn'>;

/**
 * 从 Content-Disposition 响应头中解析文件名
 * @param contentDisposition Content-Disposition 头的值
 * @returns 解析出的文件名，如果解析失败则返回 null
 */
function parseFileNameFromContentDisposition(contentDisposition: string): null | string {
  if (!contentDisposition) {
    return null;
  }

  // 匹配 filename*=UTF-8''encoded-filename 格式 (RFC 5987)
  const rfc5987Match = contentDisposition.match(/filename\*=UTF-8''([^;]+)/i);
  if (rfc5987Match && rfc5987Match[1] !== undefined) {
    try {
      return decodeURIComponent(rfc5987Match[1]);
    } catch {
      // 如果解码失败，继续尝试其他格式
    }
  }

  // 匹配 filename="quoted-filename" 格式
  const quotedMatch = contentDisposition.match(/filename="([^"]+)"/i);
  if (quotedMatch && quotedMatch[1] !== undefined) {
    const fileName = quotedMatch[1];
    // 尝试解码 URL 编码的文件名
    try {
      return decodeURIComponent(fileName);
    } catch {
      // 如果解码失败，返回原始文件名
      return fileName;
    }
  }

  // 匹配 filename=unquoted-filename 格式（包括 URL 编码的情况）
  const unquotedMatch = contentDisposition.match(/filename=([^;]+)/i);
  if (unquotedMatch && unquotedMatch[1] !== undefined) {
    const fileName = unquotedMatch[1].trim();
    // 尝试解码 URL 编码的文件名
    try {
      return decodeURIComponent(fileName);
    } catch {
      // 如果解码失败，返回原始文件名
      return fileName;
    }
  }

  return null;
}

/**
 * 从响应中提取文件名
 * @param response HTTP 响应对象
 * @param fallbackFileName 备用文件名
 * @returns 提取的文件名
 */
function extractFileNameFromResponse(response: RequestResponse<Blob>, fallbackFileName?: string): string {
  // 优先从 Content-Disposition 头中解析文件名
  const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition'];
  if (contentDisposition) {
    const fileName = parseFileNameFromContentDisposition(contentDisposition);
    if (fileName) {
      return fileName;
    }
  }

  // 如果有备用文件名，使用备用文件名
  if (fallbackFileName) {
    return fallbackFileName;
  }

  // 最后尝试从 URL 中提取文件名
  const url = response.config.url || '';
  const urlFileName = url.split('/').pop()?.split('?')[0];
  if (urlFileName && urlFileName.includes('.')) {
    return urlFileName;
  }

  // 如果都失败了，返回默认文件名
  return 'downloaded_file';
}

class FileDownloader {
  private client: RequestClient;

  constructor(client: RequestClient) {
    this.client = client;
  }

  /**
   * 下载文件
   * @param url 文件的完整链接
   * @param config 配置信息，可选。
   * @returns 如果config.responseReturn为'body'，则返回Blob(默认)，否则返回RequestResponse<Blob>
   */
  public async download<T = Blob>(url: string, config?: DownloadRequestConfig): Promise<T> {
    const finalConfig: DownloadRequestConfig = {
      responseReturn: 'body',
      ...config,
      responseType: 'blob',
      // 文件下载不限制超时时间
      timeout: 0,
    };

    const response = await this.client.get<T>(url, finalConfig);

    return response;
  }

  /**
   * 下载文件并直接触发浏览器下载
   * @param url 文件的完整链接
   * @param options 下载选项
   */
  public async downloadAndSave(
    url: string,
    options?: {
      /** 其他请求配置 */
      config?: Omit<DownloadRequestConfig, 'responseReturn'>;
      /** 备用文件名，当无法从响应头解析时使用 */
      fallbackFileName?: string;
    },
  ): Promise<void> {
    const { blob, fileName } = await this.downloadWithFileName(url, options);

    // 创建下载链接并触发下载
    const downloadUrl = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    link.style.display = 'none';

    document.body.append(link);
    link.click();
    link.remove();

    // 清理临时 URL
    setTimeout(() => URL.revokeObjectURL(downloadUrl), 100);
  }

  /**
   * 下载文件并自动解析文件名
   * @param url 文件的完整链接
   * @param options 下载选项
   * @returns 包含 blob 数据和解析出的文件名的对象
   */
  public async downloadWithFileName(
    url: string,
    options?: {
      /** 其他请求配置 */
      config?: Omit<DownloadRequestConfig, 'responseReturn'>;
      /** 备用文件名，当无法从响应头解析时使用 */
      fallbackFileName?: string;
    },
  ): Promise<{ blob: Blob; fileName: string }> {
    const finalConfig: DownloadRequestConfig = {
      responseReturn: 'raw',
      ...options?.config,
      responseType: 'blob',
      // 文件下载不限制超时时间
      timeout: 0,
    };

    const response = await this.client.get<RequestResponse<Blob>>(url, finalConfig);

    const fileName = extractFileNameFromResponse(response, options?.fallbackFileName);

    return {
      blob: response.data,
      fileName,
    };
  }
}

export { FileDownloader };
