import type { MenuRecordRaw } from '@vben-core/typings';

function findMenuByPath(list: MenuRecordRaw[], path?: string): MenuRecordRaw | null {
  for (const menu of list) {
    if (menu.path === path) {
      return menu;
    }
    const findMenu = menu.children && findMenuByPath(menu.children, path);
    if (findMenu) {
      return findMenu;
    }
  }
  return null;
}

/**
 * 查找根菜单
 * @param menus
 * @param path
 */
function findRootMenuByPath(menus: MenuRecordRaw[], path?: string, level = 0) {
  const findMenu = findMenuByPath(menus, path);
  const rootMenuPath = findMenu?.parents?.[level];
  const rootMenu = rootMenuPath ? menus.find((item) => item.path === rootMenuPath) : undefined;
  return {
    findMenu,
    rootMenu,
    rootMenuPath,
  };
}

/**
 * 根据给定的完整URL查找最匹配的数据对象（支持基础路径）
 * @param fullUrl - 要查询的完整URL
 * @param data - JSON数据源
 * @returns - 匹配到的对象或 undefined
 */
function findDataByUrl(fullUrl: string, data: any[]) {
  let bestMatch;

  for (const item of data) {
    const baseUrl = item.url;

    // 检查 fullUrl 是否以 baseUrl 开头
    if (fullUrl.startsWith(baseUrl)) {
      // 确保是完整的路径匹配，而不是部分匹配
      const isExactMatch = fullUrl.length === baseUrl.length;
      // 如果 baseUrl 不以 / 结尾，那么 fullUrl 的下一个字符必须是 /
      const isSubPath = !baseUrl.endsWith('/') && fullUrl[baseUrl.length] === '/';
      // 如果 baseUrl 以 / 结尾，那么它本身就是一个合法的子路径前缀
      const isSubPathWithSlash = baseUrl.endsWith('/');

      if ((isExactMatch || isSubPath || isSubPathWithSlash) && (!bestMatch || baseUrl.length > bestMatch.url.length)) {
        bestMatch = item;
      }
    }
  }

  return bestMatch;
}

export { findDataByUrl, findMenuByPath, findRootMenuByPath };
