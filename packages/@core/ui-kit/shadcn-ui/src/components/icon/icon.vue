<script setup lang="ts">
import type { Component } from 'vue';

import { computed } from 'vue';

import { IconDefault, IconifyIcon } from '@vben-core/icons';
import {
  isFunction,
  isHttpUrl,
  isObject,
  isString,
} from '@vben-core/shared/utils';

const props = defineProps<{
  // 没有是否显示默认图标
  fallback?: boolean;
  icon?: Component | Function | string;
}>();

const isRemoteIcon = computed(() => {
  return isString(props.icon) && isHttpUrl(props.icon);
});

const isComponent = computed(() => {
  const { icon } = props;
  return !isString(icon) && (isObject(icon) || isFunction(icon));
});

const isSvgIcon = computed(() => {
  if (isString(props.icon)) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(props.icon, 'image/svg+xml');
      return doc.documentElement.nodeName === 'svg';
    } catch (e) {
      return false;
    }
  }
  return false;
});
</script>

<template>
  <component :is="icon as Component" v-if="isComponent" v-bind="$attrs" />
  <img v-else-if="isRemoteIcon" :src="icon as string" v-bind="$attrs" />
  <div v-else-if="isSvgIcon" v-bind="$attrs" v-html="icon" class="svg-container"></div>
  <IconifyIcon v-else-if="icon" v-bind="$attrs" :icon="icon as string" />
  <IconDefault v-else-if="fallback" v-bind="$attrs" />
</template>
<style>
.svg-container {
  display: inline-block;
}
.svg-container svg {
  width: 100%;
  height: auto;
}
</style>
