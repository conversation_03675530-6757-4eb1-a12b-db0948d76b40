<script setup lang="ts">
import { computed } from 'vue';

import { cn } from '@vben-core/shared/utils';

import { useVModel } from '@vueuse/core';

const props = defineProps<{
  addonAfter?: (() => any) | string;
  class?: any;
  defaultValue?: number | string;
  modelValue?: number | string;
}>();

const emits = defineEmits<{
  (e: 'update:modelValue', payload: number | string): void;
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  defaultValue: props.defaultValue,
  passive: true,
});

const hasAddonAfter = computed(() => !!props.addonAfter);
</script>

<template>
  <div v-if="hasAddonAfter" class="flex w-full border" :class="props.class">
    <input
      v-model="modelValue"
      v-bind="$attrs"
      :class="
        cn(
          'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md rounded-r-none px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
          props.class,
        )
      "
    />
    <div class="border-input bg-background flex items-center rounded-r-md">
      <component :is="props.addonAfter" v-if="typeof props.addonAfter === 'function'" />
      <span v-else-if="typeof props.addonAfter === 'string'">{{ props.addonAfter }}</span>
    </div>
  </div>
  <input
    v-else
    v-model="modelValue"
    v-bind="$attrs"
    :class="
      cn(
        'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
        props.class,
      )
    "
  />
</template>
<style lang="scss" scoped>
input {
  --ring: var(--primary);
}
</style>
