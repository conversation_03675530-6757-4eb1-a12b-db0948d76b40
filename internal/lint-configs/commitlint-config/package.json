{"name": "@vben/commitlint-config", "version": "5.5.7", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/lint-configs/commitlint-config"}, "license": "MIT", "type": "module", "files": ["dist"], "main": "./index.mjs", "module": "./index.mjs", "exports": {".": {"import": "./index.mjs", "default": "./index.mjs"}}, "dependencies": {"@commitlint/cli": "catalog:", "@commitlint/config-conventional": "catalog:", "@vben/node-utils": "workspace:*", "commitlint-plugin-function-rules": "catalog:", "cz-git": "catalog:", "czg": "catalog:"}}